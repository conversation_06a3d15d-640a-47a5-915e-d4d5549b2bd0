import{aA as o,j as i}from"./index-C21OP4ex.js";import{C as m}from"./index-BxS2f4Cr.js";import"./loading-spinner-BsXygrn9.js";import"./skeleton-xUyFo20h.js";import"./search-context-DtMZc3QX.js";import"./command-BnLmWlRk.js";import"./calendar-BiBi2kQF.js";import"./createLucideIcon-CL0CQOA1.js";import"./index-Ct3V_iCU.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-DXjwjGKV.js";import"./search-DHRhj6_i.js";import"./createReactComponent-zh6rKAzG.js";import"./pos-api-D5WM5mnz.js";import"./scroll-area-DKiYF9x5.js";import"./index-Bh-UeytL.js";import"./select-B8Pw9rS-.js";import"./index-DuT2Ibxp.js";import"./check-DcHT8QEO.js";import"./IconChevronRight-Bwbz4HuV.js";import"./date-range-picker-B1pgj5D_.js";import"./chevron-right-BAjIoZMb.js";import"./react-icons.esm-DB-kGUq7.js";import"./popover-BtedB187.js";import"./form-usWdQ_Nt.js";import"./vietqr-api-ruJT0-tj.js";import"./use-items-zU7JIOkv.js";import"./useQuery-BNGphiae.js";import"./utils-km2FGkQ4.js";import"./item-api-C8PXkgMG.js";import"./query-keys-3lmd-xp6.js";import"./use-customization-by-id-CGF7bhQG.js";import"./use-customizations-ZELiZ0he.js";import"./useMutation-Bh5DVQPI.js";import"./user-CgNoQQSj.js";import"./crm-api-BUMUQ8t4.js";import"./modal-CFi1vCFt.js";import"./input-4sMIt001.js";import"./table-BIu4Pah2.js";import"./useCanGoBack-CxRJAjVz.js";import"./use-update-customization-BDpqS1Dp.js";import"./checkbox-DUpnJ1Rx.js";import"./collapsible-B7-SIusD.js";import"./IconX-DBkeZmO_.js";import"./use-pos-data-CaoEX2fM.js";import"./use-auth-DBc6rGI6.js";const X=function(){const t=o({from:"/_authenticated/menu/customization/customization-in-city/detail/$customizationId"});return i.jsx(m,{customizationId:t.customizationId})};export{X as component};
