import{aA as o,j as i}from"./index-Bnt3OGV2.js";import{C as m}from"./index-BRYNDS-a.js";import"./loading-spinner-DWU48Qkm.js";import"./skeleton-C-doKLMW.js";import"./search-context-DLufo9i0.js";import"./command-ByfqjQDn.js";import"./calendar-CzR6WBaB.js";import"./createLucideIcon-CNa_hh6B.js";import"./index-BT7Z3RDV.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-hQ-PVOWr.js";import"./search-BVQVKwPC.js";import"./createReactComponent-BD5R5KSl.js";import"./pos-api-BwpRFGce.js";import"./scroll-area-BeVbW7LP.js";import"./index-Bl1CGAiZ.js";import"./select-Czd7KcZQ.js";import"./index-C2T2k_Lh.js";import"./check-apx2eTVC.js";import"./IconChevronRight-BM-o6vT_.js";import"./date-range-picker-CVvofQC0.js";import"./chevron-right-sZt3EK3r.js";import"./react-icons.esm-B7rNr9e-.js";import"./popover-C2mvzdeD.js";import"./form-wT1R35uI.js";import"./vietqr-api-DAENYiJ_.js";import"./use-items-DpA4YqSE.js";import"./useQuery-DSrD7NAp.js";import"./utils-km2FGkQ4.js";import"./item-api-aC51KELB.js";import"./query-keys-3lmd-xp6.js";import"./use-customization-by-id-BDmYKkLo.js";import"./use-customizations-DcL8Qx9h.js";import"./useMutation-d67-fNFq.js";import"./user-oq7iQk7S.js";import"./crm-api-Dd9UhSCJ.js";import"./modal-B0J8RkN-.js";import"./input-CiKEYbig.js";import"./table-C602nYEy.js";import"./useCanGoBack-Bk_8ms-O.js";import"./use-update-customization-BlSaH8mg.js";import"./checkbox-BiVztVsP.js";import"./collapsible-CfXqqCTe.js";import"./IconX-BBHP1ibQ.js";import"./use-pos-data-BCiRRpbt.js";import"./use-auth-Bxzq8gtF.js";const X=function(){const t=o({from:"/_authenticated/menu/customization/customization-in-city/detail/$customizationId"});return i.jsx(m,{customizationId:t.customizationId})};export{X as component};
