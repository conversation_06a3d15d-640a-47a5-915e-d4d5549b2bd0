import{b7 as t,j as r}from"./index-C21OP4ex.js";import{C as m}from"./index-AeaVYeBm.js";import"./error-utils-CxWrwrxK.js";import"./pos-api-D5WM5mnz.js";import"./vietqr-api-ruJT0-tj.js";import"./use-item-types-DA0U4OWS.js";import"./useQuery-BNGphiae.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bh5DVQPI.js";import"./query-keys-3lmd-xp6.js";import"./use-items-zU7JIOkv.js";import"./item-api-C8PXkgMG.js";import"./use-removed-items-Ck-YNHyo.js";import"./use-item-categories-_OOUG3-t.js";import"./xlsx-DkH2s96g.js";import"./use-printer-positions-data-DnTjyrKp.js";import"./printer-position-api-Q51LgdFK.js";import"./user-CgNoQQSj.js";import"./crm-api-BUMUQ8t4.js";import"./modal-CFi1vCFt.js";import"./dialog-DXjwjGKV.js";import"./calendar-BiBi2kQF.js";import"./createLucideIcon-CL0CQOA1.js";import"./index-Ct3V_iCU.js";import"./isSameMonth-C8JQo-AN.js";import"./date-range-picker-B1pgj5D_.js";import"./chevron-right-BAjIoZMb.js";import"./react-icons.esm-DB-kGUq7.js";import"./popover-BtedB187.js";import"./select-B8Pw9rS-.js";import"./index-Bh-UeytL.js";import"./index-DuT2Ibxp.js";import"./check-DcHT8QEO.js";import"./form-usWdQ_Nt.js";import"./input-4sMIt001.js";import"./checkbox-DUpnJ1Rx.js";import"./collapsible-B7-SIusD.js";import"./use-printer-positions-BVyOb7QH.js";const O=function(){const{id:o}=t.useParams();return r.jsx(m,{id:o})};export{O as component};
