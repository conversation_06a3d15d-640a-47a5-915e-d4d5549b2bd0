import{a_ as i,j as r}from"./index-C21OP4ex.js";import{P as m}from"./index-DAdNjdl5.js";import"./use-membership-discounts-BVCJEIfO.js";import"./useQuery-BNGphiae.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bh5DVQPI.js";import"./pos-api-D5WM5mnz.js";import"./discounts-DCn5I4UW.js";import"./query-keys-3lmd-xp6.js";import"./discount-toggle-button-DS1zKaLC.js";import"./date-range-picker-B1pgj5D_.js";import"./calendar-BiBi2kQF.js";import"./createLucideIcon-CL0CQOA1.js";import"./index-Ct3V_iCU.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-BAjIoZMb.js";import"./react-icons.esm-DB-kGUq7.js";import"./popover-BtedB187.js";import"./select-B8Pw9rS-.js";import"./index-Bh-UeytL.js";import"./index-DuT2Ibxp.js";import"./check-DcHT8QEO.js";import"./form-usWdQ_Nt.js";import"./discount-form-context-CCsjguqZ.js";import"./input-4sMIt001.js";import"./tabs-1l_oK3zl.js";import"./index-UJ-79IIJ.js";import"./checkbox-DUpnJ1Rx.js";import"./calendar-BszJEazX.js";import"./circle-help-DW9a4FhX.js";import"./date-utils-DBbLjCz0.js";const A=function(){const{id:o}=i.useParams(),{store_uid:t}=i.useSearch();return console.log("🔥 Payment Discount Detail Page - URL Params:"),console.log("🔥 discountId:",o),console.log("🔥 storeUid:",t),r.jsx(m,{discountId:o,storeUid:t})};export{A as component};
