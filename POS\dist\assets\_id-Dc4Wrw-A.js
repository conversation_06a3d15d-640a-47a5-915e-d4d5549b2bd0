import{aZ as r,j as t}from"./index-C21OP4ex.js";import{S as i}from"./index-tE1loSbZ.js";import"./use-service-charge-form-SyGvhPw1.js";import"./date-utils-DBbLjCz0.js";import"./useQuery-BNGphiae.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bh5DVQPI.js";import"./pos-api-D5WM5mnz.js";import"./query-keys-3lmd-xp6.js";import"./discount-toggle-button-DS1zKaLC.js";import"./date-range-picker-B1pgj5D_.js";import"./calendar-BiBi2kQF.js";import"./createLucideIcon-CL0CQOA1.js";import"./index-Ct3V_iCU.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-BAjIoZMb.js";import"./react-icons.esm-DB-kGUq7.js";import"./popover-BtedB187.js";import"./select-B8Pw9rS-.js";import"./index-Bh-UeytL.js";import"./index-DuT2Ibxp.js";import"./check-DcHT8QEO.js";import"./form-usWdQ_Nt.js";import"./input-4sMIt001.js";import"./tabs-1l_oK3zl.js";import"./index-UJ-79IIJ.js";import"./textarea-BLrSKk17.js";import"./checkbox-DUpnJ1Rx.js";import"./modal-CFi1vCFt.js";import"./dialog-DXjwjGKV.js";import"./collapsible-B7-SIusD.js";import"./calendar-BszJEazX.js";import"./circle-help-DW9a4FhX.js";import"./switch-Drz2_0hu.js";const G=function(){const{id:o}=r.useParams();return console.log("🔥 Service Charge Detail Page - URL Params:"),console.log("🔥 serviceChargeId:",o),t.jsx(i,{serviceChargeId:o})};export{G as component};
