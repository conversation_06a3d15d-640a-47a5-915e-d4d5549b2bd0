import{b4 as t,j as r}from"./index-Bnt3OGV2.js";import{C as m}from"./index-BXiwBge0.js";import"./error-utils-D-upmeeC.js";import"./pos-api-BwpRFGce.js";import"./use-stores-IzzNh8VY.js";import"./useQuery-DSrD7NAp.js";import"./utils-km2FGkQ4.js";import"./useMutation-d67-fNFq.js";import"./vietqr-api-DAENYiJ_.js";import"./stores-api-C8uGNHTJ.js";import"./query-keys-3lmd-xp6.js";import"./use-item-types-Q-0SzpLA.js";import"./use-items-DpA4YqSE.js";import"./item-api-aC51KELB.js";import"./use-removed-items-niCKPB6t.js";import"./use-item-categories-DcZ3EWYj.js";import"./xlsx-DkH2s96g.js";import"./use-printer-positions-data-Bq_uuWP4.js";import"./printer-position-api-DqvnQ7Ep.js";import"./user-oq7iQk7S.js";import"./crm-api-Dd9UhSCJ.js";import"./modal-B0J8RkN-.js";import"./dialog-hQ-PVOWr.js";import"./calendar-CzR6WBaB.js";import"./createLucideIcon-CNa_hh6B.js";import"./index-BT7Z3RDV.js";import"./isSameMonth-C8JQo-AN.js";import"./date-range-picker-CVvofQC0.js";import"./chevron-right-sZt3EK3r.js";import"./react-icons.esm-B7rNr9e-.js";import"./popover-C2mvzdeD.js";import"./select-Czd7KcZQ.js";import"./index-Bl1CGAiZ.js";import"./index-C2T2k_Lh.js";import"./check-apx2eTVC.js";import"./form-wT1R35uI.js";import"./input-CiKEYbig.js";import"./checkbox-BiVztVsP.js";import"./collapsible-CfXqqCTe.js";import"./use-printer-positions-Bh4o_29O.js";const T=function(){const{id:o}=t.useParams();return r.jsx(m,{id:o})};export{T as component};
