import{aA as o,j as t}from"./index-C21OP4ex.js";import{I as p}from"./item-detail-form-D9vYSLpe.js";import"./form-usWdQ_Nt.js";import"./pos-api-D5WM5mnz.js";import{b as e}from"./use-items-in-store-data-BGWAo6gn.js";import"./user-CgNoQQSj.js";import"./vietqr-api-ruJT0-tj.js";import"./crm-api-BUMUQ8t4.js";import"./header-DNPEfjkR.js";import"./main-DRnqW_wu.js";import"./search-context-DtMZc3QX.js";import"./date-range-picker-B1pgj5D_.js";import"./price-source-dialog-CIAnmvPi.js";import"./multi-select-DQY8oleQ.js";import"./exceljs.min-BFFGgdR1.js";import"./core.esm-Dic0nloh.js";import"./zod-B4gLZVLM.js";import"./use-upload-image-VAG6CJhu.js";import"./images-api-V0IeMkhY.js";import"./use-item-types-DA0U4OWS.js";import"./useQuery-BNGphiae.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bh5DVQPI.js";import"./query-keys-3lmd-xp6.js";import"./use-item-classes-_IC65iw9.js";import"./use-units-dM9GTfDw.js";import"./use-items-zU7JIOkv.js";import"./item-api-C8PXkgMG.js";import"./use-removed-items-Ck-YNHyo.js";import"./use-customizations-ZELiZ0he.js";import"./use-customization-by-id-CGF7bhQG.js";import"./use-sources-CSlkwBP-.js";import"./sources-api-Cb3TooI2.js";import"./sources-CfiQ7039.js";import"./useCanGoBack-CxRJAjVz.js";import"./calendar-BiBi2kQF.js";import"./createLucideIcon-CL0CQOA1.js";import"./index-Ct3V_iCU.js";import"./isSameMonth-C8JQo-AN.js";import"./checkbox-DUpnJ1Rx.js";import"./index-DuT2Ibxp.js";import"./check-DcHT8QEO.js";import"./input-4sMIt001.js";import"./textarea-BLrSKk17.js";import"./combobox-DyrNt90A.js";import"./command-BnLmWlRk.js";import"./dialog-DXjwjGKV.js";import"./search-DHRhj6_i.js";import"./popover-BtedB187.js";import"./chevrons-up-down-ChTQDbCM.js";import"./upload-CZfvv05H.js";import"./collapsible-B7-SIusD.js";import"./confirm-dialog-AXI5ANMc.js";import"./alert-dialog-CzdUByb-.js";import"./circle-help-DW9a4FhX.js";import"./select-B8Pw9rS-.js";import"./index-Bh-UeytL.js";import"./chevron-right-BAjIoZMb.js";import"./items-in-store-api-Bg8BWWH-.js";import"./xlsx-DkH2s96g.js";import"./separator-ZoxOB1XH.js";import"./createReactComponent-zh6rKAzG.js";import"./scroll-area-DKiYF9x5.js";import"./IconChevronRight-Bwbz4HuV.js";import"./react-icons.esm-DB-kGUq7.js";import"./use-dialog-state-BYP8UC3r.js";import"./modal-CFi1vCFt.js";import"./date-picker-8CCUFJO0.js";import"./calendar-BszJEazX.js";import"./badge-gtDUxDTX.js";import"./circle-x-CqN3rxdF.js";const Rt=function(){const{id:r}=o({strict:!1}),{data:i,isLoading:m}=e(r,!!r);return m?t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"flex items-center justify-center",children:t.jsx("div",{className:"text-lg",children:"Đang tải..."})})}):i!=null&&i.data?t.jsx(p,{currentRow:i.data}):t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"flex items-center justify-center",children:t.jsx("div",{className:"text-lg",children:"Không tìm thấy món ăn"})})})};export{Rt as component};
