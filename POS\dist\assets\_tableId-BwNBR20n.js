import{aU as t,j as m}from"./index-C21OP4ex.js";import"./pos-api-D5WM5mnz.js";import"./vietqr-api-ruJT0-tj.js";import"./user-CgNoQQSj.js";import"./crm-api-BUMUQ8t4.js";import"./header-DNPEfjkR.js";import"./main-DRnqW_wu.js";import"./search-context-DtMZc3QX.js";import"./date-range-picker-B1pgj5D_.js";import"./form-usWdQ_Nt.js";import{C as i}from"./create-table-form-DY1Q1-fO.js";import"./separator-ZoxOB1XH.js";import"./command-BnLmWlRk.js";import"./calendar-BiBi2kQF.js";import"./createLucideIcon-CL0CQOA1.js";import"./index-Ct3V_iCU.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-DXjwjGKV.js";import"./search-DHRhj6_i.js";import"./createReactComponent-zh6rKAzG.js";import"./scroll-area-DKiYF9x5.js";import"./index-Bh-UeytL.js";import"./select-B8Pw9rS-.js";import"./index-DuT2Ibxp.js";import"./check-DcHT8QEO.js";import"./IconChevronRight-Bwbz4HuV.js";import"./chevron-right-BAjIoZMb.js";import"./react-icons.esm-DB-kGUq7.js";import"./popover-BtedB187.js";import"./use-areas-RQjhzRCK.js";import"./useQuery-BNGphiae.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bh5DVQPI.js";import"./images-api-V0IeMkhY.js";import"./query-keys-3lmd-xp6.js";import"./use-sales-channels-B8DiedfZ.js";import"./use-tables-DC_pzXqw.js";import"./input-4sMIt001.js";import"./checkbox-DUpnJ1Rx.js";import"./collapsible-B7-SIusD.js";import"./use-items-in-store-data-BGWAo6gn.js";import"./use-item-types-DA0U4OWS.js";import"./use-item-classes-_IC65iw9.js";import"./use-units-dM9GTfDw.js";import"./use-removed-items-Ck-YNHyo.js";import"./items-in-store-api-Bg8BWWH-.js";import"./xlsx-DkH2s96g.js";import"./copy-Cfp_O40X.js";import"./plus-C1IEs-Ov.js";import"./minus-4BfcJ1sI.js";const rt=function(){const{tableId:o}=t.useParams(),{store_uid:r}=t.useSearch();return m.jsx(i,{areaId:o,storeUid:r,fromTableLayout:!0})};export{rt as component};
