import{j as t,B as c,L as m}from"./index-C21OP4ex.js";import"./date-range-picker-B1pgj5D_.js";import"./form-usWdQ_Nt.js";import{D as d}from"./data-table-DOnht7T9.js";import{B as l}from"./badge-gtDUxDTX.js";import{S as p}from"./settings-B2dEoYrB.js";import{u as h}from"./use-account-management-hPeoI3hr.js";import"./calendar-BiBi2kQF.js";import"./createLucideIcon-CL0CQOA1.js";import"./index-Ct3V_iCU.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-BAjIoZMb.js";import"./react-icons.esm-DB-kGUq7.js";import"./popover-BtedB187.js";import"./select-B8Pw9rS-.js";import"./index-Bh-UeytL.js";import"./index-DuT2Ibxp.js";import"./check-DcHT8QEO.js";import"./table-pagination-DK9zK72W.js";import"./pagination-CFzAWpgZ.js";import"./table-BIu4Pah2.js";function x({users:a,isLoading:s,onEditUser:n,onToggleStatus:r}){const o=[{key:"username",header:"Tên người dùng",width:"200px"},{key:"email",header:"Email",width:"250px"},{key:"status",header:"Trạng thái",width:"120px",render:i=>t.jsx(l,{variant:i==="active"?"default":"secondary",children:i==="active"?"Hoạt động":"Không hoạt động"})},{key:"actions",header:"",width:"100px",render:(i,e)=>t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(c,{variant:"ghost",size:"icon",onClick:()=>n(e),className:"h-8 w-8",children:t.jsx(p,{className:"h-4 w-4"})}),t.jsx(c,{variant:"ghost",size:"icon",onClick:()=>r(e.id),className:"h-8 w-8",children:e.status==="active"?"Hủy":"Kích hoạt"})]})}];return s?t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{children:"Đang tải dữ liệu..."})}):t.jsx(d,{data:a,columns:o,isLoading:s,pageSize:20,emptyMessage:"Không có tài khoản nào",loadingMessage:"Đang tải..."})}function g(){const{users:a,isLoading:s,error:n,toggleUserStatus:r}=h(),o=e=>{console.log("Edit user:",e)},i=async e=>{await r(e)};return n?t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{className:"text-red-600",children:n})})}):t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsx("div",{className:"mb-6",children:t.jsx(m,{to:"/general-setups/create-user",children:t.jsx(c,{children:"Tạo tài khoản"})})}),t.jsx(x,{users:a,isLoading:s,onEditUser:o,onToggleStatus:i})]})}const L=g;export{L as component};
