import{j as r,c as e,x as i}from"./index-Bnt3OGV2.js";const l=i("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d({className:t,variant:a,...s}){return r.jsx("div",{"data-slot":"alert",role:"alert",className:e(l({variant:a}),t),...s})}function c({className:t,...a}){return r.jsx("div",{"data-slot":"alert-title",className:e("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...a})}function o({className:t,...a}){return r.jsx("div",{"data-slot":"alert-description",className:e("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...a})}export{d as A,c as a,o as b};
