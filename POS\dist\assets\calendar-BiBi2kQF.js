import{c as on}from"./createLucideIcon-CL0CQOA1.js";import{r as D,A as rn,j as _,C as sn,D as Ce,E as Ee,P as ue,F as le,H as an,I as Te,J as cn,K as ln,M as fn,R as h,c as ie,n as pe}from"./index-C21OP4ex.js";import{h as un,R as dn,u as hn,F as mn}from"./index-Ct3V_iCU.js";import{e as Ke,g as Ve,h as Q,t as F,n as Je,i as yn,j as gn,k as Dn,l as vn,f as Mn,m as kn,o as bn,p as On,q as wn,r as Cn,s as pn,u as Wn,v as Nn,w as Sn,x as xn,y as Xe}from"./isSameMonth-C8JQo-AN.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _n=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],$r=on("x",_n);var Ie="Dialog",[Le,zr]=sn(Ie),[En,z]=Le(Ie),Qe=e=>{const{__scopeDialog:t,children:r,open:o,defaultOpen:n,onOpenChange:s,modal:i=!0}=e,a=D.useRef(null),c=D.useRef(null),[u=!1,d]=rn({prop:o,defaultProp:n,onChange:s});return _.jsx(En,{scope:t,triggerRef:a,contentRef:c,contentId:Ce(),titleId:Ce(),descriptionId:Ce(),open:u,onOpenChange:d,onOpenToggle:D.useCallback(()=>d(f=>!f),[d]),modal:i,children:r})};Qe.displayName=Ie;var et="DialogTrigger",tt=D.forwardRef((e,t)=>{const{__scopeDialog:r,...o}=e,n=z(et,r),s=Te(t,n.triggerRef);return _.jsx(ue.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":Pe(n.open),...o,ref:s,onClick:le(e.onClick,n.onOpenToggle)})});tt.displayName=et;var Ye="DialogPortal",[Tn,nt]=Le(Ye,{forceMount:void 0}),ot=e=>{const{__scopeDialog:t,forceMount:r,children:o,container:n}=e,s=z(Ye,t);return _.jsx(Tn,{scope:t,forceMount:r,children:D.Children.map(o,i=>_.jsx(Ee,{present:r||s.open,children:_.jsx(an,{asChild:!0,container:n,children:i})}))})};ot.displayName=Ye;var ve="DialogOverlay",rt=D.forwardRef((e,t)=>{const r=nt(ve,e.__scopeDialog),{forceMount:o=r.forceMount,...n}=e,s=z(ve,e.__scopeDialog);return s.modal?_.jsx(Ee,{present:o||s.open,children:_.jsx(Yn,{...n,ref:t})}):null});rt.displayName=ve;var In=cn("DialogOverlay.RemoveScroll"),Yn=D.forwardRef((e,t)=>{const{__scopeDialog:r,...o}=e,n=z(ve,r);return _.jsx(dn,{as:In,allowPinchZoom:!0,shards:[n.contentRef],children:_.jsx(ue.div,{"data-state":Pe(n.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),ne="DialogContent",st=D.forwardRef((e,t)=>{const r=nt(ne,e.__scopeDialog),{forceMount:o=r.forceMount,...n}=e,s=z(ne,e.__scopeDialog);return _.jsx(Ee,{present:o||s.open,children:s.modal?_.jsx(Bn,{...n,ref:t}):_.jsx(Pn,{...n,ref:t})})});st.displayName=ne;var Bn=D.forwardRef((e,t)=>{const r=z(ne,e.__scopeDialog),o=D.useRef(null),n=Te(t,r.contentRef,o);return D.useEffect(()=>{const s=o.current;if(s)return un(s)},[]),_.jsx(at,{...e,ref:n,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:le(e.onCloseAutoFocus,s=>{var i;s.preventDefault(),(i=r.triggerRef.current)==null||i.focus()}),onPointerDownOutside:le(e.onPointerDownOutside,s=>{const i=s.detail.originalEvent,a=i.button===0&&i.ctrlKey===!0;(i.button===2||a)&&s.preventDefault()}),onFocusOutside:le(e.onFocusOutside,s=>s.preventDefault())})}),Pn=D.forwardRef((e,t)=>{const r=z(ne,e.__scopeDialog),o=D.useRef(!1),n=D.useRef(!1);return _.jsx(at,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var i,a;(i=e.onCloseAutoFocus)==null||i.call(e,s),s.defaultPrevented||(o.current||(a=r.triggerRef.current)==null||a.focus(),s.preventDefault()),o.current=!1,n.current=!1},onInteractOutside:s=>{var c,u;(c=e.onInteractOutside)==null||c.call(e,s),s.defaultPrevented||(o.current=!0,s.detail.originalEvent.type==="pointerdown"&&(n.current=!0));const i=s.target;((u=r.triggerRef.current)==null?void 0:u.contains(i))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&n.current&&s.preventDefault()}})}),at=D.forwardRef((e,t)=>{const{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:n,onCloseAutoFocus:s,...i}=e,a=z(ne,r),c=D.useRef(null),u=Te(t,c);return hn(),_.jsxs(_.Fragment,{children:[_.jsx(mn,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:n,onUnmountAutoFocus:s,children:_.jsx(ln,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":Pe(a.open),...i,ref:u,onDismiss:()=>a.onOpenChange(!1)})}),_.jsxs(_.Fragment,{children:[_.jsx(An,{titleId:a.titleId}),_.jsx(Fn,{contentRef:c,descriptionId:a.descriptionId})]})]})}),Be="DialogTitle",it=D.forwardRef((e,t)=>{const{__scopeDialog:r,...o}=e,n=z(Be,r);return _.jsx(ue.h2,{id:n.titleId,...o,ref:t})});it.displayName=Be;var ct="DialogDescription",lt=D.forwardRef((e,t)=>{const{__scopeDialog:r,...o}=e,n=z(ct,r);return _.jsx(ue.p,{id:n.descriptionId,...o,ref:t})});lt.displayName=ct;var ft="DialogClose",ut=D.forwardRef((e,t)=>{const{__scopeDialog:r,...o}=e,n=z(ft,r);return _.jsx(ue.button,{type:"button",...o,ref:t,onClick:le(e.onClick,()=>n.onOpenChange(!1))})});ut.displayName=ft;function Pe(e){return e?"open":"closed"}var dt="DialogTitleWarning",[qr,ht]=fn(dt,{contentName:ne,titleName:Be,docsSlug:"dialog"}),An=({titleId:e})=>{const t=ht(dt),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return D.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},Rn="DialogDescriptionWarning",Fn=({contentRef:e,descriptionId:t})=>{const o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${ht(Rn).contentName}}.`;return D.useEffect(()=>{var s;const n=(s=e.current)==null?void 0:s.getAttribute("aria-describedby");t&&n&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},Gr=Qe,Zr=tt,Ur=ot,Kr=rt,Vr=st,Jr=it,Xr=lt,Lr=ut;function jn(e,t,r){return Ke(e,t*7,r)}function Hn(e,t,r){return Ve(e,t*12,r)}function $n(e,t){let r,o=t==null?void 0:t.in;return e.forEach(n=>{!o&&typeof n=="object"&&(o=Q.bind(null,n));const s=F(n,o);(!r||r<s||isNaN(+s))&&(r=s)}),Q(o,r||NaN)}function zn(e,t){let r,o=t==null?void 0:t.in;return e.forEach(n=>{!o&&typeof n=="object"&&(o=Q.bind(null,n));const s=F(n,o);(!r||r>s||isNaN(+s))&&(r=s)}),Q(o,r||NaN)}function qn(e,t,r){const[o,n]=Je(r==null?void 0:r.in,e,t),s=o.getFullYear()-n.getFullYear(),i=o.getMonth()-n.getMonth();return s*12+i}function Gn(e,t){const{start:r,end:o}=yn(t==null?void 0:t.in,e);let n=+r>+o;const s=n?+r:+o,i=n?o:r;i.setHours(0,0,0,0),i.setDate(1);let a=1;const c=[];for(;+i<=s;)c.push(Q(r,i)),i.setMonth(i.getMonth()+a);return n?c.reverse():c}function Zn(e,t){const r=F(e,t==null?void 0:t.in),o=r.getFullYear();return r.setFullYear(o+1,0,0),r.setHours(23,59,59,999),r}function mt(e,t){var a,c,u,d;const r=gn(),o=(t==null?void 0:t.weekStartsOn)??((c=(a=t==null?void 0:t.locale)==null?void 0:a.options)==null?void 0:c.weekStartsOn)??r.weekStartsOn??((d=(u=r.locale)==null?void 0:u.options)==null?void 0:d.weekStartsOn)??0,n=F(e,t==null?void 0:t.in),s=n.getDay(),i=(s<o?-7:0)+6-(s-o);return n.setDate(n.getDate()+i),n.setHours(23,59,59,999),n}function Un(e,t){return mt(e,{...t,weekStartsOn:1})}function Kn(e,t){const r=F(e,t==null?void 0:t.in),o=r.getFullYear(),n=r.getMonth(),s=Q(r,0);return s.setFullYear(o,n+1,0),s.setHours(0,0,0,0),s.getDate()}function Vn(e,t){return F(e,t==null?void 0:t.in).getMonth()}function Jn(e,t){return F(e,t==null?void 0:t.in).getFullYear()}function Xn(e,t){return+F(e)>+F(t)}function Ln(e,t){return+F(e)<+F(t)}function Qn(e,t,r){const[o,n]=Je(r==null?void 0:r.in,e,t);return o.getFullYear()===n.getFullYear()}function eo(e,t,r){const o=F(e,r==null?void 0:r.in),n=o.getFullYear(),s=o.getDate(),i=Q(e,0);i.setFullYear(n,t,15),i.setHours(0,0,0,0);const a=Kn(i);return o.setMonth(t,Math.min(s,a)),o}function to(e,t,r){const o=F(e,r==null?void 0:r.in);return isNaN(+o)?Q(e,NaN):(o.setFullYear(t),o)}var y;(function(e){e.Root="root",e.Chevron="chevron",e.Day="day",e.DayButton="day_button",e.CaptionLabel="caption_label",e.Dropdowns="dropdowns",e.Dropdown="dropdown",e.DropdownRoot="dropdown_root",e.Footer="footer",e.MonthGrid="month_grid",e.MonthCaption="month_caption",e.MonthsDropdown="months_dropdown",e.Month="month",e.Months="months",e.Nav="nav",e.NextMonthButton="button_next",e.PreviousMonthButton="button_previous",e.Week="week",e.Weeks="weeks",e.Weekday="weekday",e.Weekdays="weekdays",e.WeekNumber="week_number",e.WeekNumberHeader="week_number_header",e.YearsDropdown="years_dropdown"})(y||(y={}));var T;(function(e){e.disabled="disabled",e.hidden="hidden",e.outside="outside",e.focused="focused",e.today="today"})(T||(T={}));var $;(function(e){e.range_end="range_end",e.range_middle="range_middle",e.range_start="range_start",e.selected="selected"})($||($={}));var R;(function(e){e.weeks_before_enter="weeks_before_enter",e.weeks_before_exit="weeks_before_exit",e.weeks_after_enter="weeks_after_enter",e.weeks_after_exit="weeks_after_exit",e.caption_after_enter="caption_after_enter",e.caption_after_exit="caption_after_exit",e.caption_before_enter="caption_before_enter",e.caption_before_exit="caption_before_exit"})(R||(R={}));const We={},ce={};function fe(e,t){try{const o=(We[e]||(We[e]=new Intl.DateTimeFormat("en-GB",{timeZone:e,hour:"numeric",timeZoneName:"longOffset"}).format))(t).split("GMT")[1]||"";return o in ce?ce[o]:$e(o,o.split(":"))}catch{if(e in ce)return ce[e];const r=e==null?void 0:e.match(no);return r?$e(e,r.slice(1)):NaN}}const no=/([+-]\d\d):?(\d\d)?/;function $e(e,t){const r=+t[0],o=+(t[1]||0);return ce[e]=r>0?r*60+o:r*60-o}class Z extends Date{constructor(...t){super(),t.length>1&&typeof t[t.length-1]=="string"&&(this.timeZone=t.pop()),this.internal=new Date,isNaN(fe(this.timeZone,this))?this.setTime(NaN):t.length?typeof t[0]=="number"&&(t.length===1||t.length===2&&typeof t[1]!="number")?this.setTime(t[0]):typeof t[0]=="string"?this.setTime(+new Date(t[0])):t[0]instanceof Date?this.setTime(+t[0]):(this.setTime(+new Date(...t)),yt(this),_e(this)):this.setTime(Date.now())}static tz(t,...r){return r.length?new Z(...r,t):new Z(Date.now(),t)}withTimeZone(t){return new Z(+this,t)}getTimezoneOffset(){return-fe(this.timeZone,this)}setTime(t){return Date.prototype.setTime.apply(this,arguments),_e(this),+this}[Symbol.for("constructDateFrom")](t){return new Z(+new Date(t),this.timeZone)}}const ze=/^(get|set)(?!UTC)/;Object.getOwnPropertyNames(Date.prototype).forEach(e=>{if(!ze.test(e))return;const t=e.replace(ze,"$1UTC");Z.prototype[t]&&(e.startsWith("get")?Z.prototype[e]=function(){return this.internal[t]()}:(Z.prototype[e]=function(){return Date.prototype[t].apply(this.internal,arguments),oo(this),+this},Z.prototype[t]=function(){return Date.prototype[t].apply(this,arguments),_e(this),+this}))});function _e(e){e.internal.setTime(+e),e.internal.setUTCMinutes(e.internal.getUTCMinutes()-e.getTimezoneOffset())}function oo(e){Date.prototype.setFullYear.call(e,e.internal.getUTCFullYear(),e.internal.getUTCMonth(),e.internal.getUTCDate()),Date.prototype.setHours.call(e,e.internal.getUTCHours(),e.internal.getUTCMinutes(),e.internal.getUTCSeconds(),e.internal.getUTCMilliseconds()),yt(e)}function yt(e){const t=fe(e.timeZone,e),r=new Date(+e);r.setUTCHours(r.getUTCHours()-1);const o=-new Date(+e).getTimezoneOffset(),n=-new Date(+r).getTimezoneOffset(),s=o-n,i=Date.prototype.getHours.apply(e)!==e.internal.getUTCHours();s&&i&&e.internal.setUTCMinutes(e.internal.getUTCMinutes()+s);const a=o-t;a&&Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+a);const c=fe(e.timeZone,e),d=-new Date(+e).getTimezoneOffset()-c,f=c!==t,m=d-a;if(f&&m){Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+m);const g=fe(e.timeZone,e),v=c-g;v&&(e.internal.setUTCMinutes(e.internal.getUTCMinutes()+v),Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+v))}}class te extends Z{static tz(t,...r){return r.length?new te(...r,t):new te(Date.now(),t)}toISOString(){const[t,r,o]=this.tzComponents(),n=`${t}${r}:${o}`;return this.internal.toISOString().slice(0,-1)+n}toString(){return`${this.toDateString()} ${this.toTimeString()}`}toDateString(){const[t,r,o,n]=this.internal.toUTCString().split(" ");return`${t==null?void 0:t.slice(0,-1)} ${o} ${r} ${n}`}toTimeString(){const t=this.internal.toUTCString().split(" ")[4],[r,o,n]=this.tzComponents();return`${t} GMT${r}${o}${n} (${ro(this.timeZone,this)})`}toLocaleString(t,r){return Date.prototype.toLocaleString.call(this,t,{...r,timeZone:(r==null?void 0:r.timeZone)||this.timeZone})}toLocaleDateString(t,r){return Date.prototype.toLocaleDateString.call(this,t,{...r,timeZone:(r==null?void 0:r.timeZone)||this.timeZone})}toLocaleTimeString(t,r){return Date.prototype.toLocaleTimeString.call(this,t,{...r,timeZone:(r==null?void 0:r.timeZone)||this.timeZone})}tzComponents(){const t=this.getTimezoneOffset(),r=t>0?"-":"+",o=String(Math.floor(Math.abs(t)/60)).padStart(2,"0"),n=String(Math.abs(t)%60).padStart(2,"0");return[r,o,n]}withTimeZone(t){return new te(+this,t)}[Symbol.for("constructDateFrom")](t){return new te(+new Date(t),this.timeZone)}}function ro(e,t){return new Intl.DateTimeFormat("en-GB",{timeZone:e,timeZoneName:"long"}).format(t).slice(12)}const qe=5,so=4;function ao(e,t){const r=t.startOfMonth(e),o=r.getDay()>0?r.getDay():7,n=t.addDays(e,-o+1),s=t.addDays(n,qe*7-1);return t.getMonth(e)===t.getMonth(s)?qe:so}function gt(e,t){const r=t.startOfMonth(e),o=r.getDay();return o===1?r:o===0?t.addDays(r,-1*6):t.addDays(r,-1*(o-1))}function io(e,t){const r=gt(e,t),o=ao(e,t);return t.addDays(r,o*7-1)}class J{constructor(t,r){this.Date=Date,this.today=()=>{var o;return(o=this.overrides)!=null&&o.today?this.overrides.today():this.options.timeZone?te.tz(this.options.timeZone):new this.Date},this.newDate=(o,n,s)=>{var i;return(i=this.overrides)!=null&&i.newDate?this.overrides.newDate(o,n,s):this.options.timeZone?new te(o,n,s,this.options.timeZone):new Date(o,n,s)},this.addDays=(o,n)=>{var s;return(s=this.overrides)!=null&&s.addDays?this.overrides.addDays(o,n):Ke(o,n)},this.addMonths=(o,n)=>{var s;return(s=this.overrides)!=null&&s.addMonths?this.overrides.addMonths(o,n):Ve(o,n)},this.addWeeks=(o,n)=>{var s;return(s=this.overrides)!=null&&s.addWeeks?this.overrides.addWeeks(o,n):jn(o,n)},this.addYears=(o,n)=>{var s;return(s=this.overrides)!=null&&s.addYears?this.overrides.addYears(o,n):Hn(o,n)},this.differenceInCalendarDays=(o,n)=>{var s;return(s=this.overrides)!=null&&s.differenceInCalendarDays?this.overrides.differenceInCalendarDays(o,n):Dn(o,n)},this.differenceInCalendarMonths=(o,n)=>{var s;return(s=this.overrides)!=null&&s.differenceInCalendarMonths?this.overrides.differenceInCalendarMonths(o,n):qn(o,n)},this.eachMonthOfInterval=o=>{var n;return(n=this.overrides)!=null&&n.eachMonthOfInterval?this.overrides.eachMonthOfInterval(o):Gn(o)},this.endOfBroadcastWeek=o=>{var n;return(n=this.overrides)!=null&&n.endOfBroadcastWeek?this.overrides.endOfBroadcastWeek(o,this):io(o,this)},this.endOfISOWeek=o=>{var n;return(n=this.overrides)!=null&&n.endOfISOWeek?this.overrides.endOfISOWeek(o):Un(o)},this.endOfMonth=o=>{var n;return(n=this.overrides)!=null&&n.endOfMonth?this.overrides.endOfMonth(o):vn(o)},this.endOfWeek=o=>{var n;return(n=this.overrides)!=null&&n.endOfWeek?this.overrides.endOfWeek(o,this.options):mt(o,this.options)},this.endOfYear=o=>{var n;return(n=this.overrides)!=null&&n.endOfYear?this.overrides.endOfYear(o):Zn(o)},this.format=(o,n)=>{var i;const s=(i=this.overrides)!=null&&i.format?this.overrides.format(o,n,this.options):Mn(o,n,this.options);return this.options.numerals&&this.options.numerals!=="latn"?this.replaceDigits(s):s},this.getISOWeek=o=>{var n;return(n=this.overrides)!=null&&n.getISOWeek?this.overrides.getISOWeek(o):kn(o)},this.getMonth=o=>{var n;return(n=this.overrides)!=null&&n.getMonth?this.overrides.getMonth(o,this.options):Vn(o,this.options)},this.getYear=o=>{var n;return(n=this.overrides)!=null&&n.getYear?this.overrides.getYear(o,this.options):Jn(o,this.options)},this.getWeek=o=>{var n;return(n=this.overrides)!=null&&n.getWeek?this.overrides.getWeek(o,this.options):bn(o,this.options)},this.isAfter=(o,n)=>{var s;return(s=this.overrides)!=null&&s.isAfter?this.overrides.isAfter(o,n):Xn(o,n)},this.isBefore=(o,n)=>{var s;return(s=this.overrides)!=null&&s.isBefore?this.overrides.isBefore(o,n):Ln(o,n)},this.isDate=o=>{var n;return(n=this.overrides)!=null&&n.isDate?this.overrides.isDate(o):On(o)},this.isSameDay=(o,n)=>{var s;return(s=this.overrides)!=null&&s.isSameDay?this.overrides.isSameDay(o,n):wn(o,n)},this.isSameMonth=(o,n)=>{var s;return(s=this.overrides)!=null&&s.isSameMonth?this.overrides.isSameMonth(o,n):Cn(o,n)},this.isSameYear=(o,n)=>{var s;return(s=this.overrides)!=null&&s.isSameYear?this.overrides.isSameYear(o,n):Qn(o,n)},this.max=o=>{var n;return(n=this.overrides)!=null&&n.max?this.overrides.max(o):$n(o)},this.min=o=>{var n;return(n=this.overrides)!=null&&n.min?this.overrides.min(o):zn(o)},this.setMonth=(o,n)=>{var s;return(s=this.overrides)!=null&&s.setMonth?this.overrides.setMonth(o,n):eo(o,n)},this.setYear=(o,n)=>{var s;return(s=this.overrides)!=null&&s.setYear?this.overrides.setYear(o,n):to(o,n)},this.startOfBroadcastWeek=o=>{var n;return(n=this.overrides)!=null&&n.startOfBroadcastWeek?this.overrides.startOfBroadcastWeek(o,this):gt(o,this)},this.startOfDay=o=>{var n;return(n=this.overrides)!=null&&n.startOfDay?this.overrides.startOfDay(o):pn(o)},this.startOfISOWeek=o=>{var n;return(n=this.overrides)!=null&&n.startOfISOWeek?this.overrides.startOfISOWeek(o):Wn(o)},this.startOfMonth=o=>{var n;return(n=this.overrides)!=null&&n.startOfMonth?this.overrides.startOfMonth(o):Nn(o)},this.startOfWeek=o=>{var n;return(n=this.overrides)!=null&&n.startOfWeek?this.overrides.startOfWeek(o,this.options):Sn(o,this.options)},this.startOfYear=o=>{var n;return(n=this.overrides)!=null&&n.startOfYear?this.overrides.startOfYear(o):xn(o)},this.options={locale:Xe,...t},this.overrides=r}getDigitMap(){const{numerals:t="latn"}=this.options,r=new Intl.NumberFormat("en-US",{numberingSystem:t}),o={};for(let n=0;n<10;n++)o[n.toString()]=r.format(n);return o}replaceDigits(t){const r=this.getDigitMap();return t.replace(/\d/g,o=>r[o]||o)}formatNumber(t){return this.replaceDigits(t.toString())}}const X=new J;function co(e,t,r={}){return Object.entries(e).filter(([,n])=>n===!0).reduce((n,[s])=>(r[s]?n.push(r[s]):t[T[s]]?n.push(t[T[s]]):t[$[s]]&&n.push(t[$[s]]),n),[t[y.Day]])}function lo(e){return h.createElement("button",{...e})}function fo(e){return h.createElement("span",{...e})}function uo(e){const{size:t=24,orientation:r="left",className:o}=e;return h.createElement("svg",{className:o,width:t,height:t,viewBox:"0 0 24 24"},r==="up"&&h.createElement("polygon",{points:"6.77 17 12.5 11.43 18.24 17 20 15.28 12.5 8 5 15.28"}),r==="down"&&h.createElement("polygon",{points:"6.77 8 12.5 13.57 18.24 8 20 9.72 12.5 17 5 9.72"}),r==="left"&&h.createElement("polygon",{points:"16 18.112 9.81111111 12 16 5.87733333 14.0888889 4 6 12 14.0888889 20"}),r==="right"&&h.createElement("polygon",{points:"8 18.612 14.1888889 12.5 8 6.37733333 9.91111111 4.5 18 12.5 9.91111111 20.5"}))}function ho(e){const{day:t,modifiers:r,...o}=e;return h.createElement("td",{...o})}function mo(e){const{day:t,modifiers:r,...o}=e,n=h.useRef(null);return h.useEffect(()=>{var s;r.focused&&((s=n.current)==null||s.focus())},[r.focused]),h.createElement("button",{ref:n,...o})}function yo(e){const{options:t,className:r,components:o,classNames:n,...s}=e,i=[n[y.Dropdown],r].join(" "),a=t==null?void 0:t.find(({value:c})=>c===s.value);return h.createElement("span",{"data-disabled":s.disabled,className:n[y.DropdownRoot]},h.createElement(o.Select,{className:i,...s},t==null?void 0:t.map(({value:c,label:u,disabled:d})=>h.createElement(o.Option,{key:c,value:c,disabled:d},u))),h.createElement("span",{className:n[y.CaptionLabel],"aria-hidden":!0},a==null?void 0:a.label,h.createElement(o.Chevron,{orientation:"down",size:18,className:n[y.Chevron]})))}function go(e){return h.createElement("div",{...e})}function Do(e){return h.createElement("div",{...e})}function vo(e){const{calendarMonth:t,displayIndex:r,...o}=e;return h.createElement("div",{...o},e.children)}function Mo(e){const{calendarMonth:t,displayIndex:r,...o}=e;return h.createElement("div",{...o})}function ko(e){return h.createElement("table",{...e})}function bo(e){return h.createElement("div",{...e})}const Dt=D.createContext(void 0);function de(){const e=D.useContext(Dt);if(e===void 0)throw new Error("useDayPicker() must be used within a custom component.");return e}function Oo(e){const{components:t}=de();return h.createElement(t.Dropdown,{...e})}function wo(e){const{onPreviousClick:t,onNextClick:r,previousMonth:o,nextMonth:n,...s}=e,{components:i,classNames:a,labels:{labelPrevious:c,labelNext:u}}=de(),d=D.useCallback(m=>{n&&(r==null||r(m))},[n,r]),f=D.useCallback(m=>{o&&(t==null||t(m))},[o,t]);return h.createElement("nav",{...s},h.createElement(i.PreviousMonthButton,{type:"button",className:a[y.PreviousMonthButton],tabIndex:o?void 0:-1,"aria-disabled":o?void 0:!0,"aria-label":c(o),onClick:f},h.createElement(i.Chevron,{disabled:o?void 0:!0,className:a[y.Chevron],orientation:"left"})),h.createElement(i.NextMonthButton,{type:"button",className:a[y.NextMonthButton],tabIndex:n?void 0:-1,"aria-disabled":n?void 0:!0,"aria-label":u(n),onClick:d},h.createElement(i.Chevron,{disabled:n?void 0:!0,orientation:"right",className:a[y.Chevron]})))}function Co(e){const{components:t}=de();return h.createElement(t.Button,{...e})}function po(e){return h.createElement("option",{...e})}function Wo(e){const{components:t}=de();return h.createElement(t.Button,{...e})}function No(e){const{rootRef:t,...r}=e;return h.createElement("div",{...r,ref:t})}function So(e){return h.createElement("select",{...e})}function xo(e){const{week:t,...r}=e;return h.createElement("tr",{...r})}function _o(e){return h.createElement("th",{...e})}function Eo(e){return h.createElement("thead",{"aria-hidden":!0},h.createElement("tr",{...e}))}function To(e){const{week:t,...r}=e;return h.createElement("th",{...r})}function Io(e){return h.createElement("th",{...e})}function Yo(e){return h.createElement("tbody",{...e})}function Bo(e){const{components:t}=de();return h.createElement(t.Dropdown,{...e})}const Po=Object.freeze(Object.defineProperty({__proto__:null,Button:lo,CaptionLabel:fo,Chevron:uo,Day:ho,DayButton:mo,Dropdown:yo,DropdownNav:go,Footer:Do,Month:vo,MonthCaption:Mo,MonthGrid:ko,Months:bo,MonthsDropdown:Oo,Nav:wo,NextMonthButton:Co,Option:po,PreviousMonthButton:Wo,Root:No,Select:So,Week:xo,WeekNumber:To,WeekNumberHeader:Io,Weekday:_o,Weekdays:Eo,Weeks:Yo,YearsDropdown:Bo},Symbol.toStringTag,{value:"Module"}));function Ao(e){return{...Po,...e}}function Ro(e){const t={"data-mode":e.mode??void 0,"data-required":"required"in e?e.required:void 0,"data-multiple-months":e.numberOfMonths&&e.numberOfMonths>1||void 0,"data-week-numbers":e.showWeekNumber||void 0,"data-broadcast-calendar":e.broadcastCalendar||void 0};return Object.entries(e).forEach(([r,o])=>{r.startsWith("data-")&&(t[r]=o)}),t}function Fo(){const e={};for(const t in y)e[y[t]]=`rdp-${y[t]}`;for(const t in T)e[T[t]]=`rdp-${T[t]}`;for(const t in $)e[$[t]]=`rdp-${$[t]}`;for(const t in R)e[R[t]]=`rdp-${R[t]}`;return e}function vt(e,t,r){return(r??new J(t)).format(e,"LLLL y")}const jo=vt;function Ho(e,t,r){return(r??new J(t)).format(e,"d")}function $o(e,t=X){return t.format(e,"LLLL")}function zo(e){return e<10?`0${e.toLocaleString()}`:`${e.toLocaleString()}`}function qo(){return""}function Go(e,t,r){return(r??new J(t)).format(e,"cccccc")}function Mt(e,t=X){return t.format(e,"yyyy")}const Zo=Mt,Uo=Object.freeze(Object.defineProperty({__proto__:null,formatCaption:vt,formatDay:Ho,formatMonthCaption:jo,formatMonthDropdown:$o,formatWeekNumber:zo,formatWeekNumberHeader:qo,formatWeekdayName:Go,formatYearCaption:Zo,formatYearDropdown:Mt},Symbol.toStringTag,{value:"Module"}));function Ko(e){return e!=null&&e.formatMonthCaption&&!e.formatCaption&&(e.formatCaption=e.formatMonthCaption),e!=null&&e.formatYearCaption&&!e.formatYearDropdown&&(e.formatYearDropdown=e.formatYearCaption),{...Uo,...e}}function Vo(e,t,r,o,n){const{startOfMonth:s,startOfYear:i,endOfYear:a,eachMonthOfInterval:c,getMonth:u}=n;return c({start:i(e),end:a(e)}).map(m=>{const g=o.formatMonthDropdown(m,n),v=u(m),C=t&&m<s(t)||r&&m>s(r)||!1;return{value:v,label:g,disabled:C}})}function Jo(e,t={},r={}){let o={...t==null?void 0:t[y.Day]};return Object.entries(e).filter(([,n])=>n===!0).forEach(([n])=>{o={...o,...r==null?void 0:r[n]}}),o}function Xo(e,t,r){const o=e.today(),n=t?e.startOfISOWeek(o):e.startOfWeek(o),s=[];for(let i=0;i<7;i++){const a=e.addDays(n,i);s.push(a)}return s}function Lo(e,t,r,o){if(!e||!t)return;const{startOfYear:n,endOfYear:s,addYears:i,getYear:a,isBefore:c,isSameYear:u}=o,d=n(e),f=s(t),m=[];let g=d;for(;c(g,f)||u(g,f);)m.push(g),g=i(g,1);return m.map(v=>{const C=r.formatYearDropdown(v,o);return{value:a(v),label:C,disabled:!1}})}function kt(e,t,r){return(r??new J(t)).format(e,"LLLL y")}const Qo=kt;function er(e,t,r,o){let n=(o??new J(r)).format(e,"PPPP");return t!=null&&t.today&&(n=`Today, ${n}`),n}function bt(e,t,r,o){let n=(o??new J(r)).format(e,"PPPP");return t.today&&(n=`Today, ${n}`),t.selected&&(n=`${n}, selected`),n}const tr=bt;function nr(){return""}function or(e){return"Choose the Month"}function rr(e){return"Go to the Next Month"}function sr(e){return"Go to the Previous Month"}function ar(e,t,r){return(r??new J(t)).format(e,"cccc")}function ir(e,t){return`Week ${e}`}function cr(e){return"Week Number"}function lr(e){return"Choose the Year"}const fr=Object.freeze(Object.defineProperty({__proto__:null,labelCaption:Qo,labelDay:tr,labelDayButton:bt,labelGrid:kt,labelGridcell:er,labelMonthDropdown:or,labelNav:nr,labelNext:rr,labelPrevious:sr,labelWeekNumber:ir,labelWeekNumberHeader:cr,labelWeekday:ar,labelYearDropdown:lr},Symbol.toStringTag,{value:"Module"})),he=e=>e instanceof HTMLElement?e:null,Ne=e=>[...e.querySelectorAll("[data-animated-month]")??[]],ur=e=>he(e.querySelector("[data-animated-month]")),Se=e=>he(e.querySelector("[data-animated-caption]")),xe=e=>he(e.querySelector("[data-animated-weeks]")),dr=e=>he(e.querySelector("[data-animated-nav]")),hr=e=>he(e.querySelector("[data-animated-weekdays]"));function mr(e,t,{classNames:r,months:o,focused:n,dateLib:s}){const i=D.useRef(null),a=D.useRef(o),c=D.useRef(!1);D.useLayoutEffect(()=>{const u=a.current;if(a.current=o,!t||!e.current||!(e.current instanceof HTMLElement)||o.length===0||u.length===0||o.length!==u.length)return;const d=s.isSameMonth(o[0].date,u[0].date),f=s.isAfter(o[0].date,u[0].date),m=f?r[R.caption_after_enter]:r[R.caption_before_enter],g=f?r[R.weeks_after_enter]:r[R.weeks_before_enter],v=i.current,C=e.current.cloneNode(!0);if(C instanceof HTMLElement?(Ne(C).forEach(M=>{if(!(M instanceof HTMLElement))return;const N=ur(M);N&&M.contains(N)&&M.removeChild(N);const S=Se(M);S&&S.classList.remove(m);const w=xe(M);w&&w.classList.remove(g)}),i.current=C):i.current=null,c.current||d||n)return;const k=v instanceof HTMLElement?Ne(v):[],b=Ne(e.current);if(b&&b.every(l=>l instanceof HTMLElement)&&k&&k.every(l=>l instanceof HTMLElement)){c.current=!0,e.current.style.isolation="isolate";const l=dr(e.current);l&&(l.style.zIndex="1"),b.forEach((M,N)=>{const S=k[N];if(!S)return;M.style.position="relative",M.style.overflow="hidden";const w=Se(M);w&&w.classList.add(m);const O=xe(M);O&&O.classList.add(g);const E=()=>{c.current=!1,e.current&&(e.current.style.isolation=""),l&&(l.style.zIndex=""),w&&w.classList.remove(m),O&&O.classList.remove(g),M.style.position="",M.style.overflow="",M.contains(S)&&M.removeChild(S)};S.style.pointerEvents="none",S.style.position="absolute",S.style.overflow="hidden",S.setAttribute("aria-hidden","true");const j=hr(S);j&&(j.style.opacity="0");const A=Se(S);A&&(A.classList.add(f?r[R.caption_before_exit]:r[R.caption_after_exit]),A.addEventListener("animationend",E));const P=xe(S);P&&P.classList.add(f?r[R.weeks_before_exit]:r[R.weeks_after_exit]),M.insertBefore(S,M.firstChild)})}})}function yr(e,t,r,o){const n=e[0],s=e[e.length-1],{ISOWeek:i,fixedWeeks:a,broadcastCalendar:c}=r??{},{addDays:u,differenceInCalendarDays:d,differenceInCalendarMonths:f,endOfBroadcastWeek:m,endOfISOWeek:g,endOfMonth:v,endOfWeek:C,isAfter:k,startOfBroadcastWeek:b,startOfISOWeek:l,startOfWeek:M}=o,N=c?b(n,o):i?l(n):M(n),S=c?m(s,o):i?g(v(s)):C(v(s)),w=d(S,N),O=f(s,n)+1,E=[];for(let P=0;P<=w;P++){const Y=u(N,P);if(t&&k(Y,t))break;E.push(Y)}const A=(c?35:42)*O;if(a&&E.length<A){const P=A-E.length;for(let Y=0;Y<P;Y++){const ee=u(E[E.length-1],1);E.push(ee)}}return E}function gr(e){const t=[];return e.reduce((r,o)=>{const n=[],s=o.weeks.reduce((i,a)=>[...i,...a.days],n);return[...r,...s]},t)}function Dr(e,t,r,o){const{numberOfMonths:n=1}=r,s=[];for(let i=0;i<n;i++){const a=o.addMonths(e,i);if(t&&a>t)break;s.push(a)}return s}function Ge(e,t){const{month:r,defaultMonth:o,today:n=t.today(),numberOfMonths:s=1,endMonth:i,startMonth:a}=e;let c=r||o||n;const{differenceInCalendarMonths:u,addMonths:d,startOfMonth:f}=t;if(i&&u(i,c)<0){const m=-1*(s-1);c=d(i,m)}return a&&u(c,a)<0&&(c=a),f(c)}class Ot{constructor(t,r,o=X){this.date=t,this.displayMonth=r,this.outside=!!(r&&!o.isSameMonth(t,r)),this.dateLib=o}isEqualTo(t){return this.dateLib.isSameDay(t.date,this.date)&&this.dateLib.isSameMonth(t.displayMonth,this.displayMonth)}}class vr{constructor(t,r){this.date=t,this.weeks=r}}class Mr{constructor(t,r){this.days=r,this.weekNumber=t}}function kr(e,t,r,o){const{addDays:n,endOfBroadcastWeek:s,endOfISOWeek:i,endOfMonth:a,endOfWeek:c,getISOWeek:u,getWeek:d,startOfBroadcastWeek:f,startOfISOWeek:m,startOfWeek:g}=o,v=e.reduce((C,k)=>{const b=r.broadcastCalendar?f(k,o):r.ISOWeek?m(k):g(k),l=r.broadcastCalendar?s(k,o):r.ISOWeek?i(a(k)):c(a(k)),M=t.filter(O=>O>=b&&O<=l),N=r.broadcastCalendar?35:42;if(r.fixedWeeks&&M.length<N){const O=t.filter(E=>{const j=N-M.length;return E>l&&E<=n(l,j)});M.push(...O)}const S=M.reduce((O,E)=>{const j=r.ISOWeek?u(E):d(E),A=O.find(Y=>Y.weekNumber===j),P=new Ot(E,k,o);return A?A.days.push(P):O.push(new Mr(j,[P])),O},[]),w=new vr(k,S);return C.push(w),C},[]);return r.reverseMonths?v.reverse():v}function br(e,t){let{startMonth:r,endMonth:o}=e;const{startOfYear:n,startOfDay:s,startOfMonth:i,endOfMonth:a,addYears:c,endOfYear:u,newDate:d,today:f}=t,{fromYear:m,toYear:g,fromMonth:v,toMonth:C}=e;!r&&v&&(r=v),!r&&m&&(r=t.newDate(m,0,1)),!o&&C&&(o=C),!o&&g&&(o=d(g,11,31));const k=e.captionLayout==="dropdown"||e.captionLayout==="dropdown-years";return r?r=i(r):m?r=d(m,0,1):!r&&k&&(r=n(c(e.today??f(),-100))),o?o=a(o):g?o=d(g,11,31):!o&&k&&(o=u(e.today??f())),[r&&s(r),o&&s(o)]}function Or(e,t,r,o){if(r.disableNavigation)return;const{pagedNavigation:n,numberOfMonths:s=1}=r,{startOfMonth:i,addMonths:a,differenceInCalendarMonths:c}=o,u=n?s:1,d=i(e);if(!t)return a(d,u);if(!(c(t,e)<s))return a(d,u)}function wr(e,t,r,o){if(r.disableNavigation)return;const{pagedNavigation:n,numberOfMonths:s}=r,{startOfMonth:i,addMonths:a,differenceInCalendarMonths:c}=o,u=n?s??1:1,d=i(e);if(!t)return a(d,-u);if(!(c(d,t)<=0))return a(d,-u)}function Cr(e){const t=[];return e.reduce((r,o)=>[...r,...o.weeks],t)}function Me(e,t){const[r,o]=D.useState(e);return[t===void 0?r:t,o]}function pr(e,t){const[r,o]=br(e,t),{startOfMonth:n,endOfMonth:s}=t,i=Ge(e,t),[a,c]=Me(i,e.month?i:void 0);D.useEffect(()=>{const w=Ge(e,t);c(w)},[e.timeZone]);const u=Dr(a,o,e,t),d=yr(u,e.endMonth?s(e.endMonth):void 0,e,t),f=kr(u,d,e,t),m=Cr(f),g=gr(f),v=wr(a,r,e,t),C=Or(a,o,e,t),{disableNavigation:k,onMonthChange:b}=e,l=w=>m.some(O=>O.days.some(E=>E.isEqualTo(w))),M=w=>{if(k)return;let O=n(w);r&&O<n(r)&&(O=n(r)),o&&O>n(o)&&(O=n(o)),c(O),b==null||b(O)};return{months:f,weeks:m,days:g,navStart:r,navEnd:o,previousMonth:v,nextMonth:C,goToMonth:M,goToDay:w=>{l(w)||M(w.date)}}}var G;(function(e){e[e.Today=0]="Today",e[e.Selected=1]="Selected",e[e.LastFocused=2]="LastFocused",e[e.FocusedModifier=3]="FocusedModifier"})(G||(G={}));function Ze(e){return!e[T.disabled]&&!e[T.hidden]&&!e[T.outside]}function Wr(e,t,r,o){let n,s=-1;for(const i of e){const a=t(i);Ze(a)&&(a[T.focused]&&s<G.FocusedModifier?(n=i,s=G.FocusedModifier):o!=null&&o.isEqualTo(i)&&s<G.LastFocused?(n=i,s=G.LastFocused):r(i.date)&&s<G.Selected?(n=i,s=G.Selected):a[T.today]&&s<G.Today&&(n=i,s=G.Today))}return n||(n=e.find(i=>Ze(t(i)))),n}function K(e,t,r=!1,o=X){let{from:n,to:s}=e;const{differenceInCalendarDays:i,isSameDay:a}=o;return n&&s?(i(s,n)<0&&([n,s]=[s,n]),i(t,n)>=(r?1:0)&&i(s,t)>=(r?1:0)):!r&&s?a(s,t):!r&&n?a(n,t):!1}function wt(e){return!!(e&&typeof e=="object"&&"before"in e&&"after"in e)}function Ae(e){return!!(e&&typeof e=="object"&&"from"in e)}function Ct(e){return!!(e&&typeof e=="object"&&"after"in e)}function pt(e){return!!(e&&typeof e=="object"&&"before"in e)}function Wt(e){return!!(e&&typeof e=="object"&&"dayOfWeek"in e)}function Nt(e,t){return Array.isArray(e)&&e.every(t.isDate)}function V(e,t,r=X){const o=Array.isArray(t)?t:[t],{isSameDay:n,differenceInCalendarDays:s,isAfter:i}=r;return o.some(a=>{if(typeof a=="boolean")return a;if(r.isDate(a))return n(e,a);if(Nt(a,r))return a.includes(e);if(Ae(a))return K(a,e,!1,r);if(Wt(a))return Array.isArray(a.dayOfWeek)?a.dayOfWeek.includes(e.getDay()):a.dayOfWeek===e.getDay();if(wt(a)){const c=s(a.before,e),u=s(a.after,e),d=c>0,f=u<0;return i(a.before,a.after)?f&&d:d||f}return Ct(a)?s(e,a.after)>0:pt(a)?s(a.before,e)>0:typeof a=="function"?a(e):!1})}function Nr(e,t,r,o,n,s,i){const{ISOWeek:a,broadcastCalendar:c}=s,{addDays:u,addMonths:d,addWeeks:f,addYears:m,endOfBroadcastWeek:g,endOfISOWeek:v,endOfWeek:C,max:k,min:b,startOfBroadcastWeek:l,startOfISOWeek:M,startOfWeek:N}=i;let w={day:u,week:f,month:d,year:m,startOfWeek:O=>c?l(O,i):a?M(O):N(O),endOfWeek:O=>c?g(O,i):a?v(O):C(O)}[e](r,t==="after"?1:-1);return t==="before"&&o?w=k([o,w]):t==="after"&&n&&(w=b([n,w])),w}function St(e,t,r,o,n,s,i,a=0){if(a>365)return;const c=Nr(e,t,r.date,o,n,s,i),u=!!(s.disabled&&V(c,s.disabled,i)),d=!!(s.hidden&&V(c,s.hidden,i)),f=c,m=new Ot(c,f,i);return!u&&!d?m:St(e,t,m,o,n,s,i,a+1)}function Sr(e,t,r,o,n){const{autoFocus:s}=e,[i,a]=D.useState(),c=Wr(t.days,r,o||(()=>!1),i),[u,d]=D.useState(s?c:void 0);return{isFocusTarget:C=>!!(c!=null&&c.isEqualTo(C)),setFocused:d,focused:u,blur:()=>{a(u),d(void 0)},moveFocus:(C,k)=>{if(!u)return;const b=St(C,k,u,t.navStart,t.navEnd,e,n);b&&(t.goToDay(b),d(b))}}}function xr(e,t,r){const{disabled:o,hidden:n,modifiers:s,showOutsideDays:i,broadcastCalendar:a,today:c}=t,{isSameDay:u,isSameMonth:d,startOfMonth:f,isBefore:m,endOfMonth:g,isAfter:v}=r,C=t.startMonth&&f(t.startMonth),k=t.endMonth&&g(t.endMonth),b={[T.focused]:[],[T.outside]:[],[T.disabled]:[],[T.hidden]:[],[T.today]:[]},l={};for(const M of e){const{date:N,displayMonth:S}=M,w=!!(S&&!d(N,S)),O=!!(C&&m(N,C)),E=!!(k&&v(N,k)),j=!!(o&&V(N,o,r)),A=!!(n&&V(N,n,r))||O||E||!a&&!i&&w||a&&i===!1&&w,P=u(N,c??r.today());w&&b.outside.push(M),j&&b.disabled.push(M),A&&b.hidden.push(M),P&&b.today.push(M),s&&Object.keys(s).forEach(Y=>{const ee=s==null?void 0:s[Y];ee&&V(N,ee,r)&&(l[Y]?l[Y].push(M):l[Y]=[M])})}return M=>{const N={[T.focused]:!1,[T.disabled]:!1,[T.hidden]:!1,[T.outside]:!1,[T.today]:!1},S={};for(const w in b){const O=b[w];N[w]=O.some(E=>E===M)}for(const w in l)S[w]=l[w].some(O=>O===M);return{...N,...S}}}function _r(e,t){const{selected:r,required:o,onSelect:n}=e,[s,i]=Me(r,n?r:void 0),a=n?r:s,{isSameDay:c}=t,u=g=>(a==null?void 0:a.some(v=>c(v,g)))??!1,{min:d,max:f}=e;return{selected:a,select:(g,v,C)=>{let k=[...a??[]];if(u(g)){if((a==null?void 0:a.length)===d||o&&(a==null?void 0:a.length)===1)return;k=a==null?void 0:a.filter(b=>!c(b,g))}else(a==null?void 0:a.length)===f?k=[g]:k=[...k,g];return n||i(k),n==null||n(k,g,v,C),k},isSelected:u}}function Er(e,t,r=0,o=0,n=!1,s=X){const{from:i,to:a}=t||{},{isSameDay:c,isAfter:u,isBefore:d}=s;let f;if(!i&&!a)f={from:e,to:r>0?void 0:e};else if(i&&!a)c(i,e)?n?f={from:i,to:void 0}:f=void 0:d(e,i)?f={from:e,to:i}:f={from:i,to:e};else if(i&&a)if(c(i,e)&&c(a,e))n?f={from:i,to:a}:f=void 0;else if(c(i,e))f={from:i,to:r>0?void 0:e};else if(c(a,e))f={from:e,to:r>0?void 0:e};else if(d(e,i))f={from:e,to:a};else if(u(e,i))f={from:i,to:e};else if(u(e,a))f={from:i,to:e};else throw new Error("Invalid range");if(f!=null&&f.from&&(f!=null&&f.to)){const m=s.differenceInCalendarDays(f.to,f.from);o>0&&m>o?f={from:e,to:void 0}:r>1&&m<r&&(f={from:e,to:void 0})}return f}function Tr(e,t,r=X){const o=Array.isArray(t)?t:[t];let n=e.from;const s=r.differenceInCalendarDays(e.to,e.from),i=Math.min(s,6);for(let a=0;a<=i;a++){if(o.includes(n.getDay()))return!0;n=r.addDays(n,1)}return!1}function Ue(e,t,r=X){return K(e,t.from,!1,r)||K(e,t.to,!1,r)||K(t,e.from,!1,r)||K(t,e.to,!1,r)}function Ir(e,t,r=X){const o=Array.isArray(t)?t:[t];if(o.filter(a=>typeof a!="function").some(a=>typeof a=="boolean"?a:r.isDate(a)?K(e,a,!1,r):Nt(a,r)?a.some(c=>K(e,c,!1,r)):Ae(a)?a.from&&a.to?Ue(e,{from:a.from,to:a.to},r):!1:Wt(a)?Tr(e,a.dayOfWeek,r):wt(a)?r.isAfter(a.before,a.after)?Ue(e,{from:r.addDays(a.after,1),to:r.addDays(a.before,-1)},r):V(e.from,a,r)||V(e.to,a,r):Ct(a)||pt(a)?V(e.from,a,r)||V(e.to,a,r):!1))return!0;const i=o.filter(a=>typeof a=="function");if(i.length){let a=e.from;const c=r.differenceInCalendarDays(e.to,e.from);for(let u=0;u<=c;u++){if(i.some(d=>d(a)))return!0;a=r.addDays(a,1)}}return!1}function Yr(e,t){const{disabled:r,excludeDisabled:o,selected:n,required:s,onSelect:i}=e,[a,c]=Me(n,i?n:void 0),u=i?n:a;return{selected:u,select:(m,g,v)=>{const{min:C,max:k}=e,b=m?Er(m,u,C,k,s,t):void 0;return o&&r&&(b!=null&&b.from)&&b.to&&Ir({from:b.from,to:b.to},r,t)&&(b.from=m,b.to=void 0),i||c(b),i==null||i(b,m,g,v),b},isSelected:m=>u&&K(u,m,!1,t)}}function Br(e,t){const{selected:r,required:o,onSelect:n}=e,[s,i]=Me(r,n?r:void 0),a=n?r:s,{isSameDay:c}=t;return{selected:a,select:(f,m,g)=>{let v=f;return!o&&a&&a&&c(f,a)&&(v=void 0),n||i(v),n==null||n(v,f,m,g),v},isSelected:f=>a?c(a,f):!1}}function Pr(e,t){const r=Br(e,t),o=_r(e,t),n=Yr(e,t);switch(e.mode){case"single":return r;case"multiple":return o;case"range":return n;default:return}}function Ar(e){const{components:t,formatters:r,labels:o,dateLib:n,locale:s,classNames:i}=D.useMemo(()=>{const p={...Xe,...e.locale};return{dateLib:new J({locale:p,weekStartsOn:e.broadcastCalendar?1:e.weekStartsOn,firstWeekContainsDate:e.firstWeekContainsDate,useAdditionalWeekYearTokens:e.useAdditionalWeekYearTokens,useAdditionalDayOfYearTokens:e.useAdditionalDayOfYearTokens,timeZone:e.timeZone,numerals:e.numerals},e.dateLib),components:Ao(e.components),formatters:Ko(e.formatters),labels:{...fr,...e.labels},locale:p,classNames:{...Fo(),...e.classNames}}},[e.locale,e.broadcastCalendar,e.weekStartsOn,e.firstWeekContainsDate,e.useAdditionalWeekYearTokens,e.useAdditionalDayOfYearTokens,e.timeZone,e.numerals,e.dateLib,e.components,e.formatters,e.labels,e.classNames]),{captionLayout:a,mode:c,onDayBlur:u,onDayClick:d,onDayFocus:f,onDayKeyDown:m,onDayMouseEnter:g,onDayMouseLeave:v,onNextClick:C,onPrevClick:k,showWeekNumber:b,styles:l}=e,{formatCaption:M,formatDay:N,formatMonthDropdown:S,formatWeekNumber:w,formatWeekNumberHeader:O,formatWeekdayName:E,formatYearDropdown:j}=r,A=pr(e,n),{days:P,months:Y,navStart:ee,navEnd:ke,previousMonth:oe,nextMonth:re,goToMonth:U}=A,be=xr(P,e,n),{isSelected:se,select:ae,selected:me}=Pr(e,n)??{},{blur:Re,focused:ye,isFocusTarget:xt,moveFocus:Fe,setFocused:ge}=Sr(e,A,be,se??(()=>!1),n),{labelDayButton:_t,labelGridcell:Et,labelGrid:Tt,labelMonthDropdown:It,labelNav:Yt,labelWeekday:Bt,labelWeekNumber:Pt,labelWeekNumberHeader:At,labelYearDropdown:Rt}=o,Ft=D.useMemo(()=>Xo(n,e.ISOWeek),[n,e.ISOWeek]),je=c!==void 0||d!==void 0,jt=D.useCallback(()=>{oe&&(U(oe),k==null||k(oe))},[oe,U,k]),Ht=D.useCallback(()=>{re&&(U(re),C==null||C(re))},[U,re,C]),$t=D.useCallback((p,I)=>x=>{x.preventDefault(),x.stopPropagation(),ge(p),ae==null||ae(p.date,I,x),d==null||d(p.date,I,x)},[ae,d,ge]),zt=D.useCallback((p,I)=>x=>{ge(p),f==null||f(p.date,I,x)},[f,ge]),qt=D.useCallback((p,I)=>x=>{Re(),u==null||u(p.date,I,x)},[Re,u]),Gt=D.useCallback((p,I)=>x=>{const L={ArrowLeft:["day",e.dir==="rtl"?"after":"before"],ArrowRight:["day",e.dir==="rtl"?"before":"after"],ArrowDown:["week","after"],ArrowUp:["week","before"],PageUp:[x.shiftKey?"year":"month","before"],PageDown:[x.shiftKey?"year":"month","after"],Home:["startOfWeek","before"],End:["endOfWeek","after"]};if(L[x.key]){x.preventDefault(),x.stopPropagation();const[H,De]=L[x.key];Fe(H,De)}m==null||m(p.date,I,x)},[Fe,m,e.dir]),Zt=D.useCallback((p,I)=>x=>{g==null||g(p.date,I,x)},[g]),Ut=D.useCallback((p,I)=>x=>{v==null||v(p.date,I,x)},[v]),Kt=D.useCallback(p=>I=>{const x=Number(I.target.value),L=n.setMonth(n.startOfMonth(p),x);U(L)},[n,U]),Vt=D.useCallback(p=>I=>{const x=Number(I.target.value),L=n.setYear(n.startOfMonth(p),x);U(L)},[n,U]),{className:Jt,style:Xt}=D.useMemo(()=>({className:[i[y.Root],e.className].filter(Boolean).join(" "),style:{...l==null?void 0:l[y.Root],...e.style}}),[i,e.className,e.style,l]),Lt=Ro(e),He=D.useRef(null);mr(He,!!e.animate,{classNames:i,months:Y,focused:ye,dateLib:n});const Qt={dayPickerProps:e,selected:me,select:ae,isSelected:se,months:Y,nextMonth:re,previousMonth:oe,goToMonth:U,getModifiers:be,components:t,classNames:i,styles:l,labels:o,formatters:r};return h.createElement(Dt.Provider,{value:Qt},h.createElement(t.Root,{rootRef:e.animate?He:void 0,className:Jt,style:Xt,dir:e.dir,id:e.id,lang:e.lang,nonce:e.nonce,title:e.title,role:e.role,"aria-label":e["aria-label"],...Lt},h.createElement(t.Months,{className:i[y.Months],style:l==null?void 0:l[y.Months]},!e.hideNavigation&&h.createElement(t.Nav,{"data-animated-nav":e.animate?"true":void 0,className:i[y.Nav],style:l==null?void 0:l[y.Nav],"aria-label":Yt(),onPreviousClick:jt,onNextClick:Ht,previousMonth:oe,nextMonth:re}),Y.map((p,I)=>{const x=Vo(p.date,ee,ke,r,n),L=Lo(ee,ke,r,n);return h.createElement(t.Month,{"data-animated-month":e.animate?"true":void 0,className:i[y.Month],style:l==null?void 0:l[y.Month],key:I,displayIndex:I,calendarMonth:p},h.createElement(t.MonthCaption,{"data-animated-caption":e.animate?"true":void 0,className:i[y.MonthCaption],style:l==null?void 0:l[y.MonthCaption],calendarMonth:p,displayIndex:I},a!=null&&a.startsWith("dropdown")?h.createElement(t.DropdownNav,{className:i[y.Dropdowns],style:l==null?void 0:l[y.Dropdowns]},a==="dropdown"||a==="dropdown-months"?h.createElement(t.MonthsDropdown,{className:i[y.MonthsDropdown],"aria-label":It(),classNames:i,components:t,disabled:!!e.disableNavigation,onChange:Kt(p.date),options:x,style:l==null?void 0:l[y.Dropdown],value:n.getMonth(p.date)}):h.createElement("span",null,S(p.date,n)),a==="dropdown"||a==="dropdown-years"?h.createElement(t.YearsDropdown,{className:i[y.YearsDropdown],"aria-label":Rt(n.options),classNames:i,components:t,disabled:!!e.disableNavigation,onChange:Vt(p.date),options:L,style:l==null?void 0:l[y.Dropdown],value:n.getYear(p.date)}):h.createElement("span",null,j(p.date,n)),h.createElement("span",{role:"status","aria-live":"polite",style:{border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap",wordWrap:"normal"}},M(p.date,n.options,n))):h.createElement(t.CaptionLabel,{className:i[y.CaptionLabel],role:"status","aria-live":"polite"},M(p.date,n.options,n))),h.createElement(t.MonthGrid,{role:"grid","aria-multiselectable":c==="multiple"||c==="range","aria-label":Tt(p.date,n.options,n)||void 0,className:i[y.MonthGrid],style:l==null?void 0:l[y.MonthGrid]},!e.hideWeekdays&&h.createElement(t.Weekdays,{"data-animated-weekdays":e.animate?"true":void 0,className:i[y.Weekdays],style:l==null?void 0:l[y.Weekdays]},b&&h.createElement(t.WeekNumberHeader,{"aria-label":At(n.options),className:i[y.WeekNumberHeader],style:l==null?void 0:l[y.WeekNumberHeader],scope:"col"},O()),Ft.map((H,De)=>h.createElement(t.Weekday,{"aria-label":Bt(H,n.options,n),className:i[y.Weekday],key:De,style:l==null?void 0:l[y.Weekday],scope:"col"},E(H,n.options,n)))),h.createElement(t.Weeks,{"data-animated-weeks":e.animate?"true":void 0,className:i[y.Weeks],style:l==null?void 0:l[y.Weeks]},p.weeks.map((H,De)=>h.createElement(t.Week,{className:i[y.Week],key:H.weekNumber,style:l==null?void 0:l[y.Week],week:H},b&&h.createElement(t.WeekNumber,{week:H,style:l==null?void 0:l[y.WeekNumber],"aria-label":Pt(H.weekNumber,{locale:s}),className:i[y.WeekNumber],scope:"row",role:"rowheader"},w(H.weekNumber)),H.days.map(B=>{const{date:q}=B,W=be(B);if(W[T.focused]=!W.hidden&&!!(ye!=null&&ye.isEqualTo(B)),W[$.selected]=(se==null?void 0:se(q))||W.selected,Ae(me)){const{from:Oe,to:we}=me;W[$.range_start]=!!(Oe&&we&&n.isSameDay(q,Oe)),W[$.range_end]=!!(Oe&&we&&n.isSameDay(q,we)),W[$.range_middle]=K(me,q,!0,n)}const en=Jo(W,l,e.modifiersStyles),tn=co(W,i,e.modifiersClassNames),nn=!je&&!W.hidden?Et(q,W,n.options,n):void 0;return h.createElement(t.Day,{key:`${n.format(q,"yyyy-MM-dd")}_${n.format(B.displayMonth,"yyyy-MM")}`,day:B,modifiers:W,className:tn.join(" "),style:en,role:"gridcell","aria-selected":W.selected||void 0,"aria-label":nn,"data-day":n.format(q,"yyyy-MM-dd"),"data-month":B.outside?n.format(q,"yyyy-MM"):void 0,"data-selected":W.selected||void 0,"data-disabled":W.disabled||void 0,"data-hidden":W.hidden||void 0,"data-outside":B.outside||void 0,"data-focused":W.focused||void 0,"data-today":W.today||void 0},!W.hidden&&je?h.createElement(t.DayButton,{className:i[y.DayButton],style:l==null?void 0:l[y.DayButton],type:"button",day:B,modifiers:W,disabled:W.disabled||void 0,tabIndex:xt(B)?0:-1,"aria-label":_t(q,W,n.options,n),onClick:$t(B,W),onBlur:qt(B,W),onFocus:zt(B,W),onKeyDown:Gt(B,W),onMouseEnter:Zt(B,W),onMouseLeave:Ut(B,W)},N(q,n.options,n)):!W.hidden&&N(B.date,n.options,n))}))))))})),e.footer&&h.createElement(t.Footer,{className:i[y.Footer],style:l==null?void 0:l[y.Footer],role:"status","aria-live":"polite"},e.footer)))}function Qr({className:e,classNames:t,showOutsideDays:r=!0,...o}){return _.jsx(Ar,{showOutsideDays:r,className:ie("p-3",e),mode:"single",classNames:{months:"flex flex-col sm:flex-row gap-2",month:"flex flex-col gap-4",month_caption:"flex justify-center pt-1 relative items-center w-full",caption_label:"text-sm font-medium",nav:"flex items-center gap-1",button_previous:ie(pe({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100 z-10","absolute left-4 top-3"),button_next:ie(pe({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100 z-10","absolute right-4 top-3"),month_grid:"w-full border-collapse space-x-1",weekdays:"flex",weekday:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",week:"flex w-full mt-2",day:ie("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md",o.mode==="range"?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":'aria-selected:rounded-md [&[aria-selected="true"]>button]:hover:bg-foreground [&[aria-selected="true"]>button]:hover:text-background/85'),day_button:ie(pe({variant:"ghost"}),"size-8 p-0 font-normal aria-selected:opacity-100"),day_selected:"opacity-100 bg-yellow-500",range_start:"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground",range_end:"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground",selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",today:"bg-accent text-accent-foreground rounded-md",outside:"day-outside text-muted-foreground aria-selected:text-muted-foreground",disabled:"text-muted-foreground opacity-50",range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",hidden:"invisible",...t},...o})}export{Qr as C,Xr as D,Kr as O,Ur as P,Gr as R,Jr as T,qr as W,$r as X,Vr as a,Lr as b,Zr as c,zr as d};
