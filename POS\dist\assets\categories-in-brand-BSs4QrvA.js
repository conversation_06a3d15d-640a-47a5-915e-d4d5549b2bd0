import{j as e,B as w,r as v,a4 as h,h as H}from"./index-C21OP4ex.js";import"./pos-api-D5WM5mnz.js";import"./vietqr-api-ruJT0-tj.js";import{a as K,u as X,b as Q,c as V}from"./use-item-types-DA0U4OWS.js";import{u as W,a as z}from"./use-item-categories-_OOUG3-t.js";import"./user-CgNoQQSj.js";import"./crm-api-BUMUQ8t4.js";import{H as q}from"./header-DNPEfjkR.js";import{M as G}from"./main-DRnqW_wu.js";import{C as J}from"./index-DemC1Nlr.js";import"./date-range-picker-B1pgj5D_.js";import"./form-usWdQ_Nt.js";import{g as Y,I as Z,S as ee}from"./id-generators-DiKoEqgK.js";import{P as te}from"./profile-dropdown-DlMxjxHH.js";import{S as ne,T as ae}from"./search-5KGATEvM.js";import{S as se}from"./status-badge-DUaAKf8K.js";import{I as re}from"./IconTrash-Cb-q_mwO.js";import{u as oe,e as ie,f as L}from"./index-B_FCwlUM.js";import{T as R,a as B,b,c as _,d as A,e as M}from"./table-BIu4Pah2.js";import{D as le,a as ce,b as me,c as O}from"./dropdown-menu-DINyPmco.js";import{I as de}from"./input-4sMIt001.js";import{read as pe,utils as he}from"./xlsx-DkH2s96g.js";import{P as ue}from"./modal-CFi1vCFt.js";import{I as xe}from"./IconUpload-DshrUJV1.js";import{I as ge}from"./IconChevronDown-BR388CFf.js";import{I as fe}from"./IconDownload-BcT9uill.js";import{I as ye}from"./IconPlus-DpdcQY0q.js";import"./useQuery-BNGphiae.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bh5DVQPI.js";import"./query-keys-3lmd-xp6.js";import"./separator-ZoxOB1XH.js";import"./dialog-DXjwjGKV.js";import"./calendar-BiBi2kQF.js";import"./createLucideIcon-CL0CQOA1.js";import"./index-Ct3V_iCU.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-BAjIoZMb.js";import"./react-icons.esm-DB-kGUq7.js";import"./popover-BtedB187.js";import"./select-B8Pw9rS-.js";import"./index-Bh-UeytL.js";import"./index-DuT2Ibxp.js";import"./check-DcHT8QEO.js";import"./createReactComponent-zh6rKAzG.js";import"./skeleton-xUyFo20h.js";import"./avatar-C98m2_SM.js";import"./search-context-DtMZc3QX.js";import"./command-BnLmWlRk.js";import"./search-DHRhj6_i.js";import"./scroll-area-DKiYF9x5.js";import"./IconChevronRight-Bwbz4HuV.js";import"./IconSearch-CyZD7dtp.js";import"./badge-gtDUxDTX.js";import"./index-UJ-79IIJ.js";const je=[{accessorKey:"id",header:"#",cell:({row:t})=>{const r=t.index+1;return e.jsx("div",{className:"w-[50px] font-medium",children:r})},enableSorting:!1},{accessorKey:"item_type_id",header:"Mã nhóm",cell:({row:t})=>{const r=t.original;return e.jsx("span",{className:"font-mono text-sm",children:r.item_type_id})}},{accessorKey:"item_type_name",header:"Tên nhóm",cell:({row:t})=>{const r=t.original;return e.jsx("span",{className:"font-medium",children:r.item_type_name})}},{accessorKey:"active",header:"Thao tác",cell:({row:t,table:r})=>{const o=t.original,a=r.options.meta;return e.jsx("div",{onClick:i=>{var s;i.stopPropagation(),(s=a==null?void 0:a.onToggleStatus)==null||s.call(a,o)},className:"cursor-pointer",children:e.jsx(se,{isActive:o.active===1,activeText:"Active",inactiveText:"Deactive"})})}},{id:"delete",header:"",cell:({row:t,table:r})=>{const o=t.original,a=r.options.meta;return e.jsxs(w,{variant:"ghost",size:"sm",onClick:i=>{var s;i.stopPropagation(),(s=a==null?void 0:a.onDeleteCategory)==null||s.call(a,o)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",title:"Xóa nhóm",children:[e.jsx(re,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Xóa nhóm ",o.item_type_name]})]})}}];function Ce({columns:t,data:r,onEditCategory:o,onDeleteCategory:a,onToggleStatus:i,onRowClick:s}){var l;const n=oe({data:r,columns:t,getCoreRowModel:ie(),meta:{onEditCategory:o,onDeleteCategory:a,onToggleStatus:i}});return e.jsx("div",{className:"rounded-md border",children:e.jsxs(R,{children:[e.jsx(B,{children:n.getHeaderGroups().map(d=>e.jsx(b,{children:d.headers.map(c=>e.jsx(_,{children:c.isPlaceholder?null:L(c.column.columnDef.header,c.getContext())},c.id))},d.id))}),e.jsx(A,{children:(l=n.getRowModel().rows)!=null&&l.length?n.getRowModel().rows.map(d=>e.jsx(b,{className:s?"cursor-pointer hover:bg-gray-50":"",onClick:()=>s==null?void 0:s(d.original),children:d.getVisibleCells().map(c=>e.jsx(M,{children:L(c.column.columnDef.cell,c.getContext())},c.id))},d.id)):e.jsx(b,{children:e.jsx(M,{colSpan:t.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]})})}function ve(){const[t,r]=v.useState(!1),[o,a]=v.useState(null),[i,s]=v.useState([]),[n,l]=v.useState(!1),d=v.useRef(null),c=K(),g=()=>{r(!1),l(!1),s([]),a(null)},p=()=>{const m=document.createElement("a");m.href="/files/categories/categories-in-brand/import_item_type_template.xlsx",m.download="import_item_type_template.xlsx",document.body.appendChild(m),m.click(),document.body.removeChild(m)},I=()=>{r(!0),l(!1),s([]),a(null)},y=()=>{var m;(m=d.current)==null||m.click()},T=async m=>new Promise((x,f)=>{const j=new FileReader;j.onload=u=>{var C;try{const S=new Uint8Array((C=u.target)==null?void 0:C.result),P=pe(S,{type:"array"}),U=P.SheetNames[0],$=P.Sheets[U],E=he.sheet_to_json($,{header:["name","id"],range:1}).filter(N=>N.name).map(N=>({name:String(N.name).trim(),id:N.id?String(N.id).trim():Y()}));if(E.length===0){h.error("File không có dữ liệu hợp lệ"),f(new Error("No valid data"));return}x(E)}catch(S){h.error("Lỗi khi đọc file Excel"),f(S)}},j.onerror=()=>{h.error("Lỗi khi đọc file"),f(new Error("File read error"))},j.readAsArrayBuffer(m)});return{importModalOpen:t,setImportModalOpen:r,importSelectedFile:o,importParsedData:i,showImportParsedData:n,importFileInputRef:d,resetImportState:g,handleOpenImportModal:I,handleDownloadTemplate:p,handleImportFileUpload:y,handleImportFileChange:async m=>{var f;const x=(f=m.target.files)==null?void 0:f[0];if(x){if(!x.name.endsWith(".xlsx")&&!x.name.endsWith(".xls")){h.error("Vui lòng chọn file Excel (.xlsx hoặc .xls)");return}a(x);try{const j=await T(x);s(j),l(!0),h.success(`Đã phân tích ${j.length} nhóm món từ file!`)}catch{a(null)}}},handleSaveImportedCategories:async()=>{if(i.length===0)return h.error("Không có dữ liệu để lưu"),!1;try{const m=i.map((x,f)=>({item_type_name:x.name,item_type_id:x.id,sort:f+1}));return await c.mutateAsync(m),h.success(`Đã tạo thành công ${i.length} nhóm món!`),g(),!0}catch{return h.error("Lỗi khi tạo nhóm món. Vui lòng thử lại."),!1}},isLoading:c.isPending}}function Ie({open:t,onOpenChange:r,showImportParsedData:o,importSelectedFile:a,importParsedData:i,isLoading:s,onCancel:n,onConfirm:l,onDownloadTemplate:d,onImportFileUpload:c}){return e.jsx(ue,{title:"Thêm nhóm",open:t,onOpenChange:r,onCancel:n,onConfirm:l||(()=>{}),confirmText:o?"Lưu":void 0,cancelText:o?"Hủy":void 0,hideButtons:!o,centerTitle:!0,maxWidth:o?"sm:max-w-4xl":"sm:max-w-[500px]",isLoading:s,children:e.jsx("div",{className:"space-y-4",children:o?e.jsx(e.Fragment,{children:e.jsx("div",{className:"space-y-4",children:e.jsx("div",{className:"max-h-96 overflow-auto rounded-md border",children:e.jsxs(R,{children:[e.jsx(B,{children:e.jsxs(b,{children:[e.jsx(_,{children:"ID"}),e.jsx(_,{children:"Tên nhóm"})]})}),e.jsx(A,{children:i.map((g,p)=>e.jsxs(b,{children:[e.jsx(M,{children:g.id}),e.jsx(M,{children:g.name})]},p))})]})})})}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-sm text-gray-600",children:"File tải lên có cấu trúc như sau:"}),e.jsx("div",{className:"flex justify-center",children:e.jsx("img",{src:"/images/categories/categories-in-brand/template-tao-nhom-mon.png",alt:"Template structure",className:"h-auto max-w-full rounded-md border"})}),e.jsx("div",{className:"text-left",children:e.jsxs("span",{className:"text-sm text-gray-600",children:["Hoặc xem trong"," ",e.jsx("button",{onClick:d,className:"text-blue-600 underline hover:text-blue-800",children:"file mẫu"})]})}),e.jsx("div",{className:"flex justify-center pt-4",children:e.jsxs(w,{onClick:c,className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-4 w-4"}),"Tải file excel lên"]})})]})})})})}function Se(t){return t instanceof Error?t.message:typeof t=="string"?t:t&&typeof t=="object"&&"message"in t?String(t.message):"Đã xảy ra lỗi không xác định"}function Ne({searchQuery:t,onSearchQueryChange:r,onSearchSubmit:o,onCreateCategory:a,isCreating:i}){const s=W(),n=ve(),l=async()=>{try{const p=await s.mutateAsync({}),I=URL.createObjectURL(p),y=document.createElement("a");y.href=I,y.download=`nhom-mon-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(y),y.click(),document.body.removeChild(y),URL.revokeObjectURL(I),h.success("Xuất nhóm món thành công!")}catch(p){const I=Se(p);h.error(I)}},d=()=>{n.handleOpenImportModal()},c=()=>{n.resetImportState()},g=async()=>{await n.handleSaveImportedCategories()};return e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("h2",{className:"text-2xl font-semibold",children:"Nhóm món toàn thương hiệu"}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Nhóm món giúp bạn sắp xếp và tổ chức các món, báo cáo về doanh số bán hàng và định tuyến các món đến máy in cụ thể."})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(de,{placeholder:"Tìm kiếm nhóm món...",className:"w-64",value:t,onChange:p=>r(p.target.value),onKeyDown:p=>{p.key==="Enter"&&(p.preventDefault(),o())}}),e.jsxs(le,{children:[e.jsx(ce,{asChild:!0,children:e.jsxs(w,{size:"sm",children:["Tiện ích",e.jsx(ge,{className:"ml-2 h-4 w-4"})]})}),e.jsxs(me,{align:"end",children:[e.jsxs(O,{onClick:d,className:"flex items-center gap-2",children:[e.jsx(Z,{className:"h-4 w-4"}),"Thêm nhóm từ file"]}),e.jsxs(O,{onClick:l,className:"flex items-center gap-2",disabled:s.isPending,children:[e.jsx(fe,{className:"h-4 w-4"}),s.isPending?"Đang xuất...":"Xuất nhóm món"]})]})]}),e.jsxs(w,{onClick:a,className:"flex items-center gap-2",disabled:i,children:[e.jsx(ye,{className:"h-4 w-4"}),i?"Đang tạo...":"Tạo nhóm"]})]}),e.jsx(Ie,{open:n.importModalOpen,onOpenChange:n.setImportModalOpen,showImportParsedData:n.showImportParsedData,importSelectedFile:n.importSelectedFile,importParsedData:n.importParsedData,isLoading:n.isLoading,onCancel:c,onConfirm:g,onDownloadTemplate:n.handleDownloadTemplate,onImportFileUpload:n.handleImportFileUpload}),e.jsx("input",{ref:n.importFileInputRef,type:"file",accept:".xlsx,.xls",onChange:n.handleImportFileChange,className:"hidden"})]})}function D(t){return t instanceof Error?t.message:typeof t=="string"?t:t&&typeof t=="object"&&"message"in t?String(t.message):"Đã xảy ra lỗi không xác định"}function be(){const t=H(),[r,o]=v.useState(""),[a,i]=v.useState(""),[s,n]=v.useState(!1),[l,d]=v.useState(null),{data:c,isLoading:g,error:p}=X({search:r||void 0,enabled:!0}),I=z(),y=Q(),T=V(),k=()=>{t({to:"/menu/category/detail"})},F=async u=>{try{const C={...u,active:u.active===1?0:1};await y.mutateAsync(C);const S=C.active===1?"kích hoạt":"vô hiệu hóa";h.success(`${S} nhóm "${u.item_type_name}" thành công!`)}catch(C){const S=D(C);h.error(S)}},m=u=>{t({to:"/menu/category/detail/$id",params:{id:u.id}})},x=u=>{t({to:"/menu/category/detail/$id",params:{id:u.id}})},f=u=>{d(u),n(!0)},j=async()=>{if(l)try{await T.mutateAsync(l.id),h.success(`Xóa nhóm "${l.item_type_name}" thành công!`),n(!1),d(null)}catch(u){const C=D(u);h.error(C)}};return e.jsxs(e.Fragment,{children:[e.jsx(q,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ne,{}),e.jsx(ae,{}),e.jsx(te,{})]})}),e.jsx(G,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(Ne,{searchQuery:a,onSearchQueryChange:i,onSearchSubmit:()=>o(a),onCreateCategory:k,isCreating:I.isPending}),p&&e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{className:"text-red-600",children:D(p)})}),g&&e.jsx(ee,{rows:8,columns:5}),!p&&!g&&e.jsx(Ce,{columns:je,data:c||[],onEditCategory:m,onDeleteCategory:f,onToggleStatus:F,onRowClick:x}),e.jsx(J,{open:s,onOpenChange:n,content:l?`Bạn có muốn xóa nhóm "${l.item_type_name}"?`:"Bạn có muốn xóa nhóm này?",confirmText:"Xác nhận",onConfirm:j,isLoading:T.isPending})]})})]})}const Mt=be;export{Mt as component};
