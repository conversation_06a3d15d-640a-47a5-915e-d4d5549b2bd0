import{r as n,A as O,j as s,C as T,D as S,P as m,F as D,E as M,I as $,Y as F}from"./index-C21OP4ex.js";var g="Collapsible",[L,H]=T(g),[k,x]=L(g),_=n.forwardRef((e,r)=>{const{__scopeCollapsible:l,open:a,defaultOpen:t,disabled:c,onOpenChange:i,...f}=e,[p=!1,d]=O({prop:a,defaultProp:t,onChange:i});return s.jsx(k,{scope:l,disabled:c,contentId:S(),open:p,onOpenToggle:n.useCallback(()=>d(b=>!b),[d]),children:s.jsx(m.div,{"data-state":R(p),"data-disabled":c?"":void 0,...f,ref:r})})});_.displayName=g;var A="CollapsibleTrigger",I=n.forwardRef((e,r)=>{const{__scopeCollapsible:l,...a}=e,t=x(A,l);return s.jsx(m.button,{type:"button","aria-controls":t.contentId,"aria-expanded":t.open||!1,"data-state":R(t.open),"data-disabled":t.disabled?"":void 0,disabled:t.disabled,...a,ref:r,onClick:D(e.onClick,t.onOpenToggle)})});I.displayName=A;var v="CollapsibleContent",w=n.forwardRef((e,r)=>{const{forceMount:l,...a}=e,t=x(v,e.__scopeCollapsible);return s.jsx(M,{present:l||t.open,children:({present:c})=>s.jsx(B,{...a,ref:r,present:c})})});w.displayName=v;var B=n.forwardRef((e,r)=>{const{__scopeCollapsible:l,present:a,children:t,...c}=e,i=x(v,l),[f,p]=n.useState(a),d=n.useRef(null),b=$(r,d),h=n.useRef(0),y=h.current,P=n.useRef(0),N=P.current,C=i.open||f,E=n.useRef(C),u=n.useRef(void 0);return n.useEffect(()=>{const o=requestAnimationFrame(()=>E.current=!1);return()=>cancelAnimationFrame(o)},[]),F(()=>{const o=d.current;if(o){u.current=u.current||{transitionDuration:o.style.transitionDuration,animationName:o.style.animationName},o.style.transitionDuration="0s",o.style.animationName="none";const j=o.getBoundingClientRect();h.current=j.height,P.current=j.width,E.current||(o.style.transitionDuration=u.current.transitionDuration,o.style.animationName=u.current.animationName),p(a)}},[i.open,a]),s.jsx(m.div,{"data-state":R(i.open),"data-disabled":i.disabled?"":void 0,id:i.contentId,hidden:!C,...c,ref:b,style:{"--radix-collapsible-content-height":y?`${y}px`:void 0,"--radix-collapsible-content-width":N?`${N}px`:void 0,...e.style},children:C&&t})});function R(e){return e?"open":"closed"}var G=_;function Y({...e}){return s.jsx(G,{"data-slot":"collapsible",...e})}function z({...e}){return s.jsx(I,{"data-slot":"collapsible-trigger",...e})}function J({...e}){return s.jsx(w,{"data-slot":"collapsible-content",...e})}export{Y as C,z as a,J as b};
