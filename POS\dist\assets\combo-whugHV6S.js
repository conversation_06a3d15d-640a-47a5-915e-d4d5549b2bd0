import{a3 as Qe,a4 as M,u as he,l as Fe,j as e,B as K,r as f,h as We}from"./index-C21OP4ex.js";import{a as Te}from"./pos-api-D5WM5mnz.js";import{u as ee}from"./use-stores-BQdEFBhG.js";import{u as Ue,a as Le,c as Oe,b as Je,d as Ae}from"./use-combos-XDEL7_3-.js";import"./vietqr-api-ruJT0-tj.js";import"./user-CgNoQQSj.js";import"./crm-api-BUMUQ8t4.js";import{H as Ze}from"./header-DNPEfjkR.js";import{P as Ve}from"./profile-dropdown-DlMxjxHH.js";import{S as et,T as tt}from"./search-5KGATEvM.js";import"./date-range-picker-B1pgj5D_.js";import{L as W}from"./form-usWdQ_Nt.js";import{I as Re}from"./input-4sMIt001.js";import{S as te,a as se,b as ne,c as ae,d as H}from"./select-B8Pw9rS-.js";import{D as st,a as nt,b as at,c as ge}from"./dropdown-menu-DINyPmco.js";import{u as it}from"./useQuery-BNGphiae.js";import{u as rt}from"./useMutation-Bh5DVQPI.js";import{Q as Ge}from"./query-keys-3lmd-xp6.js";import{D as ie,a as re,b as oe,c as ce,e as ot}from"./dialog-DXjwjGKV.js";import{S as Ke,a as $e}from"./scroll-area-DKiYF9x5.js";import{S as ct}from"./skeleton-xUyFo20h.js";import{S as lt}from"./search-DHRhj6_i.js";import{S as mt}from"./settings-B2dEoYrB.js";import{D as je}from"./download-dYw-Dq4T.js";import{U as Ne}from"./upload-CZfvv05H.js";import{A as dt}from"./arrow-up-down-DJLsa2K1.js";import{P as ht}from"./plus-C1IEs-Ov.js";import{u as ut,g as pt,a as xt,b as gt,c as ft,d as _t,e as jt,f as De}from"./index-B_FCwlUM.js";import{D as Nt}from"./data-table-pagination-BKENav2P.js";import{C as He}from"./index-DemC1Nlr.js";import{T as be,a as ve,b as Z,c as _,d as ye,e as g}from"./table-BIu4Pah2.js";import{T as qe}from"./trash-2-C_5rhUMO.js";import{B as bt}from"./badge-gtDUxDTX.js";import{C as ke}from"./checkbox-DUpnJ1Rx.js";import{C as vt,a as yt}from"./create-combo-dialog-QDD3G_8l.js";import{C as Ct}from"./copy-Cfp_O40X.js";import{e as St,r as Be}from"./excel-export-DyHBmTdc.js";import{u as ze}from"./use-promotions-B0DWFL9P.js";import{utils as fe,writeFile as Tt}from"./xlsx-DkH2s96g.js";import{M as Dt}from"./multi-select-dropdown-D_FzXRUw.js";import{X as kt}from"./calendar-BiBi2kQF.js";import"./stores-api-BIve2jSO.js";import"./separator-ZoxOB1XH.js";import"./avatar-C98m2_SM.js";import"./search-context-DtMZc3QX.js";import"./command-BnLmWlRk.js";import"./createReactComponent-zh6rKAzG.js";import"./IconChevronRight-Bwbz4HuV.js";import"./IconSearch-CyZD7dtp.js";import"./createLucideIcon-CL0CQOA1.js";import"./chevron-right-BAjIoZMb.js";import"./react-icons.esm-DB-kGUq7.js";import"./popover-BtedB187.js";import"./index-Ct3V_iCU.js";import"./isSameMonth-C8JQo-AN.js";import"./index-Bh-UeytL.js";import"./index-DuT2Ibxp.js";import"./check-DcHT8QEO.js";import"./index-UJ-79IIJ.js";import"./utils-km2FGkQ4.js";import"./zod-B4gLZVLM.js";import"./date-utils-DBbLjCz0.js";import"./use-images-nkXjpNo3.js";import"./images-api-V0IeMkhY.js";import"./use-item-types-DA0U4OWS.js";import"./use-items-zU7JIOkv.js";import"./item-api-C8PXkgMG.js";import"./use-sources-CSlkwBP-.js";import"./sources-api-Cb3TooI2.js";import"./sources-CfiQ7039.js";import"./date-picker-8CCUFJO0.js";import"./calendar-BszJEazX.js";import"./textarea-BLrSKk17.js";import"./grip-vertical-Bo_DpjTT.js";import"./alert-dialog-CzdUByb-.js";import"./exceljs.min-BFFGgdR1.js";const Xe={getPackages:async t=>{const a=new URLSearchParams({company_uid:t.company_uid,brand_uid:t.brand_uid,list_store_uid:t.list_store_uid.join(","),...t.skip_limit&&{skip_limit:"true"}});return(await Te.get(`/mdata/v1/packages?${a}`)).data},updatePackagesSort:async t=>{await Te.post("/mdata/v1/package/sort",t)}},Pt=t=>it({queryKey:[Ge.PACKAGES_LIST,t],queryFn:()=>Xe.getPackages(t)}),Mt=t=>Pt({...t,skip_limit:!0}),It=()=>{const t=Qe(),{mutate:a,isPending:i}=rt({mutationFn:async l=>await Xe.updatePackagesSort(l),onSuccess:()=>{t.invalidateQueries({queryKey:[Ge.PACKAGES_LIST]}),M.success("Cập nhật thứ tự gói thành công")},onError:l=>{var b,d;const m=((d=(b=l==null?void 0:l.response)==null?void 0:b.data)==null?void 0:d.message)||"Có lỗi xảy ra khi cập nhật thứ tự gói";M.error(m)}});return{updateSort:a,isUpdating:i}},wt=8,Pe="text/plain",_e={title:"Sắp xếp combo",confirmText:"Lưu",description:"Thứ tự hiển thị các combo sẽ được áp dụng tại thiết bị bán hàng"},Ye={loadError:"Có lỗi xảy ra khi tải danh sách combo",noSources:"Không có combo nào để sắp xếp"},B={description:"text-muted-foreground text-sm",errorText:"text-sm text-red-600",emptyState:"py-8 text-center",grid:"grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4",draggableItem:"flex aspect-square cursor-move items-center justify-center bg-slate-300 p-2 transition-colors select-none hover:bg-slate-400",itemText:"text-center text-sm font-medium",skeletonContainer:"aspect-square bg-slate-300 p-2"};function Et(){return e.jsx("div",{className:B.grid,children:Array.from({length:wt}).map((t,a)=>e.jsx("div",{className:B.skeletonContainer,children:e.jsx(ct,{className:"h-4 w-full"})},a))})}function Ft(){return e.jsx("div",{className:B.emptyState,children:e.jsx("p",{className:B.errorText,children:Ye.loadError})})}function Ut(){return e.jsx("div",{className:B.emptyState,children:e.jsx("p",{className:B.description,children:Ye.noSources})})}function Lt({pkg:t,index:a,onDragStart:i,onDragOver:l,onDrop:m}){return e.jsx("div",{draggable:!0,onDragStart:b=>i(b,a),onDragOver:l,onDrop:b=>m(b,a),className:B.draggableItem,children:e.jsx("div",{className:B.itemText,children:t.package_name})},t.id)}function Ot(t){return f.useMemo(()=>t!=null&&t.data?t.data.sort((a,i)=>a.sort-i.sort):[],[t==null?void 0:t.data])}function At(t){const[a,i]=f.useState([]);return f.useEffect(()=>{i(t)},[t]),{sortedPackages:a,handleDragStart:(d,j)=>{d.dataTransfer.setData(Pe,j.toString())},handleDragOver:d=>{d.preventDefault()},handleDrop:(d,j)=>{d.preventDefault();const v=parseInt(d.dataTransfer.getData(Pe));if(v===j)return;const T=[...a],P=T[v];T.splice(v,1),T.splice(j,0,P),i(T)}}}function Rt({open:t,onOpenChange:a,storeUids:i}){const{company:l}=he(n=>n.auth),{selectedBrand:m}=Fe(),{data:b,isLoading:d,error:j}=Mt({company_uid:(l==null?void 0:l.id)||"",brand_uid:(m==null?void 0:m.id)||"",list_store_uid:i}),{updateSort:v,isUpdating:T}=It(),P=Ot(b),{sortedPackages:D,handleDragStart:k,handleDragOver:E,handleDrop:A}=At(P),I=d||T,w=D.length===0,o=()=>{const n={company_uid:(l==null?void 0:l.id)||"",brand_uid:(m==null?void 0:m.id)||"",list_data:D.map((r,u)=>({list_package_uid:r.list_package_uid,sort:u}))};v(n,{onSuccess:()=>a(!1)})},s=()=>d?e.jsx(Et,{}):j?e.jsx(Ft,{}):w?e.jsx(Ut,{}):e.jsx("div",{className:B.grid,children:D.map((n,r)=>e.jsx(Lt,{pkg:n,index:r,onDragStart:k,onDragOver:E,onDrop:A},n.id))});return e.jsx(ie,{open:t,onOpenChange:a,children:e.jsxs(re,{className:"flex h-[85vh] max-w-6xl flex-col sm:max-w-5xl",children:[e.jsx(oe,{className:"flex-shrink-0",children:e.jsx(ce,{children:_e.title})}),e.jsx("div",{className:"mb-4 flex-shrink-0",children:e.jsx("p",{className:B.description,children:_e.description})}),e.jsx("div",{className:"min-h-0 flex-1",children:e.jsxs(Ke,{className:"h-full w-full",children:[e.jsx("div",{className:"p-4",children:s()}),e.jsx($e,{orientation:"vertical"})]})}),e.jsx(ot,{className:"flex-shrink-0",children:e.jsx(K,{onClick:o,disabled:I,children:T?"Đang lưu...":_e.confirmText})})]})})}function Gt({searchTerm:t,setSearchTerm:a,selectedStore:i,setSelectedStore:l,selectedPromotion:m,setSelectedPromotion:b,expiryStatus:d,setExpiryStatus:j,onSearchKeyDown:v,onCreateCombo:T,onExportCombo:P,onImportCombo:D}){const[k,E]=f.useState(!1),{data:A=[]}=ee(),{data:I=[]}=Ue({storeUid:i==="all"?void 0:i}),w=()=>{E(!0)},o=A.map(s=>s.id);return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Combo"}),e.jsxs("div",{className:"relative max-w-[300px] min-w-[200px] flex-1",children:[e.jsx(lt,{className:"text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2"}),e.jsx(Re,{placeholder:"Tìm kiếm combo (nhấn Enter để tìm)",value:t,onChange:s=>a(s.target.value),onKeyDown:v,className:"pl-9"})]}),e.jsxs(te,{value:i,onValueChange:l,children:[e.jsx(se,{className:"w-[180px]",children:e.jsx(ne,{placeholder:"Chọn cửa hàng"})}),e.jsxs(ae,{children:[e.jsx(H,{value:"all",children:"Tất cả cửa hàng"}),A.map(s=>e.jsx(H,{value:s.id,children:s.name},s.id))]})]}),e.jsxs(te,{value:m,onValueChange:b,children:[e.jsx(se,{className:"w-[180px]",children:e.jsx(ne,{placeholder:"Chọn khuyến mãi"})}),e.jsxs(ae,{children:[e.jsx(H,{value:"all",children:"Tất cả khuyến mãi"}),I.map(s=>e.jsx(H,{value:s.promotion_id,children:s.promotion_name},s.promotion_id))]})]}),e.jsxs(te,{value:d,onValueChange:s=>j(s),children:[e.jsx(se,{className:"w-[180px]",children:e.jsx(ne,{placeholder:"Trạng thái"})}),e.jsxs(ae,{children:[e.jsx(H,{value:"all",children:"Tất cả ngày áp dụng"}),e.jsx(H,{value:"expired",children:"Hết hạn"}),e.jsx(H,{value:"unexpired",children:"Chưa hết hạn"})]})]}),e.jsxs("div",{className:"ml-auto flex items-center gap-2",children:[e.jsxs(st,{children:[e.jsx(nt,{asChild:!0,children:e.jsxs(K,{variant:"outline",className:"flex items-center gap-2",children:[e.jsx(mt,{className:"h-4 w-4"}),"Tiện ích"]})}),e.jsxs(at,{align:"end",children:[e.jsxs(ge,{onClick:P,children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),"Xuất, sửa combo"]}),e.jsxs(ge,{onClick:D,children:[e.jsx(Ne,{className:"mr-2 h-4 w-4"}),"Thêm combo từ file"]}),e.jsxs(ge,{onClick:w,children:[e.jsx(dt,{className:"mr-2 h-4 w-4"}),"Sắp xếp combo"]})]})]}),e.jsxs(K,{onClick:T,className:"flex items-center gap-2",children:[e.jsx(ht,{className:"h-4 w-4"}),"Tạo combo"]})]})]}),e.jsx(Rt,{open:k,onOpenChange:E,storeUids:o})]})}function Kt({columns:t,data:a,onRowClick:i}){var G;const[l,m]=f.useState({}),[b,d]=f.useState({}),[j,v]=f.useState([]),[T,P]=f.useState([]),[D,k]=f.useState(!1),E=We(),{mutate:A,isPending:I}=Le(),w=(c,h)=>{h.target.closest('button, [role="dialog"], .modal, input, select, textarea')||(i?i(c):E({to:"/sale/combo/detail/$id",params:{id:c.id}}))},o=ut({data:a,columns:t,state:{sorting:T,columnVisibility:b,rowSelection:l,columnFilters:j},enableRowSelection:!0,onRowSelectionChange:m,onSortingChange:P,onColumnFiltersChange:v,onColumnVisibilityChange:d,getCoreRowModel:jt(),getFilteredRowModel:_t(),getPaginationRowModel:ft(),getSortedRowModel:gt(),getFacetedRowModel:xt(),getFacetedUniqueValues:pt()}),s=o.getFilteredSelectedRowModel().rows,n=s.length,r=()=>{k(!0)},u=()=>{const c=s.map(h=>h.original.id);A({packageUids:c},{onSuccess:()=>{m({}),k(!1)}})},y=()=>{k(!1)};return e.jsxs("div",{className:"space-y-4",children:[n>0&&e.jsx("div",{className:"bg-muted/50 flex items-center justify-start rounded-md border p-3",children:e.jsxs(K,{variant:"destructive",size:"sm",onClick:r,className:"h-8",disabled:I,children:[e.jsx(qe,{className:"mr-2 h-4 w-4"}),I?"Đang xóa...":"Xóa combo"]})}),e.jsxs(Ke,{className:"rounded-md border",children:[e.jsxs(be,{className:"relative",children:[e.jsx(ve,{children:o.getHeaderGroups().map(c=>e.jsx(Z,{children:c.headers.map(h=>e.jsx(_,{colSpan:h.colSpan,children:h.isPlaceholder?null:De(h.column.columnDef.header,h.getContext())},h.id))},c.id))}),e.jsx(ye,{children:(G=o.getRowModel().rows)!=null&&G.length?o.getRowModel().rows.map(c=>e.jsx(Z,{"data-state":c.getIsSelected()&&"selected",className:"cursor-pointer hover:bg-gray-50",onClick:h=>{const F=c.original;w(F,h)},children:c.getVisibleCells().map(h=>e.jsx(g,{children:De(h.column.columnDef.cell,h.getContext())},h.id))},c.id)):e.jsx(Z,{children:e.jsx(g,{colSpan:t.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex flex-col items-center justify-center space-y-2",children:[e.jsx("p",{className:"text-muted-foreground",children:"Không có combo nào"}),e.jsx("p",{className:"text-muted-foreground text-sm",children:"Hãy tạo combo mới để bắt đầu quản lý"})]})})})})]}),e.jsx($e,{orientation:"horizontal"})]}),e.jsx(Nt,{table:o}),n>0&&e.jsx("div",{className:"py-4",children:e.jsx("div",{className:"text-muted-foreground text-sm",children:e.jsxs("span",{children:["Đã chọn ",n," trong ",o.getFilteredRowModel().rows.length," combo"]})})}),e.jsx(He,{open:D,onOpenChange:k,title:"Lưu ý: Xóa combo có thể ảnh hưởng đến dữ liệu đang vận hành tại POS",content:`Bạn có muốn xoá ${n} combo đã chọn ?`,confirmText:"Xóa",cancelText:"Hủy",onConfirm:u,onCancel:y})]})}function $t(t,a){const i=Date.now();if(a<i)return e.jsx(bt,{variant:"destructive",className:"text-xs",children:"Hết hạn"});const l=new Date(t).toLocaleDateString("vi-VN"),m=new Date(a).toLocaleDateString("vi-VN");return e.jsx("span",{children:`${l} - ${m}`})}function Ht(t){return t.stores===1?"1 cửa hàng":`${t.stores} cửa hàng`}function qt({combo:t}){const[a,i]=f.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx(K,{variant:"ghost",size:"sm",onClick:l=>{l.stopPropagation(),i(!0)},className:"h-8 w-8 p-0",children:e.jsx(Ct,{className:"h-4 w-4"})}),e.jsx(vt,{open:a,onOpenChange:i,combo:t})]})}function Bt({combo:t}){const[a,i]=f.useState(!1),{mutate:l,isPending:m}=Le(),b=d=>{d&&d.stopPropagation(),l({packageUids:[t.id]},{onSuccess:()=>{i(!1)}})};return e.jsxs(e.Fragment,{children:[e.jsx(K,{variant:"ghost",size:"sm",onClick:d=>{d.stopPropagation(),i(!0)},className:"h-8 w-8 p-0 text-red-600 hover:bg-red-50 hover:text-red-700",children:e.jsx(qe,{className:"h-4 w-4"})}),e.jsx(He,{open:a,onOpenChange:i,title:"Xác nhận xóa combo",content:`Bạn có chắc chắn muốn xóa combo "${t.package_name}"? Hành động này không thể hoàn tác.`,confirmText:"Xóa",cancelText:"Hủy",onConfirm:b,isLoading:m})]})}const zt=()=>[{id:"select",header:({table:t})=>e.jsx(ke,{checked:t.getIsAllPageRowsSelected(),onCheckedChange:a=>t.toggleAllPageRowsSelected(!!a),"aria-label":"Select all",onClick:a=>a.stopPropagation()}),cell:({row:t})=>e.jsx(ke,{checked:t.getIsSelected(),onCheckedChange:a=>t.toggleSelected(!!a),"aria-label":"Select row",onClick:a=>a.stopPropagation()}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"index",header:"#",cell:({row:t,table:a})=>{const i=a.getState().pagination.pageIndex,l=a.getState().pagination.pageSize;return i*l+t.index+1},size:60},{accessorKey:"package_name",header:"Tên combo",cell:({row:t})=>{const a=t.original;return e.jsx("div",{className:"font-medium",children:a.package_name})},size:200},{accessorKey:"ots_value",header:"Giá",cell:({row:t})=>{const a=t.original;return e.jsx("div",{className:"text-sm font-medium",children:new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(a.ots_value)})},size:120},{accessorKey:"stores",header:"Cửa hàng",cell:({row:t})=>{const a=t.original;return e.jsx("div",{className:"text-sm",children:Ht(a)})},size:120},{accessorKey:"date_range",header:"Thời gian áp dụng",cell:({row:t})=>{const a=t.original;return e.jsx("div",{className:"text-sm",children:$t(a.from_date,a.to_date)})},size:150},{id:"copy",header:"Sao chép",cell:({row:t})=>e.jsx(qt,{combo:t.original}),enableSorting:!1,size:80},{id:"delete",header:"",cell:({row:t})=>e.jsx(Bt,{combo:t.original}),enableSorting:!1,size:60}],Me=t=>{if(!t)return"";const a=typeof t=="string"?parseInt(t):t;if(isNaN(a))return"";const i=[];for(let l=0;l<24;l++)a&1<<l&&i.push(`${l}h`);return i.join(", ")},Ie=t=>{if(!t)return"";const a=typeof t=="string"?parseInt(t):t;if(isNaN(a))return"";const i=[{value:2,name:"Chủ nhật"},{value:4,name:"Thứ 2"},{value:8,name:"Thứ 3"},{value:16,name:"Thứ 4"},{value:32,name:"Thứ 5"},{value:64,name:"Thứ 6"},{value:128,name:"Thứ 7"}],l=[];return i.forEach(m=>{a&m.value&&l.push(m.name)}),l.join(", ")};function Xt({open:t,onOpenChange:a,data:i,onConfirm:l,isLoading:m=!1,title:b="Xuất, sửa combo"}){const{company:d,brands:j}=he(o=>o.auth),{data:v=[]}=ee(),{apiPromotions:T=[]}=ze({enabled:!0}),[P,D]=f.useState(!1),k=()=>{a(!1)},E=async()=>{var o;D(!0);try{if(!i||i.length===0){M.error("Không có dữ liệu để cập nhật");return}if(!(d!=null&&d.id)||!((o=j==null?void 0:j[0])!=null&&o.id)){M.error("Thiếu thông tin công ty hoặc thương hiệu");return}const s=i.map((n,r)=>{var F;const u=()=>n.combo_code&&n.combo_code.trim()!==""?n.combo_code.trim():`${n.name?n.name.replace(/\s+/g,"-").toUpperCase():"COMBO"}-${Date.now()}-${r}`,G=(()=>{var U,L;if(!n.store_name)return((U=v[0])==null?void 0:U.id)||"";const x=v.find(N=>N.name===n.store_name);return x?x.id:((L=v[0])==null?void 0:L.id)||""})(),h=((x,U)=>{if(!x)return null;const L=String(x).trim(),N=T.find(S=>String(S.promotion_name).trim()===L);if(!N)return null;const p=(N.promotions||[]).find(S=>S.store_uid===U);return(p==null?void 0:p.promotion_uid)||null})(n.promotion_name,G);return{company_uid:(d==null?void 0:d.id)||"",brand_uid:((F=j==null?void 0:j[0])==null?void 0:F.id)||"",package_name:n.name,package_id:u(),image_path:n.image||null,image_path_thumb:n.image||null,vat_tax_rate:Number(n.vat_percent||0)/100,ots_value:Number(n.price||0),ta_value:Number(n.price||0),from_date:A(n.start_date),to_date:A(n.end_date),time_sale_date_week:I(n.apply_date),time_sale_hour_day:w(n.apply_time),use_same_data:0,deleted:!1,package_detail:{LstItem_Options:[{Name:n.group_name,Min_Permitted:Number(n.require_selection)||0,Max_Permitted:Number(n.selection_limit)||0,LstItem:n.group_item_codes?[{item_id:n.group_item_codes,ots_price:n.item_price||null,ta_price:n.item_price||null,item_name:n.name,state_change_price:1}]:[]}]},...h?{promotion_uid:h}:{},store_uid:G}});await Oe.updateCombos(s),M.success("Cập nhật combo thành công!"),l()}catch{M.error("Có lỗi xảy ra khi cập nhật combo")}finally{D(!1)}},A=o=>{if(!o)return 0;if(typeof o=="number")return o;const s=String(o);try{const[n,r,u]=s.split("-");return new Date(parseInt(u),parseInt(r)-1,parseInt(n)).getTime()}catch{return 0}},I=o=>{if(!o)return 0;if(typeof o=="number")return o;const s=String(o),n={"Chủ nhật":2,"Thứ 2":4,"Thứ 3":8,"Thứ 4":16,"Thứ 5":32,"Thứ 6":64,"Thứ 7":128};let r=0;return s.split(", ").forEach(u=>{n[u.trim()]&&(r+=n[u.trim()])}),r},w=o=>{if(!o)return 0;if(typeof o=="number")return o;const s=String(o);let n=0;return s.split(", ").forEach(r=>{const u=parseInt(r.replace("h",""));!isNaN(u)&&u>=0&&u<=23&&(n+=1<<u)}),n};return e.jsx(ie,{open:t,onOpenChange:a,children:e.jsxs(re,{className:"flex h-[90vh] w-[95vw] max-w-6xl flex-col lg:max-w-7xl",children:[e.jsx(oe,{className:"flex-shrink-0",children:e.jsx(ce,{children:b})}),e.jsx("div",{className:"flex min-h-0 flex-1 flex-col",children:e.jsx("div",{className:"flex-1 overflow-auto",children:e.jsx("div",{className:"min-w-max",children:e.jsxs(be,{children:[e.jsx(ve,{children:e.jsxs(Z,{children:[e.jsx(_,{className:"bg-background sticky left-0 w-[120px]",children:"Tên"}),e.jsx(_,{className:"w-[200px]",children:"Cửa hàng"}),e.jsx(_,{className:"w-[80px]",children:"Giá"}),e.jsx(_,{className:"w-[150px]",children:"CTKM"}),e.jsx(_,{className:"w-[120px]",children:"Mã Combo"}),e.jsx(_,{className:"w-[80px]",children:"VAT (%)"}),e.jsx(_,{className:"w-[120px]",children:"Ngày bắt đầu"}),e.jsx(_,{className:"w-[120px]",children:"Ngày kết thúc"}),e.jsx(_,{className:"w-[120px]",children:"Ngày áp dụng"}),e.jsx(_,{className:"w-[120px]",children:"Giờ áp dụng"}),e.jsx(_,{className:"w-[80px]",children:"Ảnh"}),e.jsx(_,{className:"w-[120px]",children:"Tên nhóm"}),e.jsx(_,{className:"w-[120px]",children:"Yêu cầu chọn"}),e.jsx(_,{className:"w-[120px]",children:"Giới hạn chọn"}),e.jsx(_,{className:"w-[150px]",children:"Mã món theo nhóm"}),e.jsx(_,{className:"w-[120px]",children:"Giá món con"})]})}),e.jsx(ye,{children:i.map((o,s)=>{var n,r;return e.jsxs(Z,{children:[e.jsx(g,{className:"bg-background sticky left-0 font-medium",children:o.name}),e.jsx(g,{className:"max-w-[200px] truncate",title:o.store_name,children:o.store_name}),e.jsx(g,{children:(n=o.price)==null?void 0:n.toLocaleString()}),e.jsx(g,{className:"max-w-[150px] truncate",title:o.promotion_name,children:o.promotion_name}),e.jsx(g,{children:o.combo_code}),e.jsx(g,{children:o.vat_percent}),e.jsx(g,{children:o.start_date}),e.jsx(g,{children:o.end_date}),e.jsx(g,{className:"max-w-[120px] truncate",title:Ie(o.apply_date),children:Ie(o.apply_date)}),e.jsx(g,{className:"max-w-[120px] truncate",title:Me(o.apply_time),children:Me(o.apply_time)}),e.jsx(g,{children:o.image&&e.jsx("img",{src:o.image,alt:"combo",className:"h-8 w-8 rounded object-cover"})}),e.jsx(g,{children:o.group_name}),e.jsx(g,{children:o.require_selection}),e.jsx(g,{children:o.selection_limit}),e.jsx(g,{className:"max-w-[150px] truncate",title:o.group_item_codes,children:o.group_item_codes}),e.jsx(g,{children:(r=o.item_price)==null?void 0:r.toLocaleString()})]},s)})})]})})})}),e.jsxs("div",{className:"mt-4 flex flex-shrink-0 items-center justify-between border-t pt-4",children:[e.jsx(K,{variant:"outline",onClick:k,children:"Đóng"}),e.jsx(K,{onClick:E,disabled:m||P,children:m||P?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"}),"Đang xử lý..."]}):"Lưu"})]})]})})}function Yt({open:t,onOpenChange:a}){const[i,l]=f.useState(""),[m,b]=f.useState(""),[d,j]=f.useState(null),[v,T]=f.useState(!1),[P,D]=f.useState(!1),[k,E]=f.useState([]),[A,I]=f.useState(!1),{data:w=[]}=ee(),o=Je(),{company:s}=he(h=>h.auth),{brands:n}=Fe(),{refetch:r}=Ae({storeUid:i==="all"?w.map(h=>h.id):i,status:m,enabled:!1,skipLimit:!0}),{refetch:u}=Ue({storeUid:i==="all"?void 0:i,enabled:!1,skipLimit:!0,aggregate:!0}),y=async h=>{var x;const F=(x=h.target.files)==null?void 0:x[0];if(F){j(F);try{const L=(await Be(F)).map(N=>({id:N.ID||"",name:N.Tên||"",store_name:N["Cửa hàng áp dụng"]||"",price:parseFloat(N.Giá)||0,promotion_name:N.CTKM||"",combo_code:N["Mã Combo"]||"",vat_percent:parseFloat(N["VAT (%)"])||0,start_date:N["Ngày bắt đầu"]||"",end_date:N["Ngày kết thúc"]||"",apply_date:N["Ngày áp dụng"]||"",apply_time:N["Giờ áp dụng"]||"",image:N.Ảnh||"",group_name:N["Tên nhóm"]||"",require_selection:N["Yêu cầu chọn"]||"",selection_limit:parseInt(N["Giới hạn chọn"])||0,group_item_codes:N["Mã món theo nhóm"]||"",item_price:parseFloat(N["Giá món con"])||0}));E(L),I(!0)}catch(U){console.error("Error reading file:",U),M.error("Không thể đọc file Excel")}}},G=async()=>{if(!i||!m){M.error("Vui lòng chọn cửa hàng và trạng thái");return}D(!0);try{const[h,F]=await Promise.all([r(),u()]),x=h.data||[],U=F.data||[],L=new Map;U.forEach(C=>{L.set(C.promotion_uid||C.promotion_id,C.promotion_name||C.name)});const N=x.map(C=>{var R,Q,q,X,$,xe,Ce;const p=Array.isArray(C.list_store_uid)?C.list_store_uid.join(","):C.list_store_uid||C.id||"",S=w.filter(O=>{var Y;return(Y=C.list_store_uid)==null?void 0:Y.includes(O.id)}).map(O=>O.name).join(",")||"",V=O=>{if(!O)return"";try{return new Date(O).toLocaleDateString("vi-VN",{day:"2-digit",month:"2-digit",year:"numeric"}).replace(/\//g,"-")}catch{return""}},z=((Q=(R=C.package_detail)==null?void 0:R.LstItem_Options)==null?void 0:Q[0])||{},ue=((q=z.LstItem)==null?void 0:q.map(O=>O.item_id).join(","))||"",pe=(($=(X=z.LstItem)==null?void 0:X[0])==null?void 0:$.ta_price)||((Ce=(xe=z.LstItem)==null?void 0:xe[0])==null?void 0:Ce.ots_price)||0,le=O=>{if(!O)return"";const Y=typeof O=="string"?parseInt(O):O;if(isNaN(Y))return"";const de=[];for(let J=0;J<24;J++)Y&1<<J&&de.push(`${J}h`);return de.join(", ")},me=O=>{if(!O)return"";const Y=typeof O=="string"?parseInt(O):O;if(isNaN(Y))return"";const de=[{value:2,name:"Chủ nhật"},{value:4,name:"Thứ 2"},{value:8,name:"Thứ 3"},{value:16,name:"Thứ 4"},{value:32,name:"Thứ 5"},{value:64,name:"Thứ 6"},{value:128,name:"Thứ 7"}],J=[];return de.forEach(Se=>{Y&Se.value&&J.push(Se.name)}),J.join(", ")};return{id:p,name:C.package_name||"",store_name:S,price:C.ots_value||C.ta_value||0,promotion_name:L.get(C.promotion_uid)||"",combo_code:C.package_id||"",vat_percent:(C.vat_tax_rate||0)*100,start_date:V(C.from_date),end_date:V(C.to_date),apply_date:me(C.time_sale_date_week)||"",apply_time:le(C.time_sale_hour_day)||"",image:C.image_path||"",group_name:z.Name||"",require_selection:z.Min_Permitted||"",selection_limit:z.Max_Permitted||0,group_item_codes:ue,item_price:pe}});await St(N,"update-combo.xlsx"),M.success("Tải file thành công!")}catch(h){console.error("Download failed:",h),M.error("Có lỗi xảy ra khi tải file")}finally{D(!1)}},c=async()=>{T(!0);try{const F=(await u()).data||[],x=new Map;F.forEach(p=>{const S=p.promotion_name||p.name,V=p.promotion_uid||p.id;S&&V&&x.set(S,V)});const L=(await r()).data||[],N=new Map;L.forEach(p=>{p.package_name&&N.set(p.package_name,p)});const C=k.map(p=>{var me;if(!p.name||p.name.trim()==="")return null;const S=N.get(p.name);if(!S)return console.warn(`No existing combo found for: ${p.name}`),null;const z=(()=>{if(i&&i!=="all")return i;if(w&&w.length>0)return w[0].id;throw new Error("No store available for combo update")})(),ue=R=>{if(!R)return 0;if(typeof R=="number")return R;const Q=String(R),q={"Chủ nhật":2,"Thứ 2":4,"Thứ 3":8,"Thứ 4":16,"Thứ 5":32,"Thứ 6":64,"Thứ 7":128};let X=0;return Q.split(", ").forEach($=>{q[$.trim()]&&(X+=q[$.trim()])}),X},pe=R=>{if(!R)return 0;if(typeof R=="number")return R;const Q=String(R);let q=0;return Q.split(", ").forEach(X=>{const $=parseInt(X.replace("h",""));!isNaN($)&&$>=0&&$<=23&&(q+=1<<$)}),q},le=R=>{if(!R)return 0;if(typeof R=="number")return R;const Q=String(R);try{const[q,X,$]=Q.split("-");return new Date(parseInt($),parseInt(X)-1,parseInt(q)).getTime()}catch{return 0}};return{...S,package_name:p.name,ots_value:p.price||S.ots_value,ta_value:p.price||S.ta_value,from_date:le(p.start_date)||S.from_date,to_date:le(p.end_date)||S.to_date,time_sale_hour_day:pe(p.apply_time)||S.time_sale_hour_day,time_sale_date_week:ue(p.apply_date)||S.time_sale_date_week,vat_tax_rate:p.vat_percent/100||S.vat_tax_rate,promotion_uid:x.get(p.promotion_name)||S.promotion_uid,store_uid:z,ps_store_uid:z,brand_uid:S.brand_uid||((me=n==null?void 0:n[0])==null?void 0:me.id),company_uid:(s==null?void 0:s.id)||S.company_uid,package_detail:S.package_detail||{LstItem_Options:[{Name:p.group_name||"",Min_Permitted:p.require_selection||0,Max_Permitted:p.selection_limit||0,LstItem:[]}]}}}).filter(p=>p!==null);await o.mutateAsync(C),M.success("Import thành công!"),I(!1),a(!1),l(""),b(""),j(null),E([])}catch(h){console.error("Import failed:",h),M.error("Có lỗi xảy ra khi import")}finally{T(!1)}};return e.jsxs(ie,{open:t,onOpenChange:a,children:[e.jsxs(re,{className:"max-w-md",children:[e.jsx(oe,{children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(ce,{children:"Xuất, sửa combo"})})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"font-medium",children:"Bước 1. Chỉnh bộ lọc để xuất file"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(W,{htmlFor:"store-select",children:"Tất cả điểm"}),e.jsxs(te,{value:i,onValueChange:l,children:[e.jsx(se,{id:"store-select",className:"w-full",children:e.jsx(ne,{placeholder:"Chọn cửa hàng"})}),e.jsxs(ae,{children:[e.jsx(H,{value:"all",children:"Tất cả điểm"}),w.map(h=>e.jsx(H,{value:h.id,children:h.name},h.id))]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(W,{htmlFor:"brand-select",children:"Chưa hết hạn"}),e.jsxs(te,{value:m,onValueChange:b,children:[e.jsx(se,{id:"brand-select",className:"w-full",children:e.jsx(ne,{placeholder:"Chọn trạng thái"})}),e.jsxs(ae,{children:[e.jsx(H,{value:"all",children:"Tất cả"}),e.jsx(H,{value:"unexpired",children:"Chưa hết hạn"}),e.jsx(H,{value:"expired",children:"Hết hạn"})]})]})]})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"font-medium",children:"Bước 2. Tải file dữ liệu"}),e.jsx(K,{variant:"outline",onClick:G,disabled:!i||!m||P,className:"w-full justify-start",children:P?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-current"}),"Đang tải..."]}):e.jsxs(e.Fragment,{children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),"Tải xuống"]})})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsx("p",{className:"text-muted-foreground text-sm",children:"Không sửa các cột:"}),e.jsx("p",{className:"text-xs text-blue-600",children:"ID, Cửa hàng áp dụng, Mã Combo."})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"font-medium",children:"Bước 4. Tải file lên"}),e.jsx("p",{className:"text-muted-foreground text-sm",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Re,{type:"file",accept:".xlsx,.xls,.csv",onChange:y,className:"hidden",id:"file-upload"}),e.jsxs(W,{htmlFor:"file-upload",className:"border-input bg-background ring-offset-background placeholder:text-muted-foreground hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring flex cursor-pointer items-center gap-2 rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",children:[e.jsx(Ne,{className:"h-4 w-4"}),d?d.name:"Chọn file"]})]})})]})]})]}),e.jsx(Xt,{open:A,onOpenChange:I,data:k,onConfirm:c,isLoading:v,title:"Xuất, sửa combo"})]})}const we=t=>{if(!t)return"";const a=typeof t=="string"?parseInt(t):t;if(isNaN(a))return"";const i=[];for(let l=0;l<24;l++)a&1<<l&&i.push(`${l}h`);return i.join(", ")},Ee=t=>{if(!t)return"";const a=typeof t=="string"?parseInt(t):t;if(isNaN(a))return"";const i=[{value:2,name:"Chủ nhật"},{value:4,name:"Thứ 2"},{value:8,name:"Thứ 3"},{value:16,name:"Thứ 4"},{value:32,name:"Thứ 5"},{value:64,name:"Thứ 6"},{value:128,name:"Thứ 7"}],l=[];return i.forEach(m=>{a&m.value&&l.push(m.name)}),l.join(", ")};function Qt({open:t,onOpenChange:a,data:i,onConfirm:l,isLoading:m=!1,title:b="Thêm combo",selectedStoreIds:d=[]}){const{company:j,brands:v}=he(s=>s.auth),{data:T=[]}=ee(),{apiPromotions:P=[]}=ze({enabled:!0}),[D,k]=f.useState(!1),E=()=>{a(!1)},A=async()=>{var s;k(!0);try{if(!i||i.length===0){M.error("Không có dữ liệu để import");return}if(!(j!=null&&j.id)||!((s=v==null?void 0:v[0])!=null&&s.id)){M.error("Thiếu thông tin công ty hoặc thương hiệu");return}const n=i.flatMap((r,u)=>{const y=()=>r.combo_code&&r.combo_code.trim()!==""?r.combo_code.trim():`${r.name?r.name.replace(/\s+/g,"-").toUpperCase():"COMBO"}-${Date.now()}-${u}`,c=(()=>{var x;if(d&&d.length>0)return d;if(r.store_name){const U=T.find(L=>L.name===r.store_name);if(U)return[U.id]}return(x=T[0])!=null&&x.id?[T[0].id]:[]})(),h=(x,U)=>{if(!x)return null;const L=String(x).trim(),N=P.find(S=>String(S.promotion_name).trim()===L);if(!N)return null;const p=(N.promotions||[]).find(S=>S.store_uid===U);return(p==null?void 0:p.promotion_uid)||null},F=y();return c.map(x=>{var L;const U=h(r.promotion_name,x);return{company_uid:(j==null?void 0:j.id)||"",brand_uid:((L=v==null?void 0:v[0])==null?void 0:L.id)||"",package_name:r.name,package_id:F,image_path:r.image||null,image_path_thumb:r.image||null,vat_tax_rate:Number(r.vat_percent||0)/100,ots_value:Number(r.price||0),ta_value:Number(r.price||0),from_date:I(r.start_date),to_date:I(r.end_date),time_sale_date_week:w(r.apply_date),time_sale_hour_day:o(r.apply_time),use_same_data:0,deleted:!1,package_detail:{LstItem_Options:[{Name:r.group_name,Min_Permitted:Number(r.require_selection)||0,Max_Permitted:Number(r.selection_limit)||0,LstItem:r.group_item_codes?[{item_id:r.group_item_codes,ots_price:r.item_price||null,ta_price:r.item_price||null,item_name:r.name,state_change_price:1}]:[]}]},...U?{promotion_uid:U}:{},store_uid:x}})});await Oe.createCombosFromImport(n),M.success("Tạo combo thành công!"),l()}catch{M.error("Có lỗi xảy ra khi tạo combo")}finally{k(!1)}},I=s=>{if(!s)return 0;if(typeof s=="number")return s;const n=String(s);try{const[r,u,y]=n.split("-");return new Date(parseInt(y),parseInt(u)-1,parseInt(r)).getTime()}catch{return 0}},w=s=>{if(!s)return 0;if(typeof s=="number")return s;const n=String(s),r={"Chủ nhật":2,"Thứ 2":4,"Thứ 3":8,"Thứ 4":16,"Thứ 5":32,"Thứ 6":64,"Thứ 7":128};let u=0;return n.split(", ").forEach(y=>{r[y.trim()]&&(u+=r[y.trim()])}),u},o=s=>{if(!s)return 0;if(typeof s=="number")return s;const n=String(s);let r=0;return n.split(", ").forEach(u=>{const y=parseInt(u.replace("h",""));!isNaN(y)&&y>=0&&y<=23&&(r+=1<<y)}),r};return e.jsx(ie,{open:t,onOpenChange:a,children:e.jsxs(re,{className:"flex h-[90vh] w-[95vw] max-w-6xl flex-col lg:max-w-7xl",children:[e.jsx(oe,{className:"flex-shrink-0",children:e.jsx(ce,{children:b})}),e.jsx("div",{className:"flex min-h-0 flex-1 flex-col",children:e.jsx("div",{className:"flex-1 overflow-auto",children:e.jsx("div",{className:"min-w-max",children:e.jsxs(be,{children:[e.jsx(ve,{children:e.jsxs(Z,{children:[e.jsx(_,{className:"bg-background sticky left-0 w-[120px]",children:"Tên"}),e.jsx(_,{className:"w-[200px]",children:"Cửa hàng"}),e.jsx(_,{className:"w-[80px]",children:"Giá"}),e.jsx(_,{className:"w-[150px]",children:"CTKM"}),e.jsx(_,{className:"w-[120px]",children:"Mã Combo"}),e.jsx(_,{className:"w-[80px]",children:"VAT (%)"}),e.jsx(_,{className:"w-[120px]",children:"Ngày bắt đầu"}),e.jsx(_,{className:"w-[120px]",children:"Ngày kết thúc"}),e.jsx(_,{className:"w-[120px]",children:"Ngày áp dụng"}),e.jsx(_,{className:"w-[120px]",children:"Giờ áp dụng"}),e.jsx(_,{className:"w-[80px]",children:"Ảnh"}),e.jsx(_,{className:"w-[120px]",children:"Tên nhóm"}),e.jsx(_,{className:"w-[120px]",children:"Yêu cầu chọn"}),e.jsx(_,{className:"w-[120px]",children:"Giới hạn chọn"}),e.jsx(_,{className:"w-[150px]",children:"Mã món theo nhóm"}),e.jsx(_,{className:"w-[120px]",children:"Giá món con"})]})}),e.jsx(ye,{children:i.map((s,n)=>{var r,u;return e.jsxs(Z,{children:[e.jsx(g,{className:"bg-background sticky left-0 font-medium",children:s.name}),e.jsx(g,{className:"max-w-[200px] truncate",title:s.store_name,children:s.store_name}),e.jsx(g,{children:(r=s.price)==null?void 0:r.toLocaleString()}),e.jsx(g,{className:"max-w-[150px] truncate",title:s.promotion_name,children:s.promotion_name}),e.jsx(g,{children:s.combo_code}),e.jsx(g,{children:s.vat_percent}),e.jsx(g,{children:s.start_date}),e.jsx(g,{children:s.end_date}),e.jsx(g,{className:"max-w-[120px] truncate",title:Ee(s.apply_date),children:Ee(s.apply_date)}),e.jsx(g,{className:"max-w-[120px] truncate",title:we(s.apply_time),children:we(s.apply_time)}),e.jsx(g,{children:s.image&&e.jsx("img",{src:s.image,alt:"combo",className:"h-8 w-8 rounded object-cover"})}),e.jsx(g,{children:s.group_name}),e.jsx(g,{children:s.require_selection}),e.jsx(g,{children:s.selection_limit}),e.jsx(g,{className:"max-w-[150px] truncate",title:s.group_item_codes,children:s.group_item_codes}),e.jsx(g,{children:(u=s.item_price)==null?void 0:u.toLocaleString()})]},n)})})]})})})}),e.jsxs("div",{className:"mt-4 flex flex-shrink-0 items-center justify-between border-t pt-4",children:[e.jsx(K,{variant:"outline",onClick:E,children:"Đóng"}),e.jsx(K,{onClick:A,disabled:m||D,children:m||D?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"}),"Đang xử lý..."]}):"Lưu"})]})]})})}function Wt({open:t,onOpenChange:a}){const[i,l]=f.useState([]),[m,b]=f.useState(null),[d,j]=f.useState(!1),[v,T]=f.useState([]),[P,D]=f.useState(!1),{data:k=[]}=ee(),E=()=>{a(!1),l([]),b(null)},A=async s=>{var r;const n=(r=s.target.files)==null?void 0:r[0];if(n&&i.length>0){b(n),j(!0);try{const u=await Be(n);console.log("Raw Excel data:",u);const y=k.filter(c=>i.includes(c.id)).map(c=>c.name),G=u.map((c,h)=>({id:`temp-${h}`,name:c.Tên||c.name||"",store_name:y.length===k.length?"Tất cả cửa hàng":y.length>1?`Đã chọn ${y.length} cửa hàng`:y[0]||"",price:Number(c.Giá||c.price||0),promotion_name:c.CTKM||c.promotion_name||"",combo_code:c["Mã Combo"]||c.combo_code||"",vat_percent:Number(c["VAT (%)"]||c.vat_percent||0),start_date:c["Ngày bắt đầu"]||c.start_date||"",end_date:c["Ngày kết thúc"]||c.end_date||"",apply_date:c["Ngày áp dụng"]||c.apply_date||"",apply_time:c["Giờ áp dụng"]||c.apply_time||"",image:c.Ảnh||c.image||"",group_name:c["Tên nhóm"]||c.group_name||"",require_selection:String(c["Yêu cầu chọn"]||c.require_selection||""),selection_limit:Number(c["Giới hạn chọn"]||c.selection_limit||0),group_item_codes:c["Mã món theo nhóm"]||c.group_item_codes||"",item_price:Number(c["Giá món con"]||c.item_price||0)})).filter(c=>c.name&&c.name.trim()!=="");console.log("Mapped data:",G),T(G),D(!0),M.success("Đã tải file thành công!")}catch(u){console.error("Upload failed:",u),M.error("Có lỗi xảy ra khi đọc file"),b(null)}finally{j(!1)}}else n&&i.length===0&&(M.error("Vui lòng chọn cửa hàng trước"),s.target.value="")},I=()=>{const s=["Tên","Giá","CTKM","Mã Combo","VAT (%)","Ngày bắt đầu","Ngày kết thúc","Ngày áp dụng","Giờ áp dụng","Ảnh","Tên nhóm","Yêu cầu chọn","Giới hạn chọn","Mã món theo nhóm","Giá món con"],n=fe.aoa_to_sheet([s]),r=fe.book_new();fe.book_append_sheet(r,n,"Template Combo");const u=s.map(y=>({wch:y.length+5}));n["!cols"]=u,Tt(r,"create-combo.xlsx"),M.success("Đã tải file mẫu thành công")},w=()=>{D(!1),T([])},o=()=>{D(!1),E(),M.success("Import combo thành công!")};return e.jsxs(ie,{open:t,onOpenChange:a,children:[e.jsxs(re,{className:"max-w-md",children:[e.jsx(oe,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(ce,{children:"Thêm combo"}),e.jsx(K,{variant:"ghost",size:"sm",onClick:E,className:"h-6 w-6 p-0",children:e.jsx(kt,{className:"h-4 w-4"})})]})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx(W,{className:"text-sm font-medium",children:"Bước 1. Tải file mẫu"}),e.jsxs(K,{variant:"outline",onClick:I,className:"w-full justify-start",children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),"Tải xuống"]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(W,{className:"text-sm font-medium",children:"Bước 2. Chọn cửa hàng áp dụng"}),e.jsx(Dt,{options:k.map(s=>({value:s.id,label:s.name})),value:i,onValueChange:l,placeholder:"Chọn cửa hàng áp dụng",searchPlaceholder:"Tìm kiếm",showSelectAll:!0,selectAllLabel:"Chọn tất cả"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(W,{className:"text-sm font-medium",children:"Bước 2. Thêm món vào file"}),e.jsxs("div",{className:"text-muted-foreground text-sm",children:["Không được để trống các cột ",e.jsx("span",{className:"text-blue-600",children:"tên"}),","," ",e.jsx("span",{className:"text-blue-600",children:"tên nhóm"}),", ",e.jsx("span",{className:"text-blue-600",children:"mã món theo nhóm"})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(W,{className:"text-sm font-medium",children:"Bước 3. Tải file thực đơn lên"}),e.jsx("div",{className:"text-muted-foreground mb-2 text-sm",children:"Sau khi đã điền đầy đủ thực đơn bạn có thể tải file lên"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("input",{type:"file",accept:".xlsx,.xls",onChange:A,className:"hidden",id:"combo-file-upload",disabled:i.length===0}),e.jsxs(W,{htmlFor:"combo-file-upload",className:`flex items-center justify-center rounded-md border border-dashed p-4 text-sm ${i.length>0?"cursor-pointer border-gray-300 hover:border-gray-400":"cursor-not-allowed border-gray-200 bg-gray-50 text-gray-400"}`,children:[e.jsx(Ne,{className:"mr-2 h-4 w-4"}),m?m.name:"Tải file lên"]}),i.length===0&&e.jsx("p",{className:"mt-2 text-xs text-red-500",children:"Vui lòng chọn cửa hàng trước khi tải file"})]}),d&&e.jsxs("div",{className:"flex items-center justify-center p-4",children:[e.jsx("div",{className:"mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-blue-600"}),e.jsx("span",{className:"text-sm text-gray-600",children:"Đang đọc file..."})]})]})]})]})]}),e.jsx(Qt,{open:P,onOpenChange:w,data:v,onConfirm:o,title:"Thêm",selectedStoreIds:i})]})}function Jt(){const[t,a]=f.useState(""),[i,l]=f.useState(""),[m,b]=f.useState("all"),[d,j]=f.useState("all"),[v,T]=f.useState("all"),[P,D]=f.useState(!1),[k,E]=f.useState(!1),[A,I]=f.useState(!1),[w,o]=f.useState(null),s=x=>{x.key==="Enter"&&l(t)},{data:n=[]}=ee(),{data:r=[],isLoading:u}=Ae({storeUid:m==="all"?n.map(x=>x.id):m,status:v,search:i||void 0,promotionUid:d==="all"?void 0:d}),y=()=>{D(!0)},G=()=>{E(!0)},c=()=>{I(!0)},h=x=>{o(x.list_package_uid),D(!0)},F=zt();return e.jsxs("div",{className:"space-y-6",children:[!P&&e.jsxs(e.Fragment,{children:[e.jsx(Ze,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(et,{}),e.jsx(tt,{}),e.jsx(Ve,{})]})}),e.jsx(Gt,{searchTerm:t,setSearchTerm:a,selectedStore:m,setSelectedStore:b,selectedPromotion:d,setSelectedPromotion:j,expiryStatus:v,setExpiryStatus:T,onSearchKeyDown:s,onCreateCombo:y,onExportCombo:G,onImportCombo:c}),e.jsxs("div",{className:"bg-white",children:[u&&e.jsx("div",{className:"flex h-64 items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2"}),e.jsx("p",{className:"text-muted-foreground",children:"Đang tải dữ liệu combo..."})]})}),!u&&e.jsx(Kt,{columns:F,data:r,onRowClick:h})]})]}),P&&e.jsx(yt,{open:P,onOpenChange:x=>{D(x),x||o(null)},editingComboId:w||void 0}),e.jsx(Yt,{open:k,onOpenChange:E}),e.jsx(Wt,{open:A,onOpenChange:I})]})}const bn=function(){return e.jsx("div",{className:"container mx-auto p-6",children:e.jsx(Jt,{})})};export{bn as component};
