import{a3 as G,l as U,R as x,j as e,r as P,a4 as E,B as L}from"./index-Bnt3OGV2.js";import{C as K,a as H,b as D,d as Q}from"./card-aL168XsH.js";import"./date-range-picker-CVvofQC0.js";import{L as p}from"./form-wT1R35uI.js";import{c as V}from"./crm-api-Dd9UhSCJ.js";import"./pos-api-BwpRFGce.js";import"./user-oq7iQk7S.js";import{u as $}from"./useMutation-d67-fNFq.js";import{C as A}from"./checkbox-BiVztVsP.js";import{R as X,a as M}from"./radio-group-BlvHp0Hf.js";import{m as Y,u as W,a as Z}from"./use-pos-parent-settings-DpVDINaX.js";import{C as J}from"./query-keys-DQo7uRnN.js";import{X as T}from"./calendar-CzR6WBaB.js";import{I as ee}from"./input-CiKEYbig.js";import{c as te}from"./createLucideIcon-CNa_hh6B.js";import{E as se}from"./eye-Bl14FAPI.js";import"./chevron-right-sZt3EK3r.js";import"./react-icons.esm-B7rNr9e-.js";import"./popover-C2mvzdeD.js";import"./index-BT7Z3RDV.js";import"./select-Czd7KcZQ.js";import"./index-Bl1CGAiZ.js";import"./index-C2T2k_Lh.js";import"./check-apx2eTVC.js";import"./isSameMonth-C8JQo-AN.js";import"./utils-km2FGkQ4.js";import"./index-UiaF_xtq.js";import"./useQuery-DSrD7NAp.js";import"./settings-api-CYmE3Qr-.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const re=[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}]],ae=te("clipboard",re),ie=r=>new Promise((i,o)=>{const c=new FileReader;c.readAsDataURL(r),c.onload=()=>{const n=c.result.split(",")[1];i(n)},c.onerror=s=>o(s)}),ne={uploadImage:async(r,i)=>{try{const c={image_string:await ie(r),pos_parent:i},s=new URLSearchParams({pos_parent:i});return(await V.post(`/settings/upload?${s.toString()}`,c)).data}catch(o){throw console.error("Error uploading image to CRM:",o),o}}};function oe(){const r=G();return $({mutationFn:async i=>Y.saveRegisterPageConfig(i),onSuccess:(i,o)=>{r.invalidateQueries({queryKey:["crm",J.REGISTER_PAGE_CONFIG,o.pos_parent]})}})}function ce(r){var c;const i=$({mutationFn:s=>ne.uploadImage(s,r.pos_parent),onSuccess:s=>{var n,f;if(s!=null&&s.data&&((n=r.onSuccess)==null||n.call(r,s.data)),!(s!=null&&s.message)){const v=s.message||"Upload failed - no data received";(f=r.onError)==null||f.call(r,new Error(v))}},onError:s=>{var n;(n=r.onError)==null||n.call(r,s)}});return{uploadImage:async s=>{if(!s)throw new Error("No file provided");return i.mutateAsync(s)},isUploading:i.isPending,uploadError:((c=i.error)==null?void 0:c.message)||null,clearError:()=>i.reset(),isError:i.isError,isSuccess:i.isSuccess}}const B=[{field_id:"phone",field_name:"Số điện thoại",active:1,data_type:"string",require:!0},{field_id:"name",field_name:"Họ và tên",active:1,data_type:"string",require:!0},{field_id:"gender",field_name:"Giới tính",active:0,data_type:"string",require:!1},{field_id:"birthday",field_name:"Ngày sinh",active:0,data_type:"string",require:!1},{field_id:"address",field_name:"Địa chỉ",active:0,data_type:"string",require:!1},{field_id:"email",field_name:"Email",active:0,data_type:"string",require:!1}],O=P.createContext(void 0);function de({children:r}){const{selectedBrand:i}=U(),o=(i==null?void 0:i.brandId)||"",{data:c,isLoading:s}=W({pos_parent:o}),{data:n,isLoading:f}=Z({pos_parent:o}),v=oe(),[y,g]=x.useState(!0),[j,b]=x.useState(""),[N,w]=x.useState(""),[_,I]=x.useState("vi"),[S,u]=x.useState("none"),[k,C]=x.useState(B);x.useEffect(()=>{if(c!=null&&c.data){const a=c.data;g(a.active===1),b(a.logo!==""?a.logo:(n==null?void 0:n.Logo_Image)||""),w(a.banner!==""?a.banner:(n==null?void 0:n.image)||""),I(a.default_lang||"vi");const d=a.send_otp===1?"sms":"none";if(u(d),a.form_data&&a.form_data.length>0){const m=new Map(a.form_data.map(l=>[l.field_id,l])),h=B.map(l=>{const R=m.get(l.field_id);return R?(m.delete(l.field_id),{...l,...R}):l});m.forEach(l=>h.push(l)),C(h)}else C(B)}},[c,n]);const F=(a,d)=>{C(m=>m.map(h=>h.field_id===a?{...h,...d}:h))},q=()=>{if(!o){E.error("Chưa chọn thương hiệu");return}const a={pos_parent:o,banner:N,active:y?1:0,logo:j,send_otp:S==="none"?0:1,default_lang:_||"vi",form_data:k};v.mutate(a,{onSuccess:()=>{E.success("Lưu cấu hình thành công")},onError:d=>{E.error((d==null?void 0:d.message)||"Lưu cấu hình thất bại")}})},t={brandId:o,active:y,setActive:g,logo:j,setLogo:b,banner:N,setBanner:w,defaultLang:_,setDefaultLang:I,otpMethod:S,setOtpMethod:u,fields:k,updateField:F,isLoading:s,isSaving:v.isPending,onSave:q,settings:n,isSettingsLoading:f};return e.jsx(O.Provider,{value:t,children:r})}function z(){const r=P.useContext(O);if(!r)throw new Error("useConfigRegister must be used within ConfigRegisterProvider");return r}function le(){const{otpMethod:r,setOtpMethod:i,fields:o,updateField:c,isLoading:s,isSaving:n,onSave:f,logo:v,banner:y,settings:g,setLogo:j,setBanner:b}=z(),N=P.useRef(null),w=P.useRef(null),{selectedBrand:_}=U(),{uploadImage:I,isUploading:S}=ce({pos_parent:(_==null?void 0:_.brandId)||"",onSuccess:t=>{console.log("Image uploaded successfully",t)},onError:t=>{console.log("Image upload failed",t)}}),u=async(t,a)=>{if(!(!t||!t.type.startsWith("image/")))try{const d=await I(t);if(d!=null&&d.data){const m=d.data;a==="logo"?j(m):b(m)}}catch{const m=new FileReader;m.onload=h=>{var R;const l=(R=h.target)==null?void 0:R.result;a==="logo"?j(l):b(l)},m.readAsDataURL(t)}},k=()=>{var t;(t=N.current)==null||t.click()},C=()=>{var t;(t=w.current)==null||t.click()},F=()=>{const t=(g==null?void 0:g.Logo_Image)||"";j(t)},q=()=>{const t=(g==null?void 0:g.image)||"";b(t)};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"text-base font-medium",children:"Cài đặt form đăng ký"}),e.jsx("div",{className:"rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-base font-medium",children:"Phương thức xác thực"}),e.jsx("div",{className:"text-muted-foreground text-sm",children:"Chọn kênh gửi OTP, hoặc bỏ qua bước xác thực OTP"}),e.jsx("div",{className:"mt-3",children:e.jsxs(X,{value:r,onValueChange:t=>i(t),className:"grid gap-2 sm:grid-cols-3",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(M,{value:"sms",id:"otp-sms"}),e.jsx(p,{htmlFor:"otp-sms",children:"SMS"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(M,{value:"zns",id:"otp-zns",disabled:!0}),e.jsx(p,{htmlFor:"otp-zns",children:"ZNS"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(M,{value:"none",id:"otp-none"}),e.jsx(p,{htmlFor:"otp-none",children:"Không xác thực"})]})]})})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-base font-medium",children:"Các trường thông tin trong form đăng ký"}),e.jsx("div",{className:"text-muted-foreground text-sm",children:"Chọn các dữ liệu bạn muốn thu thập từ khách hàng"}),e.jsx("div",{className:"mt-4 space-y-3",children:o.map(t=>e.jsxs("div",{className:"grid items-center gap-2 sm:grid-cols-3",children:[e.jsx(p,{className:"text-sm font-medium",htmlFor:`field-${t.field_id}`,children:t.field_name}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(A,{id:`visible-${t.field_id}`,checked:t.field_id==="phone"?!0:!!t.active,onCheckedChange:a=>c(t.field_id,{active:a?1:0}),disabled:t.field_id==="phone"}),e.jsx(p,{htmlFor:`visible-${t.field_id}`,children:"Hiển thị"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(A,{id:`required-${t.field_id}`,checked:t.field_id==="phone"?!0:!!t.require,onCheckedChange:a=>c(t.field_id,{require:!!a}),disabled:t.field_id==="phone"}),e.jsx(p,{htmlFor:`required-${t.field_id}`,children:"Bắt buộc"})]})]})]},t.field_id))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"grid items-center gap-2 sm:grid-cols-3",children:e.jsx(p,{className:"cursor-pointer",onClick:k,children:"Logo"})}),e.jsx("div",{className:"text-muted-foreground pl-0 text-sm",children:"Kích thước tối thiểu 100px x 100px, tỉ lệ 1:1"}),e.jsx("input",{ref:N,type:"file",accept:"image/*",className:"hidden",onChange:t=>{var d;const a=(d=t.target.files)==null?void 0:d[0];u==null||u(a,"logo")}}),e.jsxs("div",{className:"relative mt-2 w-fit",children:[e.jsx("img",{src:v,alt:"Logo",className:"h-[100px] w-[100px] cursor-pointer rounded border-2 border-gray-300 hover:border-gray-400",onClick:k}),e.jsx(L,{type:"button",variant:"ghost",size:"icon",className:"absolute top-1 right-1 h-6 w-6 rounded-full bg-black/50 text-white hover:bg-black/75 hover:text-white",onClick:F,children:e.jsx(T,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"grid items-center gap-2 sm:grid-cols-3",children:e.jsx(p,{className:"cursor-pointer",onClick:C,children:"Banner"})}),e.jsx("input",{ref:w,type:"file",accept:"image/*",className:"hidden",onChange:t=>{var d;const a=(d=t.target.files)==null?void 0:d[0];u==null||u(a,"banner")}}),y&&e.jsxs("div",{className:"relative mt-2 w-fit",children:[e.jsx("img",{src:y,alt:"Banner",width:150,height:150,className:"cursor-pointer rounded border-2 border-gray-300 hover:border-gray-400",onClick:C}),e.jsx(L,{type:"button",variant:"ghost",size:"icon",className:"absolute top-1 right-1 h-6 w-6 rounded-full bg-black/50 text-white hover:bg-black/75 hover:text-white",onClick:q,children:e.jsx(T,{className:"h-4 w-4"})})]})]}),e.jsx(L,{type:"button",onClick:f,disabled:!!n||!!s||!!S,children:n?"Đang lưu...":S?"Đang upload...":"Lưu cấu hình"})]})})]})}const me=()=>{const{brandId:r}=z(),o=`${typeof window<"u"?window.location.origin:""}/membership?pos_parent=${r}`,c=async()=>{try{await navigator.clipboard.writeText(o),E.success("Đã sao chép đường dẫn")}catch{E.error("Không thể sao chép, vui lòng thử lại")}},s=()=>{window.open(o,"_blank","noopener,noreferrer")};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ee,{readOnly:!0,value:o,className:"font-mono"}),e.jsx(L,{type:"button",variant:"outline",size:"icon",onClick:c,title:"Sao chép",children:e.jsx(ae,{className:"h-4 w-4"})})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs(L,{type:"button",onClick:s,children:[e.jsx(se,{className:"mr-2 h-4 w-4"})," Xem trước"]})})]})};function ge(){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h1",{className:"text-2xl font-semibold tracking-tight",children:"Trang đăng ký thành viên"}),e.jsxs(K,{children:[e.jsx(H,{className:"pb-4",children:e.jsx(D,{className:"text-base",children:"Đường dẫn trang đăng ký"})}),e.jsxs(Q,{className:"space-y-4",children:[e.jsx(me,{}),e.jsx(le,{})]})]})]})}function ue(){return e.jsx(de,{children:e.jsx(ge,{})})}const Ge=ue;export{Ge as component};
