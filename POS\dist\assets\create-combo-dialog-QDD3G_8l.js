import{r as u,z as S,j as e,B as F,at as ue,au as ds,b as gs,l as ps,av as Ee,a3 as Gs,a4 as xe,aw as Vs}from"./index-C21OP4ex.js";import{u as Ve,F as $e,a as G,b as V,c as K,d as R,e as $,L as fs}from"./form-usWdQ_Nt.js";import{s as ze}from"./zod-B4gLZVLM.js";import{e as $s,f as zs,g as Ks,h as Us,i as qs}from"./use-combos-XDEL7_3-.js";import"./date-range-picker-B1pgj5D_.js";import{D as ve,a as Ce,b as Ke,c as Ue,e as Fe}from"./dialog-DXjwjGKV.js";import{I as B}from"./input-4sMIt001.js";import{f as Le,a as js,b as bs,c as Xs,d as Rs,e as ms,g as Qs,h as Ys,i as us}from"./date-utils-DBbLjCz0.js";import{u as Bs}from"./use-images-nkXjpNo3.js";import{u as Ws}from"./use-item-types-DA0U4OWS.js";import{u as Zs}from"./use-items-zU7JIOkv.js";import{u as Js}from"./use-promotions-B0DWFL9P.js";import{u as ys}from"./use-sources-CSlkwBP-.js";import{u as et}from"./use-stores-BQdEFBhG.js";import{D as Ge}from"./date-picker-8CCUFJO0.js";import{S as Be,a as We,b as Ze,c as Je,d as Ie}from"./select-B8Pw9rS-.js";import{T as st}from"./textarea-BLrSKk17.js";import{C as ss}from"./checkbox-DUpnJ1Rx.js";import{P as tt,a as at,b as nt}from"./popover-BtedB187.js";import{S as Ns}from"./scroll-area-DKiYF9x5.js";import{G as rt}from"./grip-vertical-Bo_DpjTT.js";import{X as es}from"./calendar-BiBi2kQF.js";import{A as lt,a as ct,b as ot,c as it,d as dt,e as mt,f as ut,g as ht}from"./alert-dialog-CzdUByb-.js";import{U as xt}from"./upload-CZfvv05H.js";import{T as gt}from"./trash-2-C_5rhUMO.js";const pt=S.object({comboName:S.string().min(1,"Tên combo là bắt buộc")}),ft=()=>{const t="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";let i="";for(let d=0;d<4;d++)i+=t.charAt(Math.floor(Math.random()*t.length));return`COMBO-${i}`};function aa({open:t,onOpenChange:i,combo:d}){const[c,v]=u.useState(!1),{mutateAsync:f}=$s(),{mutateAsync:m}=zs(),g=Ve({resolver:ze(pt),defaultValues:{comboName:`${d.package_name} - Copy`}}),p=async l=>{v(!0);try{const w=(await f({packageUids:[d.id]})).data[0];if(!w)throw new Error("Không tìm thấy thông tin combo");const k=Math.floor(Date.now()/1e3),H=ft(),M={...w,package_name:l.comboName,package_id:H,created_at:k,updated_at:k,id:void 0},z=Object.fromEntries(Object.entries(M).filter(([_,I])=>I!==void 0));await m({combos:[z]}),i(!1),g.reset()}catch(j){console.error("Error copying combo:",j)}finally{v(!1)}},C=()=>{i(!1),g.reset()};return e.jsx(ve,{open:t,onOpenChange:i,children:e.jsxs(Ce,{className:"sm:max-w-md",children:[e.jsx(Ke,{children:e.jsxs(Ue,{children:["Sao chép ",d.package_name]})}),e.jsx($e,{...g,children:e.jsxs("form",{onSubmit:g.handleSubmit(p),className:"space-y-4",children:[e.jsx(G,{control:g.control,name:"comboName",render:({field:l})=>e.jsxs(V,{children:[e.jsx(K,{children:"Tên combo *"}),e.jsx(R,{children:e.jsx(B,{placeholder:"Nhập tên combo",...l,disabled:c})}),e.jsx($,{})]})}),e.jsxs(Fe,{className:"gap-2",children:[e.jsx(F,{type:"button",variant:"outline",onClick:l=>{l.stopPropagation(),C()},disabled:c,children:"Hủy"}),e.jsx(F,{type:"submit",onClick:l=>l.stopPropagation(),disabled:c||!g.formState.isValid,children:c?"Đang sao chép...":"Sao chép"})]})]})})]})})}const vs=u.createContext(void 0);function na({children:t,value:i}){return e.jsx(vs.Provider,{value:i,children:t})}function ts(){const t=u.useContext(vs);if(t===void 0)throw new Error("useComboFormContext must be used within a ComboFormProvider");return t}function ra(){const{formData:t,updateFormData:i}=ts();return{formData:t,updateFormData:i}}function la(){const{handleBack:t,handleSave:i}=ts();return{handleBack:t,handleSave:i}}function ca(){const{isFormValid:t,isLoading:i,isEditMode:d}=ts();return{isFormValid:t,isLoading:i,isEditMode:d}}const jt=()=>{const[t,i]=u.useState(null);return{draggedIndex:t,handleDragStart:(f,m)=>{i(m),f.dataTransfer.effectAllowed="move",f.dataTransfer.setData("text/html",m.toString())},handleDragOver:f=>{f.preventDefault(),f.dataTransfer.dropEffect="move"},handleDrop:(f,m,g,p)=>{if(f.preventDefault(),t===null||t===m){i(null);return}const C=[...g],l=C[t];C.splice(t,1),C.splice(m,0,l),p(C),i(null)}}};function bt({open:t,onOpenChange:i,items:d,selectedItems:c,onConfirm:v,onCancel:f}){const[m,g]=u.useState([]),[p,C]=u.useState("");u.useEffect(()=>{t&&(g([...c]),C(""))},[t,c]);const l=u.useMemo(()=>d.filter((_,I,N)=>I===N.findIndex(re=>re.id===_.id)),[d]),j=u.useMemo(()=>l.filter(_=>_.item_name.toLowerCase().includes(p.trim().toLowerCase())),[l,p]),w=j.filter(_=>m.includes(_.id)),k=j.filter(_=>!m.includes(_.id)),H=(_,I)=>{g(I?N=>N.includes(_)?N:[...N,_]:N=>N.filter(re=>re!==_))},M=()=>{v(m)},z=()=>{g([]),C(""),f()};return e.jsx(ve,{open:t,onOpenChange:i,children:e.jsxs(Ce,{className:"max-h-[90vh] w-[900px] max-w-5xl lg:max-w-5xl",children:[e.jsxs("div",{className:"space-y-6 p-2",children:[e.jsx(B,{placeholder:"Tìm kiếm món",value:p,onChange:_=>C(_.target.value)}),e.jsx(Ns,{className:"h-[60vh] w-full",children:e.jsxs("div",{className:"space-y-4",children:[w.length>0&&e.jsxs("div",{children:[e.jsx("div",{className:"mb-3 flex items-center gap-2 rounded bg-green-50 p-3 text-base font-medium text-green-600",children:e.jsxs("span",{children:["✓ Đã chọn ",w.length," món"]})}),e.jsx("div",{className:"space-y-3",children:w.map(_=>e.jsx(hs,{item:_,isSelected:!0,onToggle:H},_.id))})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-3 rounded bg-gray-50 p-3 text-base font-medium text-gray-600",children:e.jsxs("span",{children:["Còn lại ",k.length," món"]})}),e.jsx("div",{className:"space-y-3",children:k.map(_=>e.jsx(hs,{item:_,isSelected:!1,onToggle:H},_.id))})]})]})})]}),e.jsxs(Fe,{children:[e.jsx(F,{variant:"outline",onClick:z,children:"Hủy"}),e.jsx(F,{onClick:M,children:"Xong"})]})]})})}function hs({item:t,isSelected:i,onToggle:d}){return e.jsxs("div",{className:"flex items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(ss,{id:t.id,checked:i,onCheckedChange:c=>d(t.id,!!c),className:"h-5 w-5"}),e.jsx(fs,{htmlFor:t.id,className:"flex-1 cursor-pointer text-base",children:e.jsx("div",{children:e.jsx("div",{className:"font-medium",children:t.item_name})})})]})}function yt({open:t,onOpenChange:i,onConfirm:d,onCancel:c,items:v=[],selectedItemIds:f=[],initialName:m,initialRequired:g,initialMax:p,initialItemSettings:C}){const[l,j]=u.useState(m||""),[w,k]=u.useState(g??0),[H,M]=u.useState(p??0),[z,_]=u.useState(!1),[I,N]=u.useState(f),[re,W]=u.useState({}),[je,ee]=u.useState({}),[he,se]=u.useState(""),{draggedIndex:Q,handleDragStart:oe,handleDragOver:r,handleDrop:b}=jt(),O=u.useMemo(()=>f,[f==null?void 0:f.join(",")]);u.useEffect(()=>{t&&(j(m||""),k(g??0),M(p??0),N(O),W(x=>{const y={};return O.forEach(o=>{const T=v.find(te=>te.id===o),Y=(T==null?void 0:T.ots_price)??0;C!=null&&C[o]?y[o]={finalPrice:C[o].finalPrice,discountPercent:C[o].discountPercent}:y[o]=x[o]??{finalPrice:Y,discountPercent:0}}),y}),ee(x=>{const y={};return O.forEach(o=>{C!=null&&C[o]?y[o]=C[o].pricingMode:y[o]=x[o]??"custom"}),y}))},[t,m,g,p,O,v,C]);const q=()=>l.trim()?(se(""),!0):(se("Tên nhóm là bắt buộc"),!1),ie=u.useCallback(()=>{if(!q())return;const x={};I.forEach(y=>{const o=re[y],T=je[y]||"custom";x[y]={finalPrice:(o==null?void 0:o.finalPrice)||0,discountPercent:(o==null?void 0:o.discountPercent)||0,pricingMode:T}}),d({name:l.trim(),required:w,max:H,items:I,itemSettings:x}),j(""),k(0),M(0),se("")},[q,d,l,w,H,I]),_e=u.useCallback((x,y,o)=>{ee(T=>({...T,[x]:y})),y==="base"?W(T=>({...T,[x]:{finalPrice:o,discountPercent:0}})):y==="custom"&&W(T=>({...T,[x]:{...T[x],discountPercent:0}}))},[]),le=u.useCallback((x,y,o,T)=>{if(T==="base"){const Y=o*(1-y/100);W(te=>({...te,[x]:{finalPrice:Y,discountPercent:y}}))}},[]),ge=u.useCallback((x,y,o,T)=>{if(T==="custom")W(Y=>({...Y,[x]:{...Y[x],finalPrice:y}}));else{const Y=o>0?(o-y)/o*100:0;W(te=>({...te,[x]:{finalPrice:y,discountPercent:Math.max(0,Y)}}))}},[]),de=u.useCallback(x=>{N(y=>y.filter(o=>o!==x)),W(y=>{const o={...y};return delete o[x],o}),ee(y=>{const o={...y};return delete o[x],o})},[]),pe=x=>{const y=x.target.value;j(y),he&&y.trim()&&se("")},Oe=u.useCallback(x=>{x||(j(""),k(0),M(0),se(""),ee({}),c()),i(x)},[c,i]);return e.jsx(ve,{open:t,onOpenChange:Oe,children:e.jsxs(Ce,{className:"max-w-4xl lg:max-w-4xl",children:[e.jsx(Ke,{children:e.jsx(Ue,{className:"text-center",children:"Tạo nhóm"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(B,{placeholder:"Tên nhóm *",value:l,onChange:pe,className:he?"border-red-500":""}),he&&e.jsx("p",{className:"text-sm text-red-500",children:he})]}),e.jsx("div",{className:"rounded-md border",children:e.jsx("table",{className:"w-full text-sm",children:e.jsxs("tbody",{children:[e.jsxs("tr",{className:"border-b bg-gray-50",children:[e.jsx("td",{className:"w-1/3 p-4 font-medium text-gray-700",children:"Yêu cầu"}),e.jsx("td",{className:"p-4",children:e.jsx(B,{type:"number",value:w,onChange:x=>k(parseInt(x.target.value||"0",10)),className:"w-full"})})]}),e.jsxs("tr",{children:[e.jsx("td",{className:"w-1/3 p-4 font-medium text-gray-700",children:"Tối đa"}),e.jsx("td",{className:"p-4",children:e.jsx(B,{type:"number",value:H,onChange:x=>M(parseInt(x.target.value||"0",10)),className:"w-full"})})]})]})})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Danh sách món"}),e.jsx("div",{className:"max-h-96 overflow-auto rounded-md border",children:e.jsxs("table",{className:"w-full text-sm",children:[e.jsx("thead",{className:"sticky top-0 bg-gray-50",children:e.jsxs("tr",{className:"text-gray-700",children:[e.jsx("th",{className:"w-8 p-3"}),e.jsx("th",{className:"p-3 text-left font-medium",children:"Tên"}),e.jsx("th",{className:"p-3 text-left font-medium",children:"Lựa chọn giá"}),e.jsx("th",{className:"p-3 text-left font-medium",children:"Giá gốc"}),e.jsx("th",{className:"p-3 text-left font-medium",children:"Giảm giá (%)"}),e.jsx("th",{className:"p-3 text-left font-medium",children:"Giá sau điều chỉnh"}),e.jsx("th",{className:"w-8 p-3"})]})}),e.jsxs("tbody",{children:[I.length>0&&I.map((x,y)=>{var De,Ae;const o=v.find(ae=>ae.id===x),T=(o==null?void 0:o.ots_price)??0,Y=((De=re[x])==null?void 0:De.discountPercent)??0,te=((Ae=re[x])==null?void 0:Ae.finalPrice)??T,Se=Q===y,me=je[x]||"custom";return e.jsxs("tr",{draggable:!0,onDragStart:ae=>oe(ae,y),onDragOver:r,onDrop:ae=>b(ae,y,I,N),className:`border-t transition-all duration-200 ${Se?"scale-105 opacity-50 bg-blue-50":"cursor-move hover:bg-gray-50"}`,children:[e.jsx("td",{className:"p-3 text-center",children:e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(rt,{className:"h-4 w-4 text-gray-400"})})}),e.jsxs("td",{className:"p-3",children:[o==null?void 0:o.item_name," ",o!=null&&o.item_id?`(${o.item_id})`:""]}),e.jsx("td",{className:"p-3",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(F,{type:"button",variant:me==="custom"?"default":"outline",className:me==="custom"?"bg-blue-600 text-white hover:bg-blue-700":"",onClick:()=>_e(x,"custom",T),children:"Sửa giá"}),e.jsx(F,{type:"button",variant:me==="base"?"default":"outline",className:me==="base"?"bg-blue-600 text-white hover:bg-blue-700":"",onClick:()=>_e(x,"base",T),children:"Giá gốc"})]})}),e.jsxs("td",{className:"p-3",children:[new Intl.NumberFormat("vi-VN").format(T)," đ"]}),e.jsx("td",{className:"p-3",children:me==="custom"?e.jsx("div",{className:"flex items-center justify-center text-gray-400",children:e.jsx("span",{className:"text-sm",children:"—"})}):e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(B,{type:"text",className:"w-20",value:ue(Math.round(Y)),onChange:ae=>{const we=ds(ae.target.value),be=Math.min(100,Math.max(0,Number(we||0)));le(x,be,T,me)},placeholder:"0"}),e.jsx("span",{className:"text-sm text-gray-500",children:"%"})]})}),e.jsx("td",{className:"p-3",children:e.jsx(B,{type:"text",className:"w-28",value:Math.round(te)===0?"":ue(Math.round(te)),onChange:ae=>{const we=ds(ae.target.value),be=Number(we||0);ge(x,be,T,me)},placeholder:"0"})}),e.jsx("td",{className:"p-3 text-right",children:e.jsx("button",{type:"button","aria-label":"Remove",onClick:()=>de(x),className:"text-gray-500 hover:text-gray-800",children:e.jsx(es,{className:"h-4 w-4"})})})]},x)}),e.jsx("tr",{children:e.jsx("td",{colSpan:7,className:"p-4",children:e.jsx(F,{variant:"ghost",className:"text-blue-600",onClick:()=>_(!0),children:"Thêm món"})})})]})]})})]})]}),e.jsxs(Fe,{children:[e.jsx(F,{type:"button",variant:"outline",onClick:()=>Oe(!1),children:"Hủy"}),e.jsx(F,{type:"button",onClick:ie,disabled:!l.trim(),children:"Xong"})]}),e.jsx(bt,{open:z,onOpenChange:_,items:v,selectedItems:I,onConfirm:x=>{N(x),W(y=>{const o={...y};return x.forEach(T=>{if(o[T]===void 0){const Y=v.find(Se=>Se.id===T),te=(Y==null?void 0:Y.ots_price)??0;o[T]={finalPrice:te,discountPercent:0}}}),o}),_(!1)},onCancel:()=>_(!1)})]})})}const Nt=S.object({orderSource:S.string().min(1,"Vui lòng chọn nguồn đơn"),amount:S.string().min(1,"Vui lòng nhập số tiền"),comboCode:S.string().max(50,"Mã combo tối đa 50 ký tự").optional(),autoGenerateCode:S.boolean()});function vt({open:t,onOpenChange:i,onConfirm:d,onCancel:c,selectedStoreIds:v=[],onOpenTimeFrameConfig:f,editingData:m}){var oe;const{company:g}=gs(),{selectedBrand:p}=ps(),{data:C=[]}=ys({company_uid:g==null?void 0:g.id,brand_uid:p==null?void 0:p.id,skip_limit:!0,enabled:t&&!!(g!=null&&g.id)&&!!(p!=null&&p.id)&&v.length>0,list_store_uid:v.length>0?v:void 0}),[l,j]=u.useState([]),[w,k]=u.useState(!1),[H,M]=u.useState(null),z=r=>r.map((b,O)=>b?O:-1).filter(b=>b!==-1),_=r=>Object.entries(r).filter(([b,O])=>O).map(([b,O])=>parseInt(b)).sort((b,O)=>b-O),I=r=>{const b=z(r.selectedDays),O=_(r.selectedHours);return{price:parseInt(r.amount)||0,from_date:ms(r.startDate,!1),to_date:ms(r.endDate,!0),time_sale_date_week:Rs(b),time_sale_hour_day:Xs(O)}},N=Ve({resolver:ze(Nt),defaultValues:{orderSource:"",amount:"0",comboCode:"",autoGenerateCode:!0}});u.useEffect(()=>{m&&t?(N.reset({orderSource:m.orderSource,amount:m.amount,comboCode:m.comboCode||"",autoGenerateCode:m.autoGenerateCode}),m.timeFrameConfigs&&j(m.timeFrameConfigs)):t&&!m&&(N.reset({orderSource:"",amount:"0",comboCode:"",autoGenerateCode:!0}),j([]))},[m,t,N]);const re=r=>{const b=l.map(q=>I(q)),O={...r,timeFrameConfigs:l,price_times:b};d(O),N.reset(),j([])},W=()=>{N.reset(),c()},je=r=>{r||W(),i(r)},ee=r=>{M(r),k(!0)},he=()=>{H&&(j(r=>r.filter(b=>b.id!==H)),M(null)),k(!1)},se=r=>{var q;const b=((q=C.find(ie=>ie.id===N.watch("orderSource")))==null?void 0:q.sourceName)||N.watch("orderSource"),O={amount:r.amount,startDate:r.startDate,endDate:r.endDate,selectedDays:r.selectedDays,selectedHours:r.selectedHours};f==null||f(b,ie=>Q(ie,r.id),O)},Q=(r,b)=>{if(b)j(O=>O.map(q=>q.id===b?{...q,amount:r.amount,startDate:r.startDate,endDate:r.endDate,selectedDays:r.selectedDays,selectedHours:r.selectedHours}:q));else{const O={id:Date.now().toString(),amount:r.amount,startDate:r.startDate,endDate:r.endDate,selectedDays:r.selectedDays,selectedHours:r.selectedHours};j(q=>[...q,O])}};return e.jsxs(ve,{open:t,onOpenChange:je,children:[e.jsxs(Ce,{className:"max-w-md",children:[e.jsx(Ke,{children:e.jsxs(Ue,{className:"text-center",children:[m?"Chỉnh sửa":"Cấu hình"," giá theo nguồn"]})}),e.jsx($e,{...N,children:e.jsxs("form",{onSubmit:N.handleSubmit(re),className:"space-y-4",children:[e.jsx(G,{control:N.control,name:"orderSource",render:({field:r})=>e.jsxs(V,{children:[e.jsx(K,{className:"text-sm font-medium",children:"Nguồn đơn"}),e.jsx(R,{children:e.jsxs(Be,{onValueChange:r.onChange,defaultValue:r.value,children:[e.jsx(We,{className:"w-full",onClick:b=>b.preventDefault(),children:e.jsx(Ze,{placeholder:"Chọn nguồn đơn"})}),e.jsx(Je,{children:C.map(b=>e.jsx(Ie,{value:b.id,children:b.sourceName},b.id))})]})}),e.jsx($,{})]})}),e.jsx(G,{control:N.control,name:"amount",render:({field:r})=>e.jsxs(V,{children:[e.jsx(K,{className:"text-sm font-medium",children:"Số tiền"}),e.jsx(R,{children:e.jsx(B,{placeholder:"0",value:ue(r.value||""),onChange:b=>Ee(b.target.value,r.onChange)})}),e.jsx($,{})]})}),N.watch("orderSource")&&N.watch("amount")&&e.jsx("div",{className:"rounded-lg border border-blue-200 bg-blue-50 p-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-600",children:"Cấu hình giá theo khung thời gian"}),e.jsxs("div",{className:"text-sm text-gray-800",children:["Giá theo nguồn"," ",e.jsx("span",{className:"font-bold",children:((oe=C.find(r=>r.id===N.watch("orderSource")))==null?void 0:oe.sourceName)||N.watch("orderSource")})," ","sẽ được áp dụng theo số tiền ",e.jsxs("span",{className:"font-bold",children:[ue(N.watch("amount")||"")," ₫"]}),". Khi cấu hình giá theo khung thời gian số tiền sẽ hiển thị theo các khung thời gian cấu hình được dưới đây"]})]}),e.jsx(F,{type:"button",variant:"default",size:"sm",onClick:()=>{var b;const r=((b=C.find(O=>O.id===N.watch("orderSource")))==null?void 0:b.sourceName)||N.watch("orderSource");f==null||f(r,Q)},children:"Thêm cấu hình"})]})}),l.length>0&&e.jsx("div",{className:"space-y-3",children:l.map(r=>e.jsx("div",{className:"rounded-lg border border-gray-200 bg-gray-50 p-3 cursor-pointer hover:bg-gray-100",onClick:()=>{se(r)},children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"text-sm text-gray-600",children:["● Từ ngày ",Le(r.startDate)," đến ngày ",Le(r.endDate)," Giá: ",ue(r.amount)," ₫"]}),e.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:["Khung giờ ",js(_(r.selectedHours))," ",bs(z(r.selectedDays))]})]}),e.jsx(F,{type:"button",variant:"ghost",size:"sm",onClick:b=>{b.stopPropagation(),ee(r.id)},className:"text-gray-400 hover:text-red-500",children:"⊗"})]})},r.id))}),e.jsx(Fe,{children:e.jsx(F,{type:"submit",className:"w-full",children:"Xác nhận"})})]})})]}),e.jsx(lt,{open:w,onOpenChange:k,children:e.jsxs(ct,{children:[e.jsxs(ot,{children:[e.jsx(it,{children:"Xác nhận xóa"}),e.jsx(dt,{children:"Bạn có muốn bỏ cấu hình này không? Hành động này không thể hoàn tác."})]}),e.jsxs(mt,{children:[e.jsx(ut,{children:"Hủy"}),e.jsx(ht,{onClick:he,className:"bg-red-600 hover:bg-red-700",children:"Xóa"})]})]})})]})}function Ct({open:t,onOpenChange:i,stores:d,selectedStores:c,onConfirm:v,onCancel:f}){const[m,g]=u.useState([]),[p,C]=u.useState(""),l=d.filter((z,_,I)=>_===I.findIndex(N=>N.id===z.id));u.useEffect(()=>{t&&g([...c])},[t,c]);const j=l.filter(z=>m.includes(z.id)&&z.name.toLowerCase().includes(p.toLowerCase())),w=l.filter(z=>!m.includes(z.id)&&z.name.toLowerCase().includes(p.toLowerCase())),k=()=>{v(m),C("")},H=()=>{g([]),C(""),f()},M=(z,_)=>{g(_?[...m,z]:m.filter(I=>I!==z))};return e.jsx(ve,{open:t,onOpenChange:i,children:e.jsxs(Ce,{className:"max-h-[90vh] w-[800px] max-w-4xl lg:max-w-4xl",children:[e.jsxs("div",{className:"space-y-6 p-2",children:[e.jsx(_t,{value:p,onChange:C}),e.jsx(St,{selectedStores:j,unselectedStores:w,selectedIds:m,onToggle:M})]}),e.jsxs(Fe,{children:[e.jsx(F,{variant:"outline",onClick:H,children:"Hủy"}),e.jsx(F,{onClick:k,children:"Xong"})]})]})})}function _t({value:t,onChange:i}){return e.jsx(B,{placeholder:"Tìm kiếm cửa hàng",value:t,onChange:d=>i(d.target.value)})}function St({selectedStores:t,unselectedStores:i,onToggle:d}){return e.jsx(Ns,{className:"h-[60vh] w-full",children:e.jsxs("div",{className:"space-y-4",children:[t.length>0&&e.jsxs("div",{children:[e.jsx("div",{className:"mb-3 flex items-center gap-2 rounded bg-green-50 p-3 text-base font-medium text-green-600",children:e.jsxs("span",{children:["✓ Đã chọn ",t.length," cửa hàng"]})}),e.jsx("div",{className:"space-y-3",children:t.map(c=>e.jsx(xs,{store:c,isSelected:!0,onToggle:d},c.id))})]}),i.length>0&&e.jsxs("div",{children:[e.jsx("div",{className:"mb-3 rounded bg-gray-50 p-3 text-base font-medium text-gray-600",children:e.jsxs("span",{children:["Còn lại ",i.length," cửa hàng"]})}),e.jsx("div",{className:"space-y-3",children:i.map(c=>e.jsx(xs,{store:c,isSelected:!1,onToggle:d},c.id))})]})]})})}function xs({store:t,isSelected:i,onToggle:d}){return e.jsxs("div",{className:"flex items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(ss,{id:t.id,checked:i,onCheckedChange:c=>d(t.id,!!c),className:"h-5 w-5"}),e.jsx(fs,{htmlFor:t.id,className:"flex-1 cursor-pointer text-base",children:e.jsx("div",{children:e.jsx("div",{className:"font-medium",children:t.name})})})]})}const Dt=S.object({amount:S.string().min(1,"Vui lòng nhập số tiền"),startDate:S.string().min(1,"Ngày bắt đầu là bắt buộc"),endDate:S.string().min(1,"Ngày kết thúc là bắt buộc"),selectedDays:S.array(S.boolean()),selectedHours:S.record(S.string(),S.boolean())});function wt({open:t,onOpenChange:i,onConfirm:d,onCancel:c,sourceName:v,editingData:f}){const m=Ve({resolver:ze(Dt),defaultValues:{amount:"",startDate:new Date().toISOString().split("T")[0],endDate:new Date().toISOString().split("T")[0],selectedDays:[!1,!1,!1,!1,!1,!1,!1],selectedHours:{}}});u.useEffect(()=>{f&&t?m.reset({amount:f.amount,startDate:f.startDate,endDate:f.endDate,selectedDays:f.selectedDays,selectedHours:f.selectedHours}):t&&!f&&m.reset({amount:"",startDate:new Date().toISOString().split("T")[0],endDate:new Date().toISOString().split("T")[0],selectedDays:[!1,!1,!1,!1,!1,!1,!1],selectedHours:{}})},[f,t,m]);const g=(l,j)=>{if(!l){j(void 0);return}const w=l.getFullYear(),k=String(l.getMonth()+1).padStart(2,"0"),H=String(l.getDate()).padStart(2,"0");j(`${w}-${k}-${H}`)},p=l=>{d(l),m.reset()},C=()=>{m.reset(),c()};return e.jsx(ve,{open:t,onOpenChange:i,children:e.jsxs(Ce,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[e.jsx(Ke,{children:e.jsxs(Ue,{className:"text-lg font-medium",children:[f?"Chỉnh sửa":"Cấu hình"," giá theo khung thời gian nguồn ",v]})}),e.jsx($e,{...m,children:e.jsxs("form",{onSubmit:m.handleSubmit(p),className:"space-y-6",children:[e.jsx(G,{control:m.control,name:"amount",render:({field:l})=>e.jsxs(V,{children:[e.jsx(K,{className:"text-sm font-medium text-gray-700",children:"Số tiền"}),e.jsx(R,{children:e.jsx(B,{placeholder:"0",className:"w-full",value:ue(l.value||""),onChange:j=>Ee(j.target.value,l.onChange)})}),e.jsx($,{})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-700",children:"Ngày áp dụng"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(G,{control:m.control,name:"startDate",render:({field:l})=>e.jsxs(V,{children:[e.jsx(K,{className:"text-sm text-gray-600",children:"Ngày bắt đầu"}),e.jsx(R,{children:e.jsx(Ge,{date:l.value?new Date(l.value+"T00:00:00"):void 0,onDateChange:j=>g(j,l.onChange),placeholder:"Chọn ngày bắt đầu",className:"w-full"})}),e.jsx($,{})]})}),e.jsx(G,{control:m.control,name:"endDate",render:({field:l})=>e.jsxs(V,{children:[e.jsx(K,{className:"text-sm text-gray-600",children:"Ngày kết thúc"}),e.jsx(R,{children:e.jsx(Ge,{date:l.value?new Date(l.value+"T00:00:00"):void 0,onDateChange:j=>g(j,l.onChange),placeholder:"Chọn ngày kết thúc",className:"w-full"})}),e.jsx($,{})]})})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-700",children:"Khung thời gian áp dụng"}),e.jsx(G,{control:m.control,name:"selectedDays",render:({field:l})=>e.jsxs(V,{children:[e.jsx(K,{className:"text-sm text-gray-600",children:"Chọn ngày"}),e.jsx("div",{className:"grid grid-cols-7 gap-2",children:["Thứ 2","Thứ 3","Thứ 4","Thứ 5","Thứ 6","Thứ 7","Chủ nhật"].map((j,w)=>{const k=Array.isArray(l.value)?l.value[w]:!1;return e.jsx(F,{type:"button",variant:k?"default":"outline",size:"sm",className:`text-xs ${k?"bg-blue-600 text-white":"border-blue-600 text-blue-600"}`,onClick:()=>{const H=Array.isArray(l.value)?[...l.value]:[!1,!1,!1,!1,!1,!1,!1];H[w]=!H[w],l.onChange(H)},children:j},j)})}),e.jsx($,{})]})}),e.jsx(G,{control:m.control,name:"selectedHours",render:({field:l})=>e.jsxs(V,{children:[e.jsx(K,{className:"text-sm text-gray-600",children:"Chọn giờ"}),e.jsx("div",{className:"grid grid-cols-10 gap-2",children:Array.from({length:24},(j,w)=>w).map(j=>{const w=j.toString(),k=`${j}h`,H=l.value&&l.value[w];return e.jsx(F,{type:"button",variant:H?"default":"outline",size:"sm",className:`text-xs ${H?"bg-blue-600 text-white":"border-blue-600 text-blue-600"}`,onClick:()=>{const M={...l.value};M[w]=!M[w],l.onChange(M)},children:k},j)})}),e.jsx($,{})]})})]}),e.jsxs("div",{className:"flex justify-end gap-3 pt-4",children:[e.jsx(F,{type:"button",variant:"outline",onClick:C,children:"Hủy"}),e.jsx(F,{type:"submit",children:"Xác nhận"})]})]})})]})})}const Tt=(t,i)=>{const d=new Array(i).fill(!1),c=[4,8,16,32,64,128,2];for(let v=0;v<i&&v<c.length;v++)d[v]=(t&c[v])!==0;return d},kt=t=>{const i={};for(let d=0;d<24;d++)i[d.toString()]=(t&1<<d)!==0;return i},Pt=t=>t.map((i,d)=>{const c=Qs(i.time_sale_date_week||0),v=Ys(i.time_sale_hour_day||0),f=Array(7).fill(!1);c.forEach(g=>{g>=0&&g<7&&(f[g]=!0)});const m={};for(let g=0;g<24;g++)m[g.toString()]=v.includes(g);return{id:`timeframe-${d}`,amount:i.price.toString(),startDate:us(i.from_date),endDate:us(i.to_date),selectedDays:f,selectedHours:m}}),Ft=S.object({comboName:S.string().min(1,"Tên combo là bắt buộc"),price:S.string().min(1,"Giá là bắt buộc"),description:S.string().default(""),storeSelection:S.string().default(""),ctrmType:S.string().default(""),promotionId:S.string().optional(),groupSelection:S.string().default(""),comboCode:S.string().regex(/^[a-zA-Z0-9_-]*$/,"Mã combo không được chứa dấu cách và ký tự có dấu").default(""),vat:S.string().default("0"),startDate:S.string().min(1,"Ngày bắt đầu là bắt buộc"),endDate:S.string().min(1,"Ngày kết thúc là bắt buộc"),displayOrder:S.string().default(""),image:S.any().optional(),selectedDays:S.array(S.boolean()).default([!1,!1,!1,!1,!1,!1,!1]),selectedHours:S.record(S.string(),S.boolean()).default({})});function oa({open:t,onOpenChange:i,editingComboId:d}){var ns,rs,ls,cs,os;const{selectedBrand:c}=ps(),{companyUid:v}=gs(),f=Gs(),[m,g]=u.useState(!1),[p,C]=u.useState([]),[l,j]=u.useState(!1),[w,k]=u.useState(-1),[H,M]=u.useState(!1),[z,_]=u.useState(""),[I,N]=u.useState(null),[re,W]=u.useState(null),[je,ee]=u.useState(!1),[he,se]=u.useState("create"),[Q,oe]=u.useState(null),[r,b]=u.useState(null),[O,q]=u.useState(null),[ie,_e]=u.useState(!1),[le,ge]=u.useState([]),[de,pe]=u.useState([]),{data:Oe=[]}=ys({enabled:t&&p.length>0,skip_limit:!0,list_store_uid:p.length>0?p:void 0}),{promotions:x}=Js({enabled:t&&!!v&&!!(c!=null&&c.id)&&p.length>0,params:{skip_limit:!0,company_uid:v,brand_uid:c==null?void 0:c.id,list_store_uid:p.join(","),partner_auto_gen:0,active:1}}),{data:y=[]}=Ws({enabled:t&&!!v&&!!(c!=null&&c.id)&&p.length>0,company_uid:v,brand_uid:c==null?void 0:c.id,store_uid:p[0],skip_limit:!0,active:1}),{data:o=[]}=Zs({enabled:t&&!!v&&!!(c!=null&&c.id)&&p.length>0,params:{company_uid:v,brand_uid:c==null?void 0:c.id,list_store_uid:p.join(","),skip_limit:!0,active:1}}),{data:T=[],isLoading:Y,error:te}=et(),Se=T.filter(s=>s.isActive),{mutate:me,isPending:De}=Ks(),{mutate:Ae,isPending:ae}=Us(),{mutate:we,isPending:be}=Bs(),{data:X,isLoading:qe}=qs(d||"",!!d&&t),E=Ve({resolver:ze(Ft),defaultValues:{comboName:"",price:"",description:"",storeSelection:"",ctrmType:"",promotionId:"",groupSelection:"",comboCode:"",vat:"0",startDate:new Date().toISOString().split("T")[0],endDate:new Date().toISOString().split("T")[0],displayOrder:"",image:void 0,selectedDays:[!1,!1,!1,!1,!1,!1,!1],selectedHours:{}}});u.useEffect(()=>{if(t){const s=new Date().toISOString().split("T")[0];E.reset({comboName:"",price:"",description:"",storeSelection:"",ctrmType:"",promotionId:"",groupSelection:"",comboCode:"",vat:"0",startDate:s,endDate:s,displayOrder:"",image:void 0,selectedDays:[!1,!1,!1,!1,!1,!1,!1],selectedHours:{}})}},[t,E]),u.useEffect(()=>{var s,a,n,A;if(t)if(d){if(X&&X.length>0&&!qe){const h=X[0],U=X.map(D=>D.store_uid).filter(D=>D),ce=h.from_date?new Date(h.from_date).toISOString().split("T")[0]:new Date().toISOString().split("T")[0],ye=h.to_date?new Date(h.to_date).toISOString().split("T")[0]:new Date().toISOString().split("T")[0],Re=(s=h.package_detail)!=null&&s.LstItem_Options&&Array.isArray(h.package_detail.LstItem_Options)?h.package_detail.LstItem_Options.map(D=>{const fe={};return D.LstItem&&Array.isArray(D.LstItem)&&D.LstItem.forEach(ne=>{ne&&ne.item_id&&(fe[ne.item_id]={finalPrice:ne.ots_price||0,discountPercent:(ne.discount_combo_item||0)*100,pricingMode:ne.state_change_price===1?"custom":"base"})}),{id:D.id||"",name:D.Name||"",required:D.Min_Permitted||0,max:D.Max_Permitted||0,itemIds:D.LstItem&&Array.isArray(D.LstItem)?D.LstItem.map(ne=>ne.item_id):[],itemSettings:fe}}):[],Qe=(a=h.extra_data)!=null&&a.price_by_source&&Array.isArray(h.extra_data.price_by_source)?h.extra_data.price_by_source.map(D=>{const fe=D.price_times&&Array.isArray(D.price_times)&&D.price_times.length>0?Pt(D.price_times):[];return{orderSource:D.source_id||"",amount:(D.price||0).toString(),comboCode:h.package_id||"",autoGenerateCode:!1,timeFrameConfigs:fe,price_times:D.price_times&&Array.isArray(D.price_times)?D.price_times:[]}}):[];E.reset({comboName:h.package_name||"",price:((n=h.ta_value)==null?void 0:n.toString())||"",description:h.description||"",storeSelection:h.store_uid||"",ctrmType:h.promotion_id||"",promotionId:h.promotion_id||"",groupSelection:h.item_type_uid||"",comboCode:h.package_id||"",vat:h.vat_tax_rate?(h.vat_tax_rate*100).toString():"0",startDate:ce,endDate:ye,displayOrder:((A=h.sort)==null?void 0:A.toString())||"",image:void 0,selectedDays:Tt(h.time_sale_date_week||0,7),selectedHours:kt(h.time_sale_hour_day||0)}),ge(Re),pe(Qe),C(U),q(null),h.image_path&&typeof h.image_path=="string"?b(h.image_path):b(null)}}else E.reset({comboName:"",price:"",description:"",storeSelection:"",ctrmType:"",promotionId:"",groupSelection:"",comboCode:"",vat:"0",startDate:new Date().toISOString().split("T")[0],endDate:new Date().toISOString().split("T")[0],displayOrder:"",image:void 0,selectedDays:Array(7).fill(!1),selectedHours:{}}),C([]),ge([]),pe([]),b(null),q(null),_e(!1)},[t,d,X,qe,E]);const Xe=()=>{i(!1)},Cs=s=>{C(s),g(!1)},_s=()=>{g(!1)},Ss=()=>{if(te){xe.error("Không thể tải danh sách cửa hàng");return}g(!0)},Ds=()=>{k(-1),j(!0)},ws=()=>{se("create"),oe(null),ee(!0)},Ts=s=>{se("edit"),oe(s),ee(!0)},ks=s=>{ge(a=>a.filter(n=>n.id!==s))},Ps=s=>{if(w>=0){const a=[...de];a[w]=s,pe(a)}else pe([...de,s]);j(!1),k(-1)},Fs=()=>{j(!1),k(-1)},Os=s=>{k(s),j(!0)},As=s=>s.map((a,n)=>a?n:-1).filter(a=>a!==-1),Hs=s=>Object.entries(s).filter(([a,n])=>n).map(([a,n])=>parseInt(a)).sort((a,n)=>a-n),Ms=()=>{M(!1),_(""),N(null),W(null)},as=(s,a)=>{if(!s){a(void 0);return}const n=s.getFullYear(),A=String(s.getMonth()+1).padStart(2,"0"),h=String(s.getDate()).padStart(2,"0");a(`${n}-${A}-${h}`)},Is=async s=>{var a,n;if(!v||!(c!=null&&c.id)){xe.error("Thiếu thông tin công ty hoặc thương hiệu");return}if(p.length===0){xe.error("Vui lòng chọn ít nhất một cửa hàng");return}if(le.length===0){xe.error("Vui lòng thêm ít nhất một nhóm món");return}try{const A=X&&X.length>0?X[0]:null;let h="";if(d&&A&&(h=r||""),O)try{h=(await new Promise((Z,L)=>{we(O,{onSuccess:J=>Z(J),onError:J=>L(J)})})).data.image_url}catch{xe.error("Tải ảnh lên thất bại. Vui lòng thử lại.");return}const U=new Date(s.startDate),ce=new Date(s.endDate);ce.setHours(23,59,59,999);const ye=[4,8,16,32,64,128,2],Re=s.selectedDays.reduce((P,Z,L)=>Z&&L<ye.length?P|ye[L]:P,0),Qe=Object.entries(s.selectedHours).reduce((P,[Z,L])=>L?P|1<<parseInt(Z):P,0),D=x.find(P=>P.code===s.ctrmType),fe=p[0],ne=(n=(a=D==null?void 0:D.originalData)==null?void 0:a.promotions)==null?void 0:n.find(P=>P.store_uid===fe),Ye={package_detail:{LstItem_Options:le.map(P=>({id:P.id,Name:P.name,LstItem:o.filter(Z=>P.itemIds.includes(Z.id)).map(Z=>{var Te;const L=(Te=P.itemSettings)==null?void 0:Te[Z.id],J=(L==null?void 0:L.finalPrice)||Z.ots_price||0,He=(L==null?void 0:L.discountPercent)||0,Me=(L==null?void 0:L.pricingMode)||"custom";return{item_id:Z.id,item_name:Z.item_name,ta_price:J,ots_price:J,discount_combo_item:He/100,state_change_price:Me==="custom"?1:2}}),Min_Permitted:P.required||0,Max_Permitted:P.max||0}))},deleted:!1,extra_data:{price_by_source:de.map(P=>({source_id:P.orderSource,price:parseFloat(P.amount)||0,price_times:P.price_times||[],is_source_exist_in_city:!0}))},vat_tax_rate:parseFloat(s.vat)/100||0,from_date:U.getTime(),to_date:ce.getTime(),time_sale_date_week:Re,time_sale_hour_day:Qe,sort:parseInt(s.displayOrder)||1e3,package_name:s.comboName,ots_value:parseFloat(s.price)||0,description:s.description||"",company_uid:v,brand_uid:c.id,ta_value:parseFloat(s.price)||0,use_same_data:p.length>1?1:0,image_path:h};if(d&&X&&X.length>0){const P=Vs(),Z=p.map(L=>{var He,Me,Te,is;const J=X.find(Ne=>Ne.store_uid===L);if(J){const Ne=(Me=(He=D==null?void 0:D.originalData)==null?void 0:He.promotions)==null?void 0:Me.find(Pe=>Pe.store_uid===L),ke=(Ne==null?void 0:Ne.promotion_uid)||J.promotion_uid||null;return{...J,...Ye,id:J.id,store_uid:L,promotion_id:s.ctrmType||J.promotion_id,promotion_uid:ke,item_type_uid:s.groupSelection||J.item_type_uid,updated_at:Date.now(),updated_by:P||null}}else{const{id:Ne,...ke}=X[0],Pe=(is=(Te=D==null?void 0:D.originalData)==null?void 0:Te.promotions)==null?void 0:is.find(Ls=>Ls.store_uid===L),Es=(Pe==null?void 0:Pe.promotion_uid)||null;return{...ke,...Ye,store_uid:L,promotion_id:s.ctrmType||ke.promotion_id,promotion_uid:Es,item_type_uid:s.groupSelection||ke.item_type_uid,created_at:Date.now(),created_by:P||null,updated_at:Date.now(),updated_by:P||null}}}).filter(Boolean);Ae(Z,{onSuccess:()=>{xe.success("Cập nhật combo thành công"),f.invalidateQueries({queryKey:["combos"]}),f.invalidateQueries({queryKey:["combo-detail",d]}),Xe()}})}else{const P={...Ye,store_uid:fe,package_id:s.comboCode||`COMBO-${Date.now()}`,promotion_id:s.ctrmType||"",promotion_uid:(ne==null?void 0:ne.promotion_uid)||"",item_type_uid:s.groupSelection||""};me(P,{onSuccess:()=>{xe.success("Tạo combo thành công"),f.invalidateQueries({queryKey:["combos"]}),Xe()}})}}catch(A){console.error("Error submitting combo:",A),xe.error("Có lỗi xảy ra khi lưu combo")}};return t?d&&qe?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"flex h-64 items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2"}),e.jsx("p",{className:"text-muted-foreground",children:"Đang tải thông tin combo..."})]})})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(F,{type:"button",variant:"ghost",size:"sm",onClick:Xe,className:"flex items-center",children:e.jsx(es,{className:"h-4 w-4"})}),e.jsx(F,{type:"submit",form:"combo-form",disabled:!E.formState.isValid||De||ae||be,children:De||ae||be?"Đang lưu...":"Lưu"})]}),e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:d?"Chi tiết combo":"Tạo combo mới"})})]}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"rounded-lg border bg-white p-6 shadow-sm",children:e.jsx($e,{...E,children:e.jsxs("form",{id:"combo-form",onSubmit:E.handleSubmit(Is),className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-base font-medium text-gray-700",children:"Thông tin chi tiết"}),e.jsxs("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-12",children:[e.jsxs("div",{className:"space-y-6 lg:col-span-9",children:[e.jsx(G,{control:E.control,name:"comboName",render:({field:s})=>e.jsxs(V,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(K,{className:"w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium",children:["Tên combo ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(R,{className:"flex-1",children:e.jsx(B,{placeholder:"Nhập tên combo",className:"w-full",...s})})]}),e.jsx($,{className:"mt-1 ml-36"})]})}),e.jsx(G,{control:E.control,name:"price",render:({field:s})=>e.jsxs(V,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(K,{className:"w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium",children:["Giá ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(R,{className:"flex-1",children:e.jsx(B,{placeholder:"0",className:"w-full",value:ue(s.value||""),onChange:a=>Ee(a.target.value,s.onChange)})})]}),e.jsx($,{className:"mt-1 ml-36"})]})}),e.jsx(G,{control:E.control,name:"description",render:({field:s})=>e.jsxs(V,{children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx(K,{className:"w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium",children:"Mô tả"}),e.jsx(R,{className:"flex-1",children:e.jsx(st,{placeholder:"",className:"w-full",rows:3,...s})})]}),e.jsx($,{className:"mt-1 ml-36"})]})})]}),e.jsx("div",{className:"flex justify-center lg:col-span-3 lg:justify-end",children:e.jsx(G,{control:E.control,name:"image",render:({field:s})=>e.jsxs(V,{children:[e.jsx(R,{children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-2 transition-colors hover:bg-gray-100",style:{width:"105.33px",height:"105.33px"},onClick:()=>{var a;return(a=document.getElementById("image-upload"))==null?void 0:a.click()},children:r?e.jsx("img",{src:r,alt:"Preview",className:"h-full w-full rounded-lg object-cover"}):e.jsxs(e.Fragment,{children:[e.jsx(xt,{className:"mb-1 h-6 w-6 text-gray-400"}),e.jsx("span",{className:"text-center text-xs text-gray-500",children:"Chọn ảnh"})]})}),r&&e.jsx("button",{type:"button",onClick:a=>{a.stopPropagation(),b(null),q(null),s.onChange(null)},className:"absolute -top-2 -right-2 rounded-full bg-red-500 p-1 text-white transition-colors hover:bg-red-600",children:e.jsx(gt,{className:"h-3 w-3"})}),e.jsx("input",{type:"file",accept:"image/*",onChange:async a=>{var A;const n=(A=a.target.files)==null?void 0:A[0];if(n){const h=new FileReader;h.onload=U=>{var ce;b((ce=U.target)==null?void 0:ce.result)},h.readAsDataURL(n),q(n),s.onChange(n)}},className:"hidden",id:"image-upload"})]})}),e.jsx($,{})]})})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(G,{control:E.control,name:"storeSelection",render:()=>e.jsxs(V,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(K,{className:"w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium",children:["Cửa hàng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(R,{className:"flex-1",children:e.jsx(F,{type:"button",variant:"outline",className:"w-full justify-start text-blue-600 hover:text-blue-700",onClick:Ss,disabled:Y,children:Y?"Đang tải...":p.length>0?`${p.length} cửa hàng`:"Chọn cửa hàng"})})]}),e.jsx($,{className:"mt-1 ml-36"})]})}),e.jsx(G,{control:E.control,name:"ctrmType",render:({field:s})=>{const a=()=>{const n=X&&X.length>0?X[0]:null;if(d&&n&&!s.value&&n.promotion_id){const A=x.find(h=>h.code===n.promotion_id);return(A==null?void 0:A.code)||""}return s.value};return e.jsxs(V,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(K,{className:"w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium",children:"CTKM"}),e.jsx(R,{className:"flex-1",children:e.jsxs(Be,{onValueChange:s.onChange,value:a(),disabled:p.length===0,children:[e.jsx(We,{className:"w-full",children:e.jsx(Ze,{placeholder:"Chọn CTKM có tại các cửa hàng áp dụng combo"})}),e.jsx(Je,{children:x.map(n=>e.jsx(Ie,{value:n.code,children:n.name},n.code))})]})})]}),e.jsx($,{className:"mt-1 ml-36"})]})}}),e.jsx(G,{control:E.control,name:"groupSelection",render:({field:s})=>{const a=()=>{const n=X&&X.length>0?X[0]:null;return d&&n&&!s.value&&n.item_type_uid?y.find(h=>h.id===n.item_type_uid)?n.item_type_uid:"":s.value};return e.jsxs(V,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(K,{className:"w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium",children:"Nhóm món"}),e.jsx(R,{className:"flex-1",children:e.jsxs(Be,{onValueChange:s.onChange,value:a(),disabled:p.length===0,children:[e.jsx(We,{className:"w-full",children:e.jsx(Ze,{placeholder:"Chọn nhóm món"})}),e.jsxs(Je,{children:[e.jsx(Ie,{value:"none",children:"None"}),y.map(n=>e.jsx(Ie,{value:n.id,children:n.item_type_name},n.id))]})]})})]}),e.jsx($,{className:"mt-1 ml-36"})]})}}),e.jsx(G,{control:E.control,name:"comboCode",render:({field:s})=>e.jsxs(V,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(K,{className:"w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium",children:"Mã combo"}),e.jsx(R,{className:"flex-1",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(B,{placeholder:ie?"Nhập mã combo (chỉ chữ, số, _, -)":"Hệ thống sẽ tự động tạo mã combo",className:"w-full",disabled:!ie,...s,onChange:a=>{const n=a.target.value.replace(/[^a-zA-Z0-9_-]/g,"");s.onChange(n)}}),e.jsx(ss,{checked:ie,onCheckedChange:a=>{_e(!!a),a||s.onChange("")}})]})})]}),e.jsx($,{className:"mt-1 ml-36"})]})}),e.jsx(G,{control:E.control,name:"vat",render:({field:s})=>e.jsxs(V,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(K,{className:"w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium",children:"VAT"}),e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx(R,{children:e.jsx(B,{placeholder:"0",className:"w-full",value:ue(s.value||""),onChange:a=>Ee(a.target.value,s.onChange)})}),e.jsx("span",{className:"text-sm",children:"%"})]})]}),e.jsx($,{className:"mt-1 ml-36"})]})})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-700",children:"Cấu hình giá theo nguồn"}),e.jsx(F,{type:"button",variant:"outline",size:"sm",onClick:Ds,children:"Thêm nguồn"})]}),de.length>0&&e.jsx("div",{className:"space-y-2",children:de.map((s,a)=>{var h;const n=Oe.find(U=>U.id===s.orderSource),A=s.timeFrameConfigs&&s.timeFrameConfigs.length>0;return e.jsxs("div",{className:"flex items-center justify-between rounded-lg bg-gray-50 p-3",children:[e.jsxs("div",{className:"flex flex-1 items-center justify-between",children:[e.jsx("div",{className:"cursor-pointer rounded p-1",onClick:()=>Os(a),children:e.jsxs("div",{className:"font-medium",children:[(n==null?void 0:n.sourceName)||s.orderSource," - Số tiền:"," ",ue(s.amount)," ₫"]})}),A&&e.jsx("div",{className:"flex h-6 w-6 items-center justify-center",children:e.jsxs(tt,{children:[e.jsx(at,{asChild:!0,children:e.jsx("div",{className:"flex h-4 w-4 cursor-help items-center justify-center rounded-full bg-blue-500 text-xs text-white",children:"i"})}),e.jsx(nt,{className:"w-96 p-3",children:e.jsx("div",{className:"space-y-2",children:e.jsx("div",{className:"mt-3 space-y-2",children:(h=s.timeFrameConfigs)==null?void 0:h.map((U,ce)=>e.jsxs("div",{className:"border-l-2 border-gray-300 pl-2 text-xs text-gray-700",children:[e.jsxs("div",{children:["● Từ ngày ",Le(U.startDate)," đến ngày"," ",Le(U.endDate)," Giá: ",U.amount," ₫"]}),e.jsxs("div",{className:"mt-1 text-gray-500",children:["Khung giờ"," ",js(Hs(U.selectedHours))," ",bs(As(U.selectedDays))]})]},ce))})})})]})})]}),e.jsx(F,{type:"button",variant:"ghost",size:"sm",onClick:()=>{const U=de.filter((ce,ye)=>ye!==a);pe(U)},children:e.jsx(es,{className:"h-4 w-4"})})]},a)})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-700",children:"Thứ tự hiển thị trong menu"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Combo có số nhỏ hơn sẽ được sắp xếp lên trên trong menu tại thiết bị bán hàng"}),e.jsx(G,{control:E.control,name:"displayOrder",render:({field:s})=>e.jsxs(V,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(K,{className:"w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium",children:"Thứ tự hiển thị"}),e.jsx(R,{className:"flex-1",children:e.jsx(B,{placeholder:"Nhập số thứ tự hiển thị",className:"w-full",...s})})]}),e.jsx($,{className:"mt-1 ml-36"})]})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-700",children:"Ngày áp dụng"}),e.jsxs("div",{className:"flex gap-6",children:[e.jsx(G,{control:E.control,name:"startDate",render:({field:s})=>e.jsxs(V,{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(K,{className:"w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium",children:"Ngày bắt đầu"}),e.jsx(R,{className:"flex-1",children:e.jsx(Ge,{date:s.value?new Date(s.value+"T00:00:00"):void 0,onDateChange:a=>as(a,s.onChange),placeholder:"Chọn ngày bắt đầu",className:"w-full"})})]}),e.jsx($,{className:"mt-1 ml-36"})]})}),e.jsx(G,{control:E.control,name:"endDate",render:({field:s})=>e.jsxs(V,{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(K,{className:"w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium",children:"Ngày kết thúc"}),e.jsx(R,{className:"flex-1",children:e.jsx(Ge,{date:s.value?new Date(s.value+"T00:00:00"):void 0,onDateChange:a=>as(a,s.onChange),placeholder:"Chọn ngày kết thúc",className:"w-full"})})]}),e.jsx($,{className:"mt-1 ml-36"})]})})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-700",children:"Khung thời gian bán"}),e.jsx(G,{control:E.control,name:"selectedDays",render:({field:s})=>e.jsxs(V,{children:[e.jsx(K,{className:"text-sm text-gray-600",children:"Chọn ngày"}),e.jsx("div",{className:"grid grid-cols-7 gap-2",children:["Thứ 2","Thứ 3","Thứ 4","Thứ 5","Thứ 6","Thứ 7","Chủ nhật"].map((a,n)=>{const A=Array.isArray(s.value)?s.value[n]:!1;return e.jsx(F,{type:"button",variant:A?"default":"outline",size:"sm",className:`text-xs ${A?"bg-blue-600 text-white":"border-blue-600 text-blue-600"}`,onClick:()=>{const h=Array.isArray(s.value)?[...s.value]:[!1,!1,!1,!1,!1,!1,!1];h[n]=!h[n],s.onChange(h)},children:a},a)})}),e.jsx($,{})]})}),e.jsx(G,{control:E.control,name:"selectedHours",render:({field:s})=>e.jsxs(V,{children:[e.jsx(K,{className:"text-sm text-gray-600",children:"Chọn giờ"}),e.jsx("div",{className:"grid grid-cols-10 gap-2",children:Array.from({length:24},(a,n)=>n).map(a=>{const n=a.toString(),A=`${a}h`,h=s.value&&s.value[n];return e.jsx(F,{type:"button",variant:h?"default":"outline",size:"sm",className:`text-xs ${h?"bg-blue-600 text-white":"border-blue-600 text-blue-600"}`,onClick:()=>{const U={...s.value};U[n]=!U[n],s.onChange(U)},children:A},a)})}),e.jsx($,{})]})}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-700",children:"Nhóm"}),e.jsx(F,{type:"button",variant:"outline",size:"sm",onClick:ws,children:"Tạo nhóm"})]}),le.map(s=>e.jsxs("div",{className:"mt-2 rounded-md border p-3",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-gray-500",children:"⇅"}),e.jsx("span",{className:"font-medium",children:s.name})]}),e.jsxs("div",{className:"flex items-center gap-1 text-sm",children:[e.jsx(F,{type:"button",variant:"link",size:"sm",className:"px-0 text-blue-500",onClick:()=>Ts(s.id),children:"Sửa"}),e.jsx("span",{className:"text-gray-300",children:"/"}),e.jsx(F,{type:"button",variant:"link",size:"sm",className:"px-0 text-blue-500",onClick:()=>ks(s.id),children:"Xoá"})]})]}),e.jsx("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3",children:s.itemIds.map(a=>{const n=o.find(A=>A.id===a);return n?e.jsxs("div",{className:"rounded-md border bg-gray-50 p-4",children:[e.jsx("div",{className:"font-medium",children:n.item_name}),e.jsxs("div",{className:"text-sm text-gray-600",children:["(",n.item_id,")"]}),e.jsx("div",{className:"mt-2 text-sm",children:"Giá gốc"})]},a):null})})]},s.id)),e.jsx("div",{})]})]})})})}),e.jsx(Ct,{open:m,onOpenChange:g,stores:Se,selectedStores:p,onConfirm:Cs,onCancel:_s}),e.jsx(vt,{open:l,onOpenChange:j,onConfirm:Ps,onCancel:Fs,selectedStoreIds:p,editingData:w>=0?de[w]:null,onOpenTimeFrameConfig:(s,a,n)=>{_(s),N(()=>a),W(n||null),M(!0)}}),e.jsx(yt,{open:je,onOpenChange:ee,onConfirm:s=>{ge(he==="edit"&&Q?a=>a.map(n=>n.id===Q?{...n,name:s.name,required:s.required,max:s.max,itemIds:s.items,itemSettings:s.itemSettings}:n):a=>[...a,{id:`${Date.now()}`,name:s.name,required:s.required,max:s.max,itemIds:s.items,itemSettings:s.itemSettings}]),ee(!1),oe(null),se("create")},onCancel:()=>{ee(!1),oe(null),se("create")},items:o,selectedItemIds:Q?((ns=le.find(s=>s.id===Q))==null?void 0:ns.itemIds)||[]:[],initialName:Q?(rs=le.find(s=>s.id===Q))==null?void 0:rs.name:void 0,initialRequired:Q?(ls=le.find(s=>s.id===Q))==null?void 0:ls.required:void 0,initialMax:Q?(cs=le.find(s=>s.id===Q))==null?void 0:cs.max:void 0,initialItemSettings:Q?(os=le.find(s=>s.id===Q))==null?void 0:os.itemSettings:void 0}),e.jsx(wt,{open:H,onOpenChange:M,onConfirm:s=>{I&&I(s),M(!1),_(""),N(null),W(null)},onCancel:Ms,sourceName:z,editingData:re})]}):null}export{aa as C,oa as a,ca as b,ra as c,na as d,la as u};
