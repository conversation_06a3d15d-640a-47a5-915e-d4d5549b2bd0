import{r as t}from"./index-C21OP4ex.js";/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var u={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const f=(i,s,a,l)=>{const r=t.forwardRef(({color:c="currentColor",size:o=24,stroke:w=2,title:n,className:m,children:e,...h},p)=>t.createElement("svg",{ref:p,...u[i],width:o,height:o,className:["tabler-icon",`tabler-icon-${s}`,m].join(" "),strokeWidth:w,stroke:c,...h},[n&&t.createElement("title",{key:"svg-title"},n),...l.map(([d,g])=>t.createElement(d,g)),...Array.isArray(e)?e:[e]]));return r.displayName=`${a}`,r};export{f as c};
