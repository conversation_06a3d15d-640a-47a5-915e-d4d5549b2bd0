import{j as e}from"./index-Bnt3OGV2.js";const n=function(){return e.jsxs("div",{className:"container mx-auto p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Bức tranh khách hàng"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"<PERSON>ân tích chi tiết hành vi và đặc điểm của khách hàng"})]}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:e.jsx("svg",{className:"mx-auto h-12 w-12",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Bức tranh khách hàng"}),e.jsx("p",{className:"text-gray-500",children:"Nội dung phân tích sẽ được phát triển ở giai đoạn tiếp theo"})]})})]})};export{n as component};
