import{r as v,j as c,R as Z,u as L,a3 as K,a4 as F,B as H,l as it,z as V}from"./index-C21OP4ex.js";import{u as ot}from"./use-dialog-state-BYP8UC3r.js";import{u as Q}from"./useQuery-BNGphiae.js";import{Q as T}from"./query-keys-3lmd-xp6.js";import{b as x}from"./pos-api-D5WM5mnz.js";import"./vietqr-api-ruJT0-tj.js";import{u as at}from"./use-item-types-DA0U4OWS.js";import{u as st}from"./use-item-classes-_IC65iw9.js";import{u as rt}from"./use-units-dM9GTfDw.js";import"./user-CgNoQQSj.js";import"./crm-api-BUMUQ8t4.js";import{C as O}from"./checkbox-DUpnJ1Rx.js";import{I as ct}from"./input-4sMIt001.js";import{P as lt}from"./modal-CFi1vCFt.js";import"./date-range-picker-B1pgj5D_.js";import{u as dt,F as ut,a as mt,b as ht,c as _t,d as gt,e as pt}from"./form-usWdQ_Nt.js";import{C as J}from"./chevron-right-BAjIoZMb.js";import{C as X}from"./select-B8Pw9rS-.js";import{s as yt}from"./zod-B4gLZVLM.js";import{D as ft,a as bt,b as It,c as Ct,e as Ft,f as wt}from"./dialog-DXjwjGKV.js";import{C as xt}from"./combobox-DyrNt90A.js";import{E as vt}from"./exceljs.min-BFFGgdR1.js";import{u as j}from"./useMutation-Bh5DVQPI.js";const tt=Z.createContext(null);function he({children:t}){const[n,e]=ot(null),[i,s]=v.useState(null);return c.jsx(tt,{value:{open:n,setOpen:e,currentRow:i,setCurrentRow:s},children:t})}const _e=()=>{const t=Z.useContext(tt);if(!t)throw new Error("useItemsInCity has to be used within <ItemsInCityContext>");return t},Y=()=>{const t=new vt.Workbook;return t.creator="POS System",t.lastModifiedBy="POS System",t.created=new Date,t.modified=new Date,t},Tt=()=>["ID","Mã món","Thành phố","Tên","Giá","Trạng thái","Mã barcode","Món ăn kèm","Không cập nhật số lượng món ăn kèm","Đơn vị","Nhóm","Tên nhóm","Loại món","Tên loại","Mô tả","SKU","VAT (%)","Thời gian chế biến (phút)","Cho phép sửa giá khi bán","Cấu hình món ảo","Cấu hình món dịch vụ","Cấu hình món ăn là vé buffet","Giờ","Ngày","Thứ tự","Hình ảnh","Công thức inQR cho máy pha trà"],et=t=>{const n=Tt(),e=t.addWorksheet("Menu");return e.addRow(n).eachCell(a=>{a.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},a.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},a.alignment={horizontal:"center",vertical:"middle"},a.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),[36,15,12,25,12,12,15,15,35,12,15,12,15,12,25,15,12,25,25,20,25,35,12,12,12,25,35].forEach((a,_)=>{e.getColumn(_+1).width=a}),e},Mt=()=>["ID","Mã món","Thành phố","Tên","Giá (Mặc định 0)","Trạng thái (Mặc định 1)","Mã barcode (Tối đa 13)","Món ăn kèm (Mặc định 0)","Không cập nhật số lượng món ăn kèm (Mặc định 1)","Đơn vị (Mặc định MON)","Nhóm (Mặc định LOẠI KHÁC)","Tên nhóm","Loại món (Mặc định rỗng)","Tên loại","Mô tả","SKU (Tối đa 50)","VAT (%) (Mặc định 0)","Thời gian chế biến (phút) (Mặc định 0)","Cho phép sửa giá khi bán (Mặc định 0)","Cấu hình món ảo (Mặc định 0)","Cấu hình món dịch vụ (Mặc định 0)","Cấu hình món ăn là vé buffet (Mặc định 0)","Giờ (Mặc định 0)","Ngày (Mặc định 0)","Thứ tự (Mặc định 0)","Hình ảnh","Công thức inQR cho máy pha trà"],nt=()=>["Thành phố","Tên","Giá (Mặc định 0)","Mã món","Mã barcode (Tối đa 13)","Món ăn kèm (Mặc định 0)","Không cập nhật số lượng món ăn kèm (Mặc định 0)","Nhóm (Mặc định LOẠI KHÁC)","Loại món (Mặc định rỗng)","Mô tả","SKU (tối đa 50)","Đơn vị (Mặc định MON)","VAT (%) (Mặc định 0)","Thời gian chế biến (phút) (Mặc định 0)","Cho phép sửa giá khi bán (Mặc định 0)","Cấu hình món ảo (Mặc định 0)","Cấu hình món dịch vụ (Mặc định 0)","Cấu hình món ăn là vé buffet (Mặc định 0)","Ngày (Mặc định 0)","Giờ (Mặc định 0)","Hình ảnh"],St=()=>[["d9692391-3f3f-4754-9416-878d2d8b52ce","ITEM-3279","Hồ Chí Minh","Cà Phê Sữa (L)",0,1,"",0,1,"AM","ITEM_TYPE_OTHER","Uncategory","DA","Đồ ăn","không","",0,10,14,0,0,1,2064384,224,0,"https://image.foodbook.vn/images/20250829/1756431019051-anh-test-may-in-mau-chat-luong-hinh-2.jpg",""],["8119567b-f80e-43ac-8c85-012dd8f56b18","KHOAI","Hồ Chí Minh","pomato",0,1,"",1,1,"TRAI","MAK","MÓN ĂN KÈM","MA","manh","","",0,5,14,1,0,1,6355002,254,0,"https://image.foodbook.vn/images/20250829/1756450169874-vo_tri_2.jpg",""]],kt=()=>[["Chủ nhật","2"],["Thứ 2","4"],["Thứ 3","8"],["Thứ 4","16"],["Thứ 5","32"],["Thứ 6","64"],["Thứ 7","128"],["Ví dụ: CN, T2, T5 = 2 + 4 + 32","38"]],Et=()=>[["0h","1"],["1h","2"],["2h","4"],["3h","8"],["4h","16"],["5h","32"],["6h","64"],["7h","128"],["8h","256"],["9h","512"],["10h","1024"],["11h","2048"],["12h","4096"],["13h","8192"],["14h","16384"],["15h","32768"],["16h","65536"],["17h","131072"],["18h","262144"],["19h","524288"],["20h","1048576"],["21h","2097152"],["22h","4194304"],["23h","8388608"],["Ví dụ: 0h, 1h, 3h = 1 + 2 + 8","11"]],Nt=t=>{const n=t.filter(e=>e.active===1).map(e=>[e.item_type_id,e.item_type_name]);return n.length>0?n:[["Không có dữ liệu",""]]},Rt=t=>{const n=t.filter(e=>e.active===1).map(e=>[e.item_class_id,e.item_class_name]);return n.length>0?n:[["Không có dữ liệu",""]]},At=t=>{if(!t||t.length===0)return[["Không có dữ liệu",""]];const n=t.map(e=>[e.unit_id,e.unit_name]);return n.length>0?n:[["Không có dữ liệu",""]]},jt=(t,n)=>{var C,S,P,q,E,N,A,k,U,W;const e=Mt(),i=t.addWorksheet("Template"),s=i.addRow(['Đây là sheet mẫu để tham khảo. Vui lòng quay lại sheet "Menu" để nhập dữ liệu.']);i.mergeCells(`A${s.number}:AA${s.number}`),s.getCell(1).fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},s.getCell(1).font={color:{argb:"FFFFFFFF"},bold:!0,size:11},s.getCell(1).alignment={horizontal:"left",vertical:"middle"},s.getCell(1).border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}},i.addRow(e).eachCell(m=>{m.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},m.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},m.alignment={horizontal:"center",vertical:"middle"},m.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),St().forEach(m=>{i.addRow(m).eachCell(f=>{f.font={size:10},f.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}}})}),i.addRow([]);const g=i.addRow(["BẢNG THAM CHIẾU NGÀY","","","BẢNG THAM CHIẾU GIỜ","","","BẢNG THAM CHIẾU NHÓM MÓN","","","BẢNG THAM CHIẾU LOẠI MÓN","","","BẢNG THAM CHIẾU ĐƠN VỊ",""]),o=g.number;i.mergeCells(`A${o}:B${o}`),i.mergeCells(`D${o}:E${o}`),i.mergeCells(`G${o}:H${o}`),i.mergeCells(`J${o}:K${o}`),i.mergeCells(`M${o}:N${o}`),g.eachCell(m=>{m.value&&m.value.toString().trim()!==""&&(m.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},m.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},m.alignment={horizontal:"center",vertical:"middle"},m.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}})}),i.addRow(["Thời gian","Giá trị","","Thời gian","Giá trị","","Mã nhóm","Tên nhóm","","Mã loại món","Tên loại món","","Mã đơn vị","Tên đơn vị"]).eachCell(m=>{m.value&&m.value.toString().trim()!==""&&(m.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF79ABE3"}},m.font={bold:!0,size:10},m.alignment={horizontal:"center",vertical:"middle"},m.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}})});const d=kt(),u=Et(),y=n!=null&&n.itemTypes?Nt(n.itemTypes):[["Không có dữ liệu",""]],l=n!=null&&n.itemClasses?Rt(n.itemClasses):[["Không có dữ liệu",""]],p=n!=null&&n.units?At(n.units):[["Không có dữ liệu",""]],I=Math.max(d.length,u.length,y.length,l.length,p.length),b=[];for(let m=0;m<I;m++){const h=[((C=d[m])==null?void 0:C[0])||"",((S=d[m])==null?void 0:S[1])||"","",((P=u[m])==null?void 0:P[0])||"",((q=u[m])==null?void 0:q[1])||"","",((E=y[m])==null?void 0:E[0])||"",((N=y[m])==null?void 0:N[1])||"","",((A=l[m])==null?void 0:A[0])||"",((k=l[m])==null?void 0:k[1])||"","",((U=p[m])==null?void 0:U[0])||"",((W=p[m])==null?void 0:W[1])||""];b.push(h)}return b.forEach(m=>{i.addRow(m).eachCell(f=>{f.value&&f.value.toString().trim()!==""&&(f.font={size:10},f.alignment={horizontal:"left",vertical:"middle"},f.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}})})}),[36,15,12,25,12,12,15,15,35,12,15,12,15,12,25,15,12,25,25,20,25,35,12,12,12,25,35].forEach((m,h)=>{i.getColumn(h+1).width=m}),i},Pt=t=>{const n=nt(),e=t.addWorksheet("Menu");return e.addRow(n).eachCell(a=>{a.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},a.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},a.alignment={horizontal:"center",vertical:"middle"},a.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),[12,25,12,15,15,15,35,15,15,25,15,12,12,25,25,20,25,35,12,12,25].forEach((a,_)=>{e.getColumn(_+1).width=a}),e},Dt=(t,n)=>{const e=nt(),i=t.addWorksheet("Template"),s=i.addRow(['Đây là sheet mẫu để tham khảo. Vui lòng quay lại sheet "Menu" để nhập dữ liệu.']);return s.font={bold:!0,size:12,color:{argb:"FF0560A6"}},s.alignment={horizontal:"center",vertical:"middle"},i.mergeCells("A1:U1"),i.addRow([]),i.addRow(e).eachCell(o=>{o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},o.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},o.alignment={horizontal:"center",vertical:"middle"},o.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),[["Hà Nội","Món 1",1e5,"TRA_SUA_SO_1_YQ4P","",0,0,"MONN_KEMVK84","ITEM_CLASS-9ZKE","Món đắt","item-121","COC",8,12,0,0,0,0,124,12,"https://img.foodbook.vn/images/20191202/1575271193305-image.jpg"],["Đà Nẵng","Món 2",99999,"ITEM_KFHE","1281-II",1,1,"ITEMTYPE_BC56","ITEM_CLASS-12OP","Món ăn ngon","ITEM111","LANG",12,20,1,1,1,1,0,0,"https://img.foodbook.vn/images/20191107/1573097661136-image.jpg"]].forEach(o=>{i.addRow(o)}),[12,25,12,15,15,15,35,15,15,25,15,12,12,25,25,20,25,35,12,12,25].forEach((o,r)=>{i.getColumn(r+1).width=o}),i},ge=async(t,n)=>{try{const e=Y(),i=et(e);if(n&&n.length>0){const d=l=>{var I;const p=(I=t==null?void 0:t.itemTypes)==null?void 0:I.find(b=>b.id===l);return{name:(p==null?void 0:p.item_type_name)||l,id:(p==null?void 0:p.item_type_id)||l}},u=l=>{var I;const p=(I=t==null?void 0:t.itemClasses)==null?void 0:I.find(b=>b.id===l);return{name:(p==null?void 0:p.item_class_name)||l,id:(p==null?void 0:p.item_class_id)||l}},y=l=>{var I;const p=(I=t==null?void 0:t.units)==null?void 0:I.find(b=>b.id===l);return{name:(p==null?void 0:p.unit_name)||l,id:(p==null?void 0:p.unit_id)||l}};n.forEach(l=>{var C;const I=((C=(l.cities||[])[0])==null?void 0:C.city_name)||"",b=l.extra_data||{},M=[l.id||"",l.item_id||"",I,l.item_name||"",l.ta_price||0,l.active||1,l.item_id_barcode||"",l.is_eat_with||0,b.no_update_quantity_toping||1,y(l.unit_uid).id,d(l.item_type_uid).id,d(l.item_type_uid).name,u(l.item_class_uid).id,u(l.item_class_uid).name,l.description||"",l.item_id_mapping||"",l.ta_tax*100||0,Math.round(l.time_cooking/6e4)||0,b.enable_edit_price||0,b.is_virtual_item||0,b.is_item_service||0,b.is_buffet_item||0,l.time_sale_hour_day||0,l.time_sale_date_week||0,l.sort,l.image_path||"",b.formula_qrcode||""];i.addRow(M)})}jt(e,t);const s=await e.xlsx.writeBuffer(),a=new Blob([s],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),g=`items_export_template_${new Date().toISOString().slice(0,19).replace(/:/g,"-")}.xlsx`,o=window.URL.createObjectURL(a),r=document.createElement("a");return r.href=o,r.download=g,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(o),Promise.resolve()}catch(e){return console.error("Error creating Excel file:",e),Promise.reject(e)}},zt=async t=>{try{const n=Y();Pt(n),Dt(n,t);const e=await n.xlsx.writeBuffer(),i=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),a=`items_import_template_${new Date().toISOString().slice(0,19).replace(/:/g,"-")}.xlsx`,_=window.URL.createObjectURL(i),g=document.createElement("a");return g.href=_,g.download=a,document.body.appendChild(g),g.click(),document.body.removeChild(g),window.URL.revokeObjectURL(_),Promise.resolve()}catch(n){return console.error("Error creating import Excel file:",n),Promise.reject(n)}},Lt=async(t,n)=>{try{const e=Y(),i=et(e),s=o=>{var d;const r=(d=n==null?void 0:n.itemTypes)==null?void 0:d.find(u=>u.id===o);return{name:(r==null?void 0:r.item_type_name)||o,id:(r==null?void 0:r.item_type_id)||o}},a=o=>{var d;const r=(d=n==null?void 0:n.itemClasses)==null?void 0:d.find(u=>u.id===o);return{name:(r==null?void 0:r.item_class_name)||o,id:(r==null?void 0:r.item_class_id)||o}},_=o=>{var d;const r=(d=n==null?void 0:n.units)==null?void 0:d.find(u=>u.id===o);return{name:(r==null?void 0:r.unit_name)||o,id:(r==null?void 0:r.unit_id)||o}};t.forEach(o=>{var l;const d=((l=(o.cities||[])[0])==null?void 0:l.city_name)||"",u=o.extra_data||{},y=[o.id||"",o.item_id||"",d,o.item_name||"",o.ta_price||0,o.active||1,o.item_id_barcode||"",o.is_eat_with||0,u.no_update_quantity_toping||1,_(o.unit_uid).id,s(o.item_type_uid).id,s(o.item_type_uid).name,a(o.item_class_uid).id,a(o.item_class_uid).name,o.description||"",o.item_id_mapping||"",o.ta_tax*100||0,Math.round(o.time_cooking/6e4)||0,u.enable_edit_price||0,u.is_virtual_item||0,u.is_item_service||0,u.is_buffet_item||0,o.time_sale_hour_day||0,o.time_sale_date_week||0,o.sort,o.image_path||"",u.formula_qrcode||""];i.addRow(y)});const g=await e.xlsx.writeBuffer();return new Blob([g],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"})}catch(e){throw console.error("Error creating Excel blob:",e),e}},qt=()=>{try{const t=localStorage.getItem("pos_cities_data");if(t)return JSON.parse(t).map(e=>e.id)}catch{}return[]},G=t=>{if(!t)return null;if(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(t))return t;try{const e=localStorage.getItem("pos_cities_data");if(e){const s=JSON.parse(e).find(a=>a.city_name===t);return(s==null?void 0:s.id)||null}}catch{return null}return null},R=new Map,$=new Map,Ht=10*60*1e3;function z(t){return typeof t=="object"&&t!==null&&"response"in t}const w={getItemsInCity:async t=>{const n=t.city_uid&&t.city_uid!=="all"?G(t.city_uid):t.city_uid,e=t.active!==void 0?t.active.toString():"undefined",i=t.skip_limit?"true":"false",s=`${t.company_uid}-${t.brand_uid}-${t.page||1}-${n||"all"}-${t.list_city_uid||"all"}-${t.item_type_uid||"all"}-${t.time_sale_date_week||""}-${e}-${t.reverse||0}-${t.search||""}-${t.limit||50}-${i}`,a=R.get(s);if(a&&Date.now()-a.timestamp<Ht)return a.data;const _=$.get(s);if(_)return _;const g=(async()=>{try{const o=new URLSearchParams;if(o.append("company_uid",t.company_uid),o.append("brand_uid",t.brand_uid),t.page&&o.append("page",t.page.toString()),t.item_type_uid&&o.append("item_type_uid",t.item_type_uid),t.list_city_uid)o.append("list_city_uid",t.list_city_uid);else if(t.city_uid&&t.city_uid!=="all"){const u=G(t.city_uid);u&&o.append("city_uid",u)}else{const u=qt();u.length>0&&o.append("list_city_uid",u.join(","))}t.time_sale_date_week&&o.append("time_sale_date_week",t.time_sale_date_week),t.reverse!==void 0&&o.append("reverse",t.reverse.toString()),t.search&&o.append("search",t.search),t.active!==void 0&&o.append("active",t.active.toString()),t.limit&&o.append("limit",t.limit.toString()),t.skip_limit&&o.append("skip_limit","true");const r=await x.get(`/mdata/v1/items?${o.toString()}`);if(!r.data||typeof r.data!="object")throw new Error("Invalid response format from items in city API");const d=r.data;return R.set(s,{data:d,timestamp:Date.now()}),d}finally{$.delete(s)}})();return $.set(s,g),g},deleteItemInCity:async t=>{var n;try{const e=new URLSearchParams;e.append("company_uid",t.company_uid),e.append("brand_uid",t.brand_uid),e.append("id",t.id),await x.delete(`/mdata/v1/item?${e.toString()}`),R.clear()}catch(e){throw z(e)&&((n=e.response)==null?void 0:n.status)===404?new Error("Item not found."):e}},deleteMultipleItemsInCity:async t=>{var n;try{const e=new URLSearchParams;e.append("company_uid",t.company_uid),e.append("brand_uid",t.brand_uid),e.append("list_item_uid",t.list_item_uid.join(",")),await x.delete(`/mdata/v1/items?${e.toString()}`),R.clear()}catch(e){throw z(e)&&((n=e.response)==null?void 0:n.status)===404?new Error("Items not found."):e}},downloadTemplate:async t=>{var a;const n=t.city_uid&&t.city_uid!=="all"?G(t.city_uid):null,e=new URLSearchParams({skip_limit:"true",company_uid:t.company_uid,brand_uid:t.brand_uid,...n&&{city_uid:n},...t.item_type_uid&&t.item_type_uid!=="all"&&{item_type_uid:t.item_type_uid},...t.active&&t.active!=="all"&&{active:t.active}}),i=await x.get(`/mdata/v1/items?${e}`),s=Array.isArray((a=i.data)==null?void 0:a.data)?i.data.data:[];return await Lt(s,t.referenceData)},fetchItemsData:async t=>{var s;const n=t.city_uid&&t.city_uid!=="all"?G(t.city_uid):null,e=new URLSearchParams({skip_limit:"true",company_uid:t.company_uid,brand_uid:t.brand_uid,...n&&{city_uid:n},...t.item_type_uid&&t.item_type_uid!=="all"&&{item_type_uid:t.item_type_uid},...t.active&&t.active!=="all"&&{active:t.active}}),i=await x.get(`/mdata/v1/items?${e}`);return Array.isArray((s=i.data)==null?void 0:s.data)?i.data.data:[]},createItemInCity:async t=>{var n,e;try{const i=await x.post("/mdata/v1/item",t);return R.clear(),i.data.data||i.data}catch(i){throw z(i)&&((n=i.response)==null?void 0:n.status)===400?new Error(((e=i.response.data)==null?void 0:e.message)||"Invalid data provided."):i}},updateItemInCity:async t=>{var n,e;try{const i=await x.put("/mdata/v1/item",t,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return R.clear(),i.data.data||i.data}catch(i){throw z(i)&&((n=i.response)==null?void 0:n.status)===400?new Error(((e=i.response.data)==null?void 0:e.message)||"Invalid data provided."):i}},getItemByListId:async t=>{var n,e;try{const i=new URLSearchParams({skip_limit:"true",company_uid:t.company_uid,brand_uid:t.brand_uid,is_all:"true",list_item_id:t.list_item_id}),s=await x.get(`/mdata/v1/items?${i}`,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4}),a=Array.isArray((n=s.data)==null?void 0:n.data)?s.data.data:[];if(!a.length)throw new Error("Item not found");return{data:a[0]}}catch(i){throw z(i)&&((e=i.response)==null?void 0:e.status)===404?new Error("Item not found."):i}},getItemById:async t=>{var n;try{const e=new URLSearchParams;e.append("id",t.id),t.company_uid&&e.append("company_uid",t.company_uid),t.brand_uid&&e.append("brand_uid",t.brand_uid);const i=await x.get(`/mdata/v1/item?${e.toString()}`);if(!i.data||typeof i.data!="object")throw new Error("Invalid response format from item detail API");return i.data}catch(e){throw z(e)&&((n=e.response)==null?void 0:n.status)===404?new Error("Item not found."):e}},importItems:async t=>(await x.post("/mdata/v1/items/import",{company_uid:t.company_uid,brand_uid:t.brand_uid,items:t.items})).data,updateItemStatus:async t=>{var n,e;try{const s={...(await w.getItemById({id:t.id})).data,active:t.active,company_uid:t.company_uid,brand_uid:t.brand_uid},a=await x.put("/mdata/v1/item",s,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return R.clear(),a.data.data||a.data}catch(i){throw z(i)&&((n=i.response)==null?void 0:n.status)===400?new Error(((e=i.response.data)==null?void 0:e.message)||"Invalid data provided."):i}},downloadImportTemplate:async t=>{try{await zt(t)}catch(n){throw console.error("Error creating import template:",n),n}},clearCache:()=>{R.clear(),$.clear()},getCacheStats:()=>({cacheSize:R.size,pendingRequests:$.size})},Kt=(t={})=>{const{params:n={},enabled:e=!0}=t,{company:i,brands:s}=L(l=>l.auth),a=s==null?void 0:s[0],_={company_uid:(i==null?void 0:i.id)||"",brand_uid:(a==null?void 0:a.id)||"",page:1,reverse:1,limit:50,...n},g=!!(i!=null&&i.id&&(a!=null&&a.id)),o=Q({queryKey:[T.ITEMS_IN_CITY_LIST,JSON.stringify(_)],queryFn:async()=>(await w.getItemsInCity(_)).data||[],enabled:e&&g,staleTime:5*60*1e3,refetchInterval:10*60*1e3}),r={..._,page:(_.page||1)+1},d=Q({queryKey:[T.ITEMS_IN_CITY_LIST,"next",JSON.stringify(r)],queryFn:async()=>(await w.getItemsInCity(r)).data||[],enabled:e&&g&&(o.data?o.data.length>0:!1),staleTime:2*60*1e3,gcTime:5*60*1e3}),u=_.limit||50,y=(d.data?d.data.length>0:!1)||(o.data?o.data.length===u:!1);return{data:o.data,isLoading:o.isLoading,error:o.error,refetch:o.refetch,isFetching:o.isFetching,nextPageData:d.data||[],hasNextPage:y}},pe=(t,n=!0)=>Q({queryKey:[T.ITEMS_IN_CITY_DETAIL,t],queryFn:()=>w.getItemById({id:t}),enabled:n&&!!t,staleTime:5*60*1e3}),ye=(t={})=>{var o,r,d;const n=Kt(t),e=(o=t.params)==null?void 0:o.city_uid,i=(r=t.params)==null?void 0:r.list_city_uid,{data:s=[]}=at({skip_limit:!0,...e&&e!=="all"?{city_uid:e}:{},...i?{list_city_uid:i}:{}}),{data:a=[]}=st({skip_limit:!0}),{data:_=[]}=rt();return{data:((d=n.data)==null?void 0:d.map(u=>{var N,A;const y=u,p=((N=(y.cities||[])[0])==null?void 0:N.city_name)||"",I=y.item_type_uid?s.find(k=>k.id===y.item_type_uid):null,b=(I==null?void 0:I.item_type_name)||"",M=y.item_class_uid?a.find(k=>k.id===y.item_class_uid):null,C=(M==null?void 0:M.item_class_name)||"",S=y.unit_uid?_.find(k=>k.id===y.unit_uid):null,P=(S==null?void 0:S.unit_name)||"",E=y.is_eat_with===1||y.item_id_eat_with&&y.item_id_eat_with!==""?y.item_id_eat_with||"Món ăn kèm":"";return{...u,code:y.item_id||"",name:u.item_name,price:u.ots_price,vatPercent:u.ots_tax,cookingTime:u.time_cooking,categoryGroup:b,itemType:b,itemClass:C,unit:P,sideItems:E||void 0,city:p,buffetConfig:((A=y.extra_data)==null?void 0:A.is_buffet_item)===1?"Đã cấu hình":"Chưa cấu hình",customization:y.customization_uid||void 0,isActive:!!u.active,createdAt:typeof u.created_at=="number"?new Date(u.created_at*1e3):new Date(new Date(u.created_at).getTime())}}))||[],isLoading:n.isLoading,error:n.error,refetch:n.refetch,isFetching:n.isFetching,nextPageData:n.nextPageData,hasNextPage:n.hasNextPage}},fe=()=>{const t=K(),n=j({mutationFn:e=>w.createItemInCity(e),onSuccess:()=>{w.clearCache(),t.invalidateQueries({queryKey:[T.ITEMS_IN_CITY_LIST]}),F.success("Tạo món thành công!")},onError:e=>{F.error(e.message||"Có lỗi xảy ra khi tạo món")}});return{createItemAsync:n.mutateAsync,isPending:n.isPending}},$t=()=>{const t=K(),n=j({mutationFn:e=>w.updateItemInCity(e),onSuccess:()=>{w.clearCache(),t.invalidateQueries({queryKey:[T.ITEMS_IN_CITY_LIST]}),t.invalidateQueries({queryKey:[T.ITEMS_IN_CITY_DETAIL]}),F.success("Cập nhật món thành công!")},onError:e=>{F.error(e.message||"Có lỗi xảy ra khi cập nhật món")}});return{updateItemAsync:n.mutateAsync,isPending:n.isPending}},be=()=>{const t=K(),{company:n,brands:e}=L(a=>a.auth),i=e==null?void 0:e[0],s=j({mutationFn:a=>{const _={company_uid:(n==null?void 0:n.id)||"",brand_uid:(i==null?void 0:i.id)||"",id:a};return w.deleteItemInCity(_)},onSuccess:()=>{t.invalidateQueries({queryKey:[T.ITEMS_IN_CITY_LIST]}),F.success("Xóa món thành công!")},onError:a=>{F.error(a.message||"Có lỗi xảy ra khi xóa món")}});return{deleteItemAsync:s.mutateAsync,isPending:s.isPending}},Ie=()=>{const t=K(),{company:n,brands:e}=L(a=>a.auth),i=e==null?void 0:e[0],s=j({mutationFn:a=>{const _={company_uid:(n==null?void 0:n.id)||"",brand_uid:(i==null?void 0:i.id)||"",list_item_uid:a};return w.deleteMultipleItemsInCity(_)},onSuccess:()=>{t.invalidateQueries({queryKey:[T.ITEMS_IN_CITY_LIST]}),F.success("Xóa món ăn thành công")},onError:a=>{F.error((a==null?void 0:a.message)||"Có lỗi xảy ra khi xóa món ăn")}});return{deleteMultipleItemsAsync:s.mutateAsync,isPending:s.isPending}},Ce=()=>{const t=K(),{company:n,brands:e}=L(a=>a.auth),i=e==null?void 0:e[0],s=j({mutationFn:a=>{const _={id:a.id,active:a.active,company_uid:(n==null?void 0:n.id)||"",brand_uid:(i==null?void 0:i.id)||""};return w.updateItemStatus(_)},onSuccess:()=>{w.clearCache(),t.invalidateQueries({queryKey:[T.ITEMS_IN_CITY_LIST]}),t.invalidateQueries({queryKey:[T.ITEMS_IN_CITY_DETAIL]}),F.success("Cập nhật trạng thái thành công!")},onError:a=>{F.error(a.message||"Có lỗi xảy ra khi cập nhật trạng thái")}});return{updateStatusAsync:s.mutateAsync,isPending:s.isPending}},Fe=()=>{const t=j({mutationFn:n=>w.downloadImportTemplate(n),onSuccess:()=>{F.success("Tải template thành công!")},onError:n=>{F.error(n.message||"Có lỗi xảy ra khi tải template")}});return{downloadImportTemplateAsync:t.mutateAsync,isPending:t.isPending}},we=()=>{const{company:t,brands:n}=L(s=>s.auth),e=n==null?void 0:n[0],i=j({mutationFn:s=>{const a={company_uid:(t==null?void 0:t.id)||"",brand_uid:(e==null?void 0:e.id)||"",...s};return w.fetchItemsData(a)},onError:s=>{F.error(s.message||"Có lỗi xảy ra khi tải dữ liệu")}});return{fetchItemsDataAsync:i.mutateAsync,isPending:i.isPending}},xe=()=>{const t=K(),{company:n,brands:e}=L(a=>a.auth),i=e==null?void 0:e[0],s=j({mutationFn:a=>{const _={company_uid:(n==null?void 0:n.id)||"",brand_uid:(i==null?void 0:i.id)||"",items:a};return w.importItems(_)},onSuccess:()=>{t.invalidateQueries({queryKey:[T.ITEMS_IN_CITY_LIST]}),F.success("Import món thành công!")},onError:a=>{F.error(a.message||"Có lỗi xảy ra khi import món")}});return{importItemsAsync:s.mutateAsync,isPending:s.isPending}};function ve({itemsBuffet:t,open:n,onOpenChange:e,onItemsChange:i,items:s,hide:a=!0,enable:_=!0,onEnableChange:g}){const[o,r]=v.useState(""),[d,u]=v.useState([]),[y,l]=v.useState(!1),[p,I]=v.useState(!1),[b,M]=v.useState(!1);v.useEffect(()=>{n&&(u(Array.isArray(t)?t:[]),M(_))},[t,n]);const C=v.useMemo(()=>o?s.filter(h=>{var f;return(f=h.item_name)==null?void 0:f.toLowerCase().includes(o.toLowerCase())}):s,[s,o]),S=v.useMemo(()=>C.length?C.filter(h=>d.includes(h.item_id||"")):[],[C,d]),P=v.useMemo(()=>C.length?C.filter(h=>!d.includes(h.item_id||"")):[],[C,d]),q=h=>{u(f=>f.includes(h)?f.filter(D=>D!==h):[...f,h])},E=S.length,N=C.length,A=N>0&&E===N,k=E>0&&E<N,U=()=>{if(A){const h=C.map(f=>f.item_id);u(f=>f.filter(D=>!h.includes(D)))}else{const h=C.map(f=>f.item_id);u(f=>{const D=[...f];return h.forEach(B=>{D.includes(B||"")||D.push(B||"")}),D})}},W=()=>{i(d),e(!1)},m=()=>{u([]),e(!1)};return c.jsx(lt,{title:"Chọn danh sách món không đi kèm vé buffet",centerTitle:!0,open:n,onOpenChange:e,onCancel:m,onConfirm:W,confirmText:"Lưu",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",children:c.jsxs("div",{className:"space-y-4",children:[!a&&c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx(O,{id:"enable-buffet",checked:b,onCheckedChange:h=>{const f=!!h;M(f),g==null||g(f)}}),c.jsx("label",{htmlFor:"enable-buffet",className:"cursor-pointer text-blue-600",onClick:()=>M(h=>!h),children:"Cấu hình món ăn là vé buffet"})]}),(a||b)&&c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"flex items-center gap-2",children:c.jsx(ct,{placeholder:"Tìm kiếm",value:o,onChange:h=>r(h.target.value),className:"w-full"})}),!a&&c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx(H,{type:"button",variant:"outline",className:"flex-1 justify-start",children:"Danh sách món không đi kèm vé buffet"}),c.jsx(H,{type:"button",variant:"link",className:"flex-1 justify-start text-blue-600",onClick:()=>{},children:"Danh sách vé buffet được upsize"})]}),c.jsxs("div",{className:"rounded-lg bg-green-50 p-3",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(O,{id:"select-all",checked:A,...k&&{"data-indeterminate":"true"},onCheckedChange:U,className:"data-[state=checked]:border-green-600 data-[state=checked]:bg-green-600"}),c.jsxs("label",{htmlFor:"select-all",className:"cursor-pointer text-sm font-medium text-green-700",children:["Đã chọn ",E]}),c.jsx(H,{variant:"ghost",size:"sm",className:"ml-auto h-6 px-2 text-xs",onClick:()=>l(!y),children:y?c.jsx(J,{className:"h-3 w-3"}):c.jsx(X,{className:"h-3 w-3"})})]}),!y&&S.length>0&&c.jsx("div",{className:"mt-3 space-y-2",children:S.map(h=>c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(O,{id:`selected-${h.item_id}`,checked:d.includes(h.item_id),onCheckedChange:()=>q(h.item_id)}),c.jsx("label",{htmlFor:`selected-${h.item_id}`,className:"flex-1 cursor-pointer text-sm",children:h.item_name})]},h.item_id))})]}),c.jsxs("div",{className:"rounded-lg bg-gray-50 p-3",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsxs("div",{className:"text-sm font-medium text-gray-700",children:["Còn lại ",P.length]}),c.jsx(H,{variant:"ghost",size:"sm",className:"ml-auto h-6 px-2 text-xs",onClick:()=>I(!p),children:p?c.jsx(J,{className:"h-3 w-3"}):c.jsx(X,{className:"h-3 w-3"})})]}),!p&&c.jsx("div",{className:"mt-3 max-h-60 space-y-2 overflow-y-auto",children:P.map(h=>c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(O,{id:h.item_id,checked:d.includes(h.item_id),onCheckedChange:()=>q(h.item_id)}),c.jsx("label",{htmlFor:h.item_id,className:"flex-1 cursor-pointer text-sm",children:h.item_name})]},h.item_id))})]})]})]})})}const Ut=V.object({customization_uid:V.string().nullable()});function Te({item:t,customizations:n,open:e,onOpenChange:i}){const{updateItemAsync:s}=$t(),{company:a}=L(r=>r.auth),{selectedBrand:_}=it(),g=dt({resolver:yt(Ut),defaultValues:{customization_uid:"none"}});v.useEffect(()=>{if(e)try{g.reset({customization_uid:(t==null?void 0:t.customization_uid)??null})}catch{F.error("Lỗi khi load customization data")}},[e,g,t]);const o=async r=>{try{if(!(t!=null&&t.id)||!(a!=null&&a.id)||!(_!=null&&_.id))throw new Error("Required data is missing");const d=r.customization_uid==="none"?null:r.customization_uid;await s({...t,customization_uid:d}),i(!1)}catch{F.error("Lỗi khi cập nhật customization")}};return t?c.jsx(ft,{open:e,onOpenChange:r=>{i(r),g.reset()},children:c.jsxs(bt,{className:"top-[20%] w-full max-w-4xl translate-y-[-50%]",children:[c.jsx(It,{children:c.jsx(Ct,{className:"text-center",children:"Cấu hình customization"})}),c.jsx(ut,{...g,children:c.jsxs("form",{onSubmit:g.handleSubmit(o),className:"space-y-4",children:[c.jsx(mt,{control:g.control,name:"customization_uid",render:({field:r})=>c.jsxs(ht,{children:[c.jsx(_t,{children:"Customization áp dụng cho món"}),c.jsx(gt,{children:c.jsx(xt,{value:r.value??"",onValueChange:d=>r.onChange(d===""?null:d),options:n.map(d=>({value:d.id,label:d.name})),placeholder:"Chọn customization...",searchPlaceholder:"Tìm kiếm customization...",emptyText:"Không có dữ liệu",className:"w-full"})}),c.jsx(pt,{})]})}),c.jsxs(Ft,{children:[c.jsx(wt,{asChild:!0,children:c.jsx(H,{variant:"outline",type:"button",children:"Hủy"})}),c.jsx(H,{type:"submit",disabled:g.formState.isSubmitting,children:g.formState.isSubmitting?"Đang lưu...":"Lưu"})]})]})})]})}):null}export{ve as B,Te as C,he as I,Ie as a,Fe as b,xe as c,we as d,be as e,Ce as f,ge as g,$t as h,ye as i,pe as j,fe as k,_e as u};
