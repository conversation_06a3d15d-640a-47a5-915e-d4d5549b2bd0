import{r as v,j as c,R as X,u as A,a3 as H,a4 as x,B as $,l as nt,z as B}from"./index-Bnt3OGV2.js";import{u as it}from"./use-dialog-state-jYRzWcqs.js";import{u as Y}from"./useQuery-DSrD7NAp.js";import{Q as T}from"./query-keys-3lmd-xp6.js";import{b as F}from"./pos-api-BwpRFGce.js";import"./vietqr-api-DAENYiJ_.js";import{u as st}from"./use-item-types-Q-0SzpLA.js";import{u as at}from"./use-item-classes-CsFb8pem.js";import{u as ot}from"./use-units-DeWvOXUG.js";import"./user-oq7iQk7S.js";import"./crm-api-Dd9UhSCJ.js";import{C as W}from"./checkbox-BiVztVsP.js";import{I as rt}from"./input-CiKEYbig.js";import{P as ct}from"./modal-B0J8RkN-.js";import"./date-range-picker-CVvofQC0.js";import{u as dt,F as lt,a as ut,b as mt,c as ht,d as _t,e as gt}from"./form-wT1R35uI.js";import{C as V}from"./chevron-right-sZt3EK3r.js";import{C as J}from"./select-Czd7KcZQ.js";import{s as yt}from"./zod-CNC8A7Cl.js";import{D as pt,a as ft,b as It,c as bt,e as Ct,f as xt}from"./dialog-hQ-PVOWr.js";import{C as wt}from"./combobox-Dfida1wD.js";import{E as Ft}from"./exceljs.min-Da2DFgpf.js";import{u as P}from"./useMutation-d67-fNFq.js";const Z=X.createContext(null);function de({children:t}){const[n,e]=it(null),[i,o]=v.useState(null);return c.jsx(Z,{value:{open:n,setOpen:e,currentRow:i,setCurrentRow:o},children:t})}const le=()=>{const t=X.useContext(Z);if(!t)throw new Error("useItemsInCity has to be used within <ItemsInCityContext>");return t},tt=()=>{const t=new Ft.Workbook;return t.creator="POS System",t.lastModifiedBy="POS System",t.created=new Date,t.modified=new Date,t},vt=()=>["ID","Mã món","Thành phố","Tên","Giá","Trạng thái","Mã barcode","Món ăn kèm","Không cập nhật số lượng món ăn kèm","Đơn vị","Nhóm","Tên nhóm","Loại món","Tên loại","Mô tả","SKU","VAT (%)","Thời gian chế biến (phút)","Cho phép sửa giá khi bán","Cấu hình món ảo","Cấu hình món dịch vụ","Cấu hình món ăn là vé buffet","Giờ","Ngày","Thứ tự","Hình ảnh","Công thức inQR cho máy pha trà"],et=t=>{const n=vt(),e=t.addWorksheet("Menu");return e.addRow(n).eachCell(a=>{a.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},a.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},a.alignment={horizontal:"center",vertical:"middle"},a.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),[36,15,12,25,12,12,15,15,35,12,15,12,15,12,25,15,12,25,25,20,25,35,12,12,12,25,35].forEach((a,_)=>{e.getColumn(_+1).width=a}),e},Tt=()=>["ID","Mã món","Thành phố","Tên","Giá (Mặc định 0)","Trạng thái (Mặc định 1)","Mã barcode (Tối đa 13)","Món ăn kèm (Mặc định 0)","Không cập nhật số lượng món ăn kèm (Mặc định 1)","Đơn vị (Mặc định MON)","Nhóm (Mặc định LOẠI KHÁC)","Tên nhóm","Loại món (Mặc định rỗng)","Tên loại","Mô tả","SKU (Tối đa 50)","VAT (%) (Mặc định 0)","Thời gian chế biến (phút) (Mặc định 0)","Cho phép sửa giá khi bán (Mặc định 0)","Cấu hình món ảo (Mặc định 0)","Cấu hình món dịch vụ (Mặc định 0)","Cấu hình món ăn là vé buffet (Mặc định 0)","Giờ (Mặc định 0)","Ngày (Mặc định 0)","Thứ tự (Mặc định 0)","Hình ảnh","Công thức inQR cho máy pha trà"],St=()=>[["d9692391-3f3f-4754-9416-878d2d8b52ce","ITEM-3279","Hồ Chí Minh","Cà Phê Sữa (L)",0,1,"",0,1,"AM","ITEM_TYPE_OTHER","Uncategory","DA","Đồ ăn","không","",0,10,14,0,0,1,2064384,224,0,"https://image.foodbook.vn/images/20250829/1756431019051-anh-test-may-in-mau-chat-luong-hinh-2.jpg",""],["8119567b-f80e-43ac-8c85-012dd8f56b18","KHOAI","Hồ Chí Minh","pomato",0,1,"",1,1,"TRAI","MAK","MÓN ĂN KÈM","MA","manh","","",0,5,14,1,0,1,6355002,254,0,"https://image.foodbook.vn/images/20250829/1756450169874-vo_tri_2.jpg",""]],kt=()=>[["Chủ nhật","2"],["Thứ 2","4"],["Thứ 3","8"],["Thứ 4","16"],["Thứ 5","32"],["Thứ 6","64"],["Thứ 7","128"],["Ví dụ: CN, T2, T5 = 2 + 4 + 32","38"]],Mt=()=>[["0h","1"],["1h","2"],["2h","4"],["3h","8"],["4h","16"],["5h","32"],["6h","64"],["7h","128"],["8h","256"],["9h","512"],["10h","1024"],["11h","2048"],["12h","4096"],["13h","8192"],["14h","16384"],["15h","32768"],["16h","65536"],["17h","131072"],["18h","262144"],["19h","524288"],["20h","1048576"],["21h","2097152"],["22h","4194304"],["23h","8388608"],["Ví dụ: 0h, 1h, 3h = 1 + 2 + 8","11"]],Nt=t=>{const n=t.filter(e=>e.active===1).map(e=>[e.item_type_id,e.item_type_name]);return n.length>0?n:[["Không có dữ liệu",""]]},jt=t=>{const n=t.filter(e=>e.active===1).map(e=>[e.item_class_id,e.item_class_name]);return n.length>0?n:[["Không có dữ liệu",""]]},Et=t=>{if(!t||t.length===0)return[["Không có dữ liệu",""]];const n=t.map(e=>[e.unit_id,e.unit_name]);return n.length>0?n:[["Không có dữ liệu",""]]},Rt=(t,n)=>{var C,k,D,q,N,j,R,M,U,Q;const e=Tt(),i=t.addWorksheet("Template"),o=i.addRow(['Đây là sheet mẫu để tham khảo. Vui lòng quay lại sheet "Menu" để nhập dữ liệu.']);i.mergeCells(`A${o.number}:AA${o.number}`),o.getCell(1).fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},o.getCell(1).font={color:{argb:"FFFFFFFF"},bold:!0,size:11},o.getCell(1).alignment={horizontal:"left",vertical:"middle"},o.getCell(1).border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}},i.addRow(e).eachCell(m=>{m.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},m.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},m.alignment={horizontal:"center",vertical:"middle"},m.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),St().forEach(m=>{i.addRow(m).eachCell(f=>{f.font={size:10},f.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}}})}),i.addRow([]);const p=i.addRow(["BẢNG THAM CHIẾU NGÀY","","","BẢNG THAM CHIẾU GIỜ","","","BẢNG THAM CHIẾU NHÓM MÓN","","","BẢNG THAM CHIẾU LOẠI MÓN","","","BẢNG THAM CHIẾU ĐƠN VỊ",""]),s=p.number;i.mergeCells(`A${s}:B${s}`),i.mergeCells(`D${s}:E${s}`),i.mergeCells(`G${s}:H${s}`),i.mergeCells(`J${s}:K${s}`),i.mergeCells(`M${s}:N${s}`),p.eachCell(m=>{m.value&&m.value.toString().trim()!==""&&(m.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},m.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},m.alignment={horizontal:"center",vertical:"middle"},m.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}})}),i.addRow(["Thời gian","Giá trị","","Thời gian","Giá trị","","Mã nhóm","Tên nhóm","","Mã loại món","Tên loại món","","Mã đơn vị","Tên đơn vị"]).eachCell(m=>{m.value&&m.value.toString().trim()!==""&&(m.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF79ABE3"}},m.font={bold:!0,size:10},m.alignment={horizontal:"center",vertical:"middle"},m.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}})});const l=kt(),u=Mt(),y=n!=null&&n.itemTypes?Nt(n.itemTypes):[["Không có dữ liệu",""]],d=n!=null&&n.itemClasses?jt(n.itemClasses):[["Không có dữ liệu",""]],g=n!=null&&n.units?Et(n.units):[["Không có dữ liệu",""]],b=Math.max(l.length,u.length,y.length,d.length,g.length),I=[];for(let m=0;m<b;m++){const h=[((C=l[m])==null?void 0:C[0])||"",((k=l[m])==null?void 0:k[1])||"","",((D=u[m])==null?void 0:D[0])||"",((q=u[m])==null?void 0:q[1])||"","",((N=y[m])==null?void 0:N[0])||"",((j=y[m])==null?void 0:j[1])||"","",((R=d[m])==null?void 0:R[0])||"",((M=d[m])==null?void 0:M[1])||"","",((U=g[m])==null?void 0:U[0])||"",((Q=g[m])==null?void 0:Q[1])||""];I.push(h)}return I.forEach(m=>{i.addRow(m).eachCell(f=>{f.value&&f.value.toString().trim()!==""&&(f.font={size:10},f.alignment={horizontal:"left",vertical:"middle"},f.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}})})}),[36,15,12,25,12,12,15,15,35,12,15,12,15,12,25,15,12,25,25,20,25,35,12,12,12,25,35].forEach((m,h)=>{i.getColumn(h+1).width=m}),i},ue=async(t,n)=>{try{const e=tt(),i=et(e);if(n&&n.length>0){const l=d=>{var b;const g=(b=t==null?void 0:t.itemTypes)==null?void 0:b.find(I=>I.id===d);return{name:(g==null?void 0:g.item_type_name)||d,id:(g==null?void 0:g.item_type_id)||d}},u=d=>{var b;const g=(b=t==null?void 0:t.itemClasses)==null?void 0:b.find(I=>I.id===d);return{name:(g==null?void 0:g.item_class_name)||d,id:(g==null?void 0:g.item_class_id)||d}},y=d=>{var b;const g=(b=t==null?void 0:t.units)==null?void 0:b.find(I=>I.id===d);return{name:(g==null?void 0:g.unit_name)||d,id:(g==null?void 0:g.unit_id)||d}};n.forEach(d=>{var C;const b=((C=(d.cities||[])[0])==null?void 0:C.city_name)||"",I=d.extra_data||{},S=[d.id||"",d.item_id||"",b,d.item_name||"",d.ta_price||0,d.active||1,d.item_id_barcode||"",d.is_eat_with||0,I.no_update_quantity_toping||1,y(d.unit_uid).id,l(d.item_type_uid).id,l(d.item_type_uid).name,u(d.item_class_uid).id,u(d.item_class_uid).name,d.description||"",d.item_id_mapping||"",d.ta_tax*100||0,Math.round(d.time_cooking/6e4)||0,I.enable_edit_price||0,I.is_virtual_item||0,I.is_item_service||0,I.is_buffet_item||0,d.time_sale_hour_day||0,d.time_sale_date_week||0,d.sort,d.image_path||"",I.formula_qrcode||""];i.addRow(S)})}Rt(e,t);const o=await e.xlsx.writeBuffer(),a=new Blob([o],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),p=`items_import_template_${new Date().toISOString().slice(0,19).replace(/:/g,"-")}.xlsx`,s=window.URL.createObjectURL(a),r=document.createElement("a");return r.href=s,r.download=p,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(s),Promise.resolve()}catch(e){return console.error("Error creating Excel file:",e),Promise.reject(e)}},At=async(t,n)=>{try{const e=tt(),i=et(e),o=s=>{var l;const r=(l=n==null?void 0:n.itemTypes)==null?void 0:l.find(u=>u.id===s);return{name:(r==null?void 0:r.item_type_name)||s,id:(r==null?void 0:r.item_type_id)||s}},a=s=>{var l;const r=(l=n==null?void 0:n.itemClasses)==null?void 0:l.find(u=>u.id===s);return{name:(r==null?void 0:r.item_class_name)||s,id:(r==null?void 0:r.item_class_id)||s}},_=s=>{var l;const r=(l=n==null?void 0:n.units)==null?void 0:l.find(u=>u.id===s);return{name:(r==null?void 0:r.unit_name)||s,id:(r==null?void 0:r.unit_id)||s}};t.forEach(s=>{var d;const l=((d=(s.cities||[])[0])==null?void 0:d.city_name)||"",u=s.extra_data||{},y=[s.id||"",s.item_id||"",l,s.item_name||"",s.ta_price||0,s.active||1,s.item_id_barcode||"",s.is_eat_with||0,u.no_update_quantity_toping||1,_(s.unit_uid).id,o(s.item_type_uid).id,o(s.item_type_uid).name,a(s.item_class_uid).id,a(s.item_class_uid).name,s.description||"",s.item_id_mapping||"",s.ta_tax*100||0,Math.round(s.time_cooking/6e4)||0,u.enable_edit_price||0,u.is_virtual_item||0,u.is_item_service||0,u.is_buffet_item||0,s.time_sale_hour_day||0,s.time_sale_date_week||0,s.sort,s.image_path||"",u.formula_qrcode||""];i.addRow(y)});const p=await e.xlsx.writeBuffer();return new Blob([p],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"})}catch(e){throw console.error("Error creating Excel blob:",e),e}},Pt=()=>{try{const t=localStorage.getItem("pos_cities_data");if(t)return JSON.parse(t).map(e=>e.id)}catch{}return[]},G=t=>{if(!t)return null;if(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(t))return t;try{const e=localStorage.getItem("pos_cities_data");if(e){const o=JSON.parse(e).find(a=>a.city_name===t);return(o==null?void 0:o.id)||null}}catch{return null}return null},E=new Map,K=new Map,Dt=10*60*1e3;function z(t){return typeof t=="object"&&t!==null&&"response"in t}const w={getItemsInCity:async t=>{const n=t.city_uid&&t.city_uid!=="all"?G(t.city_uid):t.city_uid,e=t.active!==void 0?t.active.toString():"undefined",i=t.skip_limit?"true":"false",o=`${t.company_uid}-${t.brand_uid}-${t.page||1}-${n||"all"}-${t.list_city_uid||"all"}-${t.item_type_uid||"all"}-${t.time_sale_date_week||""}-${e}-${t.reverse||0}-${t.search||""}-${t.limit||50}-${i}`,a=E.get(o);if(a&&Date.now()-a.timestamp<Dt)return a.data;const _=K.get(o);if(_)return _;const p=(async()=>{try{const s=new URLSearchParams;if(s.append("company_uid",t.company_uid),s.append("brand_uid",t.brand_uid),t.page&&s.append("page",t.page.toString()),t.item_type_uid&&s.append("item_type_uid",t.item_type_uid),t.list_city_uid)s.append("list_city_uid",t.list_city_uid);else if(t.city_uid&&t.city_uid!=="all"){const u=G(t.city_uid);u&&s.append("city_uid",u)}else{const u=Pt();u.length>0&&s.append("list_city_uid",u.join(","))}t.time_sale_date_week&&s.append("time_sale_date_week",t.time_sale_date_week),t.reverse!==void 0&&s.append("reverse",t.reverse.toString()),t.search&&s.append("search",t.search),t.active!==void 0&&s.append("active",t.active.toString()),t.limit&&s.append("limit",t.limit.toString()),t.skip_limit&&s.append("skip_limit","true");const r=await F.get(`/mdata/v1/items?${s.toString()}`);if(!r.data||typeof r.data!="object")throw new Error("Invalid response format from items in city API");const l=r.data;return E.set(o,{data:l,timestamp:Date.now()}),l}finally{K.delete(o)}})();return K.set(o,p),p},deleteItemInCity:async t=>{var n;try{const e=new URLSearchParams;e.append("company_uid",t.company_uid),e.append("brand_uid",t.brand_uid),e.append("id",t.id),await F.delete(`/mdata/v1/item?${e.toString()}`),E.clear()}catch(e){throw z(e)&&((n=e.response)==null?void 0:n.status)===404?new Error("Item not found."):e}},deleteMultipleItemsInCity:async t=>{var n;try{const e=new URLSearchParams;e.append("company_uid",t.company_uid),e.append("brand_uid",t.brand_uid),e.append("list_item_uid",t.list_item_uid.join(",")),await F.delete(`/mdata/v1/items?${e.toString()}`),E.clear()}catch(e){throw z(e)&&((n=e.response)==null?void 0:n.status)===404?new Error("Items not found."):e}},downloadTemplate:async t=>{var a;const n=t.city_uid&&t.city_uid!=="all"?G(t.city_uid):null,e=new URLSearchParams({skip_limit:"true",company_uid:t.company_uid,brand_uid:t.brand_uid,...n&&{city_uid:n},...t.item_type_uid&&t.item_type_uid!=="all"&&{item_type_uid:t.item_type_uid},...t.active&&t.active!=="all"&&{active:t.active}}),i=await F.get(`/mdata/v1/items?${e}`),o=Array.isArray((a=i.data)==null?void 0:a.data)?i.data.data:[];return await At(o,t.referenceData)},fetchItemsData:async t=>{var o;const n=t.city_uid&&t.city_uid!=="all"?G(t.city_uid):null,e=new URLSearchParams({skip_limit:"true",company_uid:t.company_uid,brand_uid:t.brand_uid,...n&&{city_uid:n},...t.item_type_uid&&t.item_type_uid!=="all"&&{item_type_uid:t.item_type_uid},...t.active&&t.active!=="all"&&{active:t.active}}),i=await F.get(`/mdata/v1/items?${e}`);return Array.isArray((o=i.data)==null?void 0:o.data)?i.data.data:[]},createItemInCity:async t=>{var n,e;try{const i=await F.post("/mdata/v1/item",t);return E.clear(),i.data.data||i.data}catch(i){throw z(i)&&((n=i.response)==null?void 0:n.status)===400?new Error(((e=i.response.data)==null?void 0:e.message)||"Invalid data provided."):i}},updateItemInCity:async t=>{var n,e;try{const i=await F.put("/mdata/v1/item",t,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return E.clear(),i.data.data||i.data}catch(i){throw z(i)&&((n=i.response)==null?void 0:n.status)===400?new Error(((e=i.response.data)==null?void 0:e.message)||"Invalid data provided."):i}},getItemByListId:async t=>{var n,e;try{const i=new URLSearchParams({skip_limit:"true",company_uid:t.company_uid,brand_uid:t.brand_uid,is_all:"true",list_item_id:t.list_item_id}),o=await F.get(`/mdata/v1/items?${i}`,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4}),a=Array.isArray((n=o.data)==null?void 0:n.data)?o.data.data:[];if(!a.length)throw new Error("Item not found");return{data:a[0]}}catch(i){throw z(i)&&((e=i.response)==null?void 0:e.status)===404?new Error("Item not found."):i}},getItemById:async t=>{var n;try{const e=new URLSearchParams;e.append("id",t.id),t.company_uid&&e.append("company_uid",t.company_uid),t.brand_uid&&e.append("brand_uid",t.brand_uid);const i=await F.get(`/mdata/v1/item?${e.toString()}`);if(!i.data||typeof i.data!="object")throw new Error("Invalid response format from item detail API");return i.data}catch(e){throw z(e)&&((n=e.response)==null?void 0:n.status)===404?new Error("Item not found."):e}},importItems:async t=>(await F.post("/mdata/v1/items/import",{company_uid:t.company_uid,brand_uid:t.brand_uid,items:t.items})).data,updateItemStatus:async t=>{var n,e;try{const o={...(await w.getItemById({id:t.id})).data,active:t.active,company_uid:t.company_uid,brand_uid:t.brand_uid},a=await F.put("/mdata/v1/item",o,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return E.clear(),a.data.data||a.data}catch(i){throw z(i)&&((n=i.response)==null?void 0:n.status)===400?new Error(((e=i.response.data)==null?void 0:e.message)||"Invalid data provided."):i}},clearCache:()=>{E.clear(),K.clear()},getCacheStats:()=>({cacheSize:E.size,pendingRequests:K.size})},Lt=(t={})=>{const{params:n={},enabled:e=!0}=t,{company:i,brands:o}=A(d=>d.auth),a=o==null?void 0:o[0],_={company_uid:(i==null?void 0:i.id)||"",brand_uid:(a==null?void 0:a.id)||"",page:1,reverse:1,limit:50,...n},p=!!(i!=null&&i.id&&(a!=null&&a.id)),s=Y({queryKey:[T.ITEMS_IN_CITY_LIST,JSON.stringify(_)],queryFn:async()=>(await w.getItemsInCity(_)).data||[],enabled:e&&p,staleTime:5*60*1e3,refetchInterval:10*60*1e3}),r={..._,page:(_.page||1)+1},l=Y({queryKey:[T.ITEMS_IN_CITY_LIST,"next",JSON.stringify(r)],queryFn:async()=>(await w.getItemsInCity(r)).data||[],enabled:e&&p&&(s.data?s.data.length>0:!1),staleTime:2*60*1e3,gcTime:5*60*1e3}),u=_.limit||50,y=(l.data?l.data.length>0:!1)||(s.data?s.data.length===u:!1);return{data:s.data,isLoading:s.isLoading,error:s.error,refetch:s.refetch,isFetching:s.isFetching,nextPageData:l.data||[],hasNextPage:y}},me=(t,n=!0)=>Y({queryKey:[T.ITEMS_IN_CITY_DETAIL,t],queryFn:()=>w.getItemById({id:t}),enabled:n&&!!t,staleTime:5*60*1e3}),he=(t={})=>{var s,r,l;const n=Lt(t),e=(s=t.params)==null?void 0:s.city_uid,i=(r=t.params)==null?void 0:r.list_city_uid,{data:o=[]}=st({skip_limit:!0,...e&&e!=="all"?{city_uid:e}:{},...i?{list_city_uid:i}:{}}),{data:a=[]}=at({skip_limit:!0}),{data:_=[]}=ot();return{data:((l=n.data)==null?void 0:l.map(u=>{var j,R;const y=u,g=((j=(y.cities||[])[0])==null?void 0:j.city_name)||"",b=y.item_type_uid?o.find(M=>M.id===y.item_type_uid):null,I=(b==null?void 0:b.item_type_name)||"",S=y.item_class_uid?a.find(M=>M.id===y.item_class_uid):null,C=(S==null?void 0:S.item_class_name)||"",k=y.unit_uid?_.find(M=>M.id===y.unit_uid):null,D=(k==null?void 0:k.unit_name)||"",N=y.is_eat_with===1||y.item_id_eat_with&&y.item_id_eat_with!==""?y.item_id_eat_with||"Món ăn kèm":"";return{...u,code:y.item_id||"",name:u.item_name,price:u.ots_price,vatPercent:u.ots_tax,cookingTime:u.time_cooking,categoryGroup:I,itemType:I,itemClass:C,unit:D,sideItems:N||void 0,city:g,buffetConfig:((R=y.extra_data)==null?void 0:R.is_buffet_item)===1?"Đã cấu hình":"Chưa cấu hình",customization:y.customization_uid||void 0,isActive:!!u.active,createdAt:typeof u.created_at=="number"?new Date(u.created_at*1e3):new Date(new Date(u.created_at).getTime())}}))||[],isLoading:n.isLoading,error:n.error,refetch:n.refetch,isFetching:n.isFetching,nextPageData:n.nextPageData,hasNextPage:n.hasNextPage}},_e=()=>{const t=H(),n=P({mutationFn:e=>w.createItemInCity(e),onSuccess:()=>{w.clearCache(),t.invalidateQueries({queryKey:[T.ITEMS_IN_CITY_LIST]}),x.success("Tạo món thành công!")},onError:e=>{x.error(e.message||"Có lỗi xảy ra khi tạo món")}});return{createItemAsync:n.mutateAsync,isPending:n.isPending}},zt=()=>{const t=H(),n=P({mutationFn:e=>w.updateItemInCity(e),onSuccess:()=>{w.clearCache(),t.invalidateQueries({queryKey:[T.ITEMS_IN_CITY_LIST]}),t.invalidateQueries({queryKey:[T.ITEMS_IN_CITY_DETAIL]}),x.success("Cập nhật món thành công!")},onError:e=>{x.error(e.message||"Có lỗi xảy ra khi cập nhật món")}});return{updateItemAsync:n.mutateAsync,isPending:n.isPending}},ge=()=>{const t=H(),{company:n,brands:e}=A(a=>a.auth),i=e==null?void 0:e[0],o=P({mutationFn:a=>{const _={company_uid:(n==null?void 0:n.id)||"",brand_uid:(i==null?void 0:i.id)||"",id:a};return w.deleteItemInCity(_)},onSuccess:()=>{t.invalidateQueries({queryKey:[T.ITEMS_IN_CITY_LIST]}),x.success("Xóa món thành công!")},onError:a=>{x.error(a.message||"Có lỗi xảy ra khi xóa món")}});return{deleteItemAsync:o.mutateAsync,isPending:o.isPending}},ye=()=>{const t=H(),{company:n,brands:e}=A(a=>a.auth),i=e==null?void 0:e[0],o=P({mutationFn:a=>{const _={company_uid:(n==null?void 0:n.id)||"",brand_uid:(i==null?void 0:i.id)||"",list_item_uid:a};return w.deleteMultipleItemsInCity(_)},onSuccess:()=>{t.invalidateQueries({queryKey:[T.ITEMS_IN_CITY_LIST]}),x.success("Xóa món ăn thành công")},onError:a=>{x.error((a==null?void 0:a.message)||"Có lỗi xảy ra khi xóa món ăn")}});return{deleteMultipleItemsAsync:o.mutateAsync,isPending:o.isPending}},pe=()=>{const t=H(),{company:n,brands:e}=A(a=>a.auth),i=e==null?void 0:e[0],o=P({mutationFn:a=>{const _={id:a.id,active:a.active,company_uid:(n==null?void 0:n.id)||"",brand_uid:(i==null?void 0:i.id)||""};return w.updateItemStatus(_)},onSuccess:()=>{w.clearCache(),t.invalidateQueries({queryKey:[T.ITEMS_IN_CITY_LIST]}),t.invalidateQueries({queryKey:[T.ITEMS_IN_CITY_DETAIL]}),x.success("Cập nhật trạng thái thành công!")},onError:a=>{x.error(a.message||"Có lỗi xảy ra khi cập nhật trạng thái")}});return{updateStatusAsync:o.mutateAsync,isPending:o.isPending}},fe=()=>{const{company:t,brands:n}=A(o=>o.auth),e=n==null?void 0:n[0],i=P({mutationFn:o=>{const a={company_uid:(t==null?void 0:t.id)||"",brand_uid:(e==null?void 0:e.id)||"",...o};return w.downloadTemplate(a)},onSuccess:o=>{const a=window.URL.createObjectURL(o),_=document.createElement("a");_.href=a,_.download=`items-template-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(_),_.click(),document.body.removeChild(_),window.URL.revokeObjectURL(a),x.success("Tải template thành công!")},onError:o=>{x.error(o.message||"Có lỗi xảy ra khi tải template")}});return{downloadTemplateAsync:i.mutateAsync,isPending:i.isPending}},Ie=()=>{const{company:t,brands:n}=A(o=>o.auth),e=n==null?void 0:n[0],i=P({mutationFn:o=>{const a={company_uid:(t==null?void 0:t.id)||"",brand_uid:(e==null?void 0:e.id)||"",...o};return w.fetchItemsData(a)},onError:o=>{x.error(o.message||"Có lỗi xảy ra khi tải dữ liệu")}});return{fetchItemsDataAsync:i.mutateAsync,isPending:i.isPending}},be=()=>{const t=H(),{company:n,brands:e}=A(a=>a.auth),i=e==null?void 0:e[0],o=P({mutationFn:a=>{const _={company_uid:(n==null?void 0:n.id)||"",brand_uid:(i==null?void 0:i.id)||"",items:a};return w.importItems(_)},onSuccess:()=>{t.invalidateQueries({queryKey:[T.ITEMS_IN_CITY_LIST]}),x.success("Import món thành công!")},onError:a=>{x.error(a.message||"Có lỗi xảy ra khi import món")}});return{importItemsAsync:o.mutateAsync,isPending:o.isPending}};function Ce({itemsBuffet:t,open:n,onOpenChange:e,onItemsChange:i,items:o,hide:a=!0,enable:_=!0,onEnableChange:p}){const[s,r]=v.useState(""),[l,u]=v.useState([]),[y,d]=v.useState(!1),[g,b]=v.useState(!1),[I,S]=v.useState(!1);v.useEffect(()=>{n&&(u(Array.isArray(t)?t:[]),S(_))},[t,n]);const C=v.useMemo(()=>s?o.filter(h=>{var f;return(f=h.item_name)==null?void 0:f.toLowerCase().includes(s.toLowerCase())}):o,[o,s]),k=v.useMemo(()=>C.length?C.filter(h=>l.includes(h.item_id||"")):[],[C,l]),D=v.useMemo(()=>C.length?C.filter(h=>!l.includes(h.item_id||"")):[],[C,l]),q=h=>{u(f=>f.includes(h)?f.filter(L=>L!==h):[...f,h])},N=k.length,j=C.length,R=j>0&&N===j,M=N>0&&N<j,U=()=>{if(R){const h=C.map(f=>f.item_id);u(f=>f.filter(L=>!h.includes(L)))}else{const h=C.map(f=>f.item_id);u(f=>{const L=[...f];return h.forEach(O=>{L.includes(O||"")||L.push(O||"")}),L})}},Q=()=>{i(l),e(!1)},m=()=>{u([]),e(!1)};return c.jsx(ct,{title:"Chọn danh sách món không đi kèm vé buffet",centerTitle:!0,open:n,onOpenChange:e,onCancel:m,onConfirm:Q,confirmText:"Lưu",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",children:c.jsxs("div",{className:"space-y-4",children:[!a&&c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx(W,{id:"enable-buffet",checked:I,onCheckedChange:h=>{const f=!!h;S(f),p==null||p(f)}}),c.jsx("label",{htmlFor:"enable-buffet",className:"cursor-pointer text-blue-600",onClick:()=>S(h=>!h),children:"Cấu hình món ăn là vé buffet"})]}),(a||I)&&c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"flex items-center gap-2",children:c.jsx(rt,{placeholder:"Tìm kiếm",value:s,onChange:h=>r(h.target.value),className:"w-full"})}),!a&&c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx($,{type:"button",variant:"outline",className:"flex-1 justify-start",children:"Danh sách món không đi kèm vé buffet"}),c.jsx($,{type:"button",variant:"link",className:"flex-1 justify-start text-blue-600",onClick:()=>{},children:"Danh sách vé buffet được upsize"})]}),c.jsxs("div",{className:"rounded-lg bg-green-50 p-3",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(W,{id:"select-all",checked:R,...M&&{"data-indeterminate":"true"},onCheckedChange:U,className:"data-[state=checked]:border-green-600 data-[state=checked]:bg-green-600"}),c.jsxs("label",{htmlFor:"select-all",className:"cursor-pointer text-sm font-medium text-green-700",children:["Đã chọn ",N]}),c.jsx($,{variant:"ghost",size:"sm",className:"ml-auto h-6 px-2 text-xs",onClick:()=>d(!y),children:y?c.jsx(V,{className:"h-3 w-3"}):c.jsx(J,{className:"h-3 w-3"})})]}),!y&&k.length>0&&c.jsx("div",{className:"mt-3 space-y-2",children:k.map(h=>c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(W,{id:`selected-${h.item_id}`,checked:l.includes(h.item_id),onCheckedChange:()=>q(h.item_id)}),c.jsx("label",{htmlFor:`selected-${h.item_id}`,className:"flex-1 cursor-pointer text-sm",children:h.item_name})]},h.item_id))})]}),c.jsxs("div",{className:"rounded-lg bg-gray-50 p-3",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsxs("div",{className:"text-sm font-medium text-gray-700",children:["Còn lại ",D.length]}),c.jsx($,{variant:"ghost",size:"sm",className:"ml-auto h-6 px-2 text-xs",onClick:()=>b(!g),children:g?c.jsx(V,{className:"h-3 w-3"}):c.jsx(J,{className:"h-3 w-3"})})]}),!g&&c.jsx("div",{className:"mt-3 max-h-60 space-y-2 overflow-y-auto",children:D.map(h=>c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(W,{id:h.item_id,checked:l.includes(h.item_id),onCheckedChange:()=>q(h.item_id)}),c.jsx("label",{htmlFor:h.item_id,className:"flex-1 cursor-pointer text-sm",children:h.item_name})]},h.item_id))})]})]})]})})}const qt=B.object({customization_uid:B.string().nullable()});function xe({item:t,customizations:n,open:e,onOpenChange:i}){const{updateItemAsync:o}=zt(),{company:a}=A(r=>r.auth),{selectedBrand:_}=nt(),p=dt({resolver:yt(qt),defaultValues:{customization_uid:"none"}});v.useEffect(()=>{if(e)try{p.reset({customization_uid:(t==null?void 0:t.customization_uid)??null})}catch{x.error("Lỗi khi load customization data")}},[e,p,t]);const s=async r=>{try{if(!(t!=null&&t.id)||!(a!=null&&a.id)||!(_!=null&&_.id))throw new Error("Required data is missing");const l=r.customization_uid==="none"?null:r.customization_uid;await o({...t,customization_uid:l}),i(!1)}catch{x.error("Lỗi khi cập nhật customization")}};return t?c.jsx(pt,{open:e,onOpenChange:r=>{i(r),p.reset()},children:c.jsxs(ft,{className:"top-[20%] w-full max-w-4xl translate-y-[-50%]",children:[c.jsx(It,{children:c.jsx(bt,{className:"text-center",children:"Cấu hình customization"})}),c.jsx(lt,{...p,children:c.jsxs("form",{onSubmit:p.handleSubmit(s),className:"space-y-4",children:[c.jsx(ut,{control:p.control,name:"customization_uid",render:({field:r})=>c.jsxs(mt,{children:[c.jsx(ht,{children:"Customization áp dụng cho món"}),c.jsx(_t,{children:c.jsx(wt,{value:r.value??"",onValueChange:l=>r.onChange(l===""?null:l),options:n.map(l=>({value:l.id,label:l.name})),placeholder:"Chọn customization...",searchPlaceholder:"Tìm kiếm customization...",emptyText:"Không có dữ liệu",className:"w-full"})}),c.jsx(gt,{})]})}),c.jsxs(Ct,{children:[c.jsx(xt,{asChild:!0,children:c.jsx($,{variant:"outline",type:"button",children:"Hủy"})}),c.jsx($,{type:"submit",disabled:p.formState.isSubmitting,children:p.formState.isSubmitting?"Đang lưu...":"Lưu"})]})]})})]})}):null}export{Ce as B,xe as C,de as I,ye as a,fe as b,be as c,Ie as d,ge as e,pe as f,ue as g,zt as h,he as i,me as j,_e as k,le as u};
