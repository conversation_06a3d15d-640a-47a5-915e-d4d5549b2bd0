import{j as e,c as n,B as c}from"./index-Bnt3OGV2.js";import{A as o,a as i,C as d,E as l}from"./react-icons.esm-B7rNr9e-.js";import{D as x,a as m,b as g,c as r,e as h}from"./dropdown-menu-BjJ0HZAV.js";function u({column:s,title:a,className:t}){return s.getCanSort()?e.jsx("div",{className:n("flex items-center space-x-2",t),children:e.jsxs(x,{children:[e.jsx(m,{asChild:!0,children:e.jsxs(c,{variant:"ghost",size:"sm",className:"data-[state=open]:bg-accent -ml-3 h-8",children:[e.jsx("span",{children:a}),s.getIsSorted()==="desc"?e.jsx(o,{className:"ml-2 h-4 w-4"}):s.getIsSorted()==="asc"?e.jsx(i,{className:"ml-2 h-4 w-4"}):e.jsx(d,{className:"ml-2 h-4 w-4"})]})}),e.jsxs(g,{align:"start",children:[e.jsxs(r,{onClick:()=>s.toggleSorting(!1),children:[e.jsx(i,{className:"text-muted-foreground/70 mr-2 h-3.5 w-3.5"}),"Asc"]}),e.jsxs(r,{onClick:()=>s.toggleSorting(!0),children:[e.jsx(o,{className:"text-muted-foreground/70 mr-2 h-3.5 w-3.5"}),"Desc"]}),s.getCanHide()&&e.jsxs(e.Fragment,{children:[e.jsx(h,{}),e.jsxs(r,{onClick:()=>s.toggleVisibility(!1),children:[e.jsx(l,{className:"text-muted-foreground/70 mr-2 h-3.5 w-3.5"}),"Hide"]})]})]})]})}):e.jsx("div",{className:n(t),children:a})}export{u as D};
