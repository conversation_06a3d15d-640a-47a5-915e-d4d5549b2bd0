import{j as i,r}from"./index-C21OP4ex.js";const s=r.createContext(void 0);function c({children:o,value:t}){return i.jsx(s.Provider,{value:t,children:o})}function e(){const o=r.useContext(s);if(o===void 0)throw new Error("useMembershipDiscountFormContext must be used within a MembershipDiscountFormProvider");return o}function m(){const{formData:o,updateFormData:t}=e();return{formData:o,updateFormData:t}}function a(){const{handleBack:o,handleSave:t}=e();return{handleBack:o,handleSave:t}}function p(){const{isFormValid:o,isLoading:t,isEditMode:n}=e();return{isFormValid:o,isLoading:t,isEditMode:n}}function d(){const{promotions:o,isLoadingPromotions:t}=e();return{promotions:o,isLoadingPromotions:t}}export{c as M,p as a,m as b,d as c,a as u};
