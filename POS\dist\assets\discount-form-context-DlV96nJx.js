import{u as v}from"./useQuery-DSrD7NAp.js";import{ay as w,a3 as l,b as y,l as g,a4 as f,j as b,r as T}from"./index-Bnt3OGV2.js";import{u as _}from"./useMutation-d67-fNFq.js";import{a as u,b as F}from"./pos-api-BwpRFGce.js";import{j as d}from"./date-utils-DBbLjCz0.js";import{Q as c}from"./query-keys-3lmd-xp6.js";const q=async(t={})=>{var i;const e=new URLSearchParams;t.companyUid&&e.append("company_uid",t.companyUid),t.brandUid&&e.append("brand_uid",t.brandUid),t.page&&e.append("page",t.page.toString()),t.listStoreUid&&t.listStoreUid.length>0&&e.append("list_store_uid",t.listStoreUid.join(",")),t.promotionPartnerAutoGen!==void 0&&e.append("promotion_partner_auto_gen",t.promotionPartnerAutoGen.toString()),t.status&&e.append("status",t.status),t.active!==void 0&&e.append("active",t.active.toString()),t.searchTerm&&e.append("search",t.searchTerm);const n=await u.get(`/mdata/v1/discounts?${e.toString()}`);if((i=n.data)!=null&&i.data){const{convertApiDiscountToDiscount:o}=await w(async()=>{const{convertApiDiscountToDiscount:s}=await import("./discounts-DCn5I4UW.js");return{convertApiDiscountToDiscount:s}},[]);return n.data.data.map(o)}return[]},x=async t=>{var i;const e=new URLSearchParams;e.append("company_uid",t.companyUid),e.append("brand_uid",t.brandUid),e.append("id",t.id);const n=await u.get(`/mdata/v1/discount?${e.toString()}`);if((i=n.data)!=null&&i.data)return n.data.data;throw new Error("Discount not found")},P=async t=>{const e=new URLSearchParams;e.append("company_uid",t.companyUid),e.append("brand_uid",t.brandUid),e.append("id",t.id),await u.delete(`/mdata/v1/discount?${e.toString()}`)},E=(t,e,n,i)=>{const o=d.convertDaysToBitFlags(t.selectedDays),s=d.convertHoursToBitFlags(t.selectedHours);console.log("Transform - formData.filterState:",t.filterState);const{is_all:r,is_item:a,is_type:p,type_id:m,item_id:h,is_combo:D,combo_id:C}=t.filterState;return{store_uid:e,promotion_uid:t.promotionUid,discount_type:t.discountType,type_id:m,item_id:h,from_date:t.fromDate.getTime(),to_date:t.toDate.getTime(),sale_channel_uid:t.saleChannelUid,time_sale_date_week:o,time_sale_hour_day:s,company_uid:n,brand_uid:i,ta_discount:t.discountValue,ots_discount:t.discountValue,is_all:r,is_type:p,is_item:a,extra_data:{is_combo:D,combo_id:C},promotion_partner_auto_gen:1,is_update_same_discounts:!1}},k=async t=>{var e,n;try{return{success:!0,data:(await u.post("/mdata/v1/discount",t)).data}}catch(i){return{success:!1,message:((n=(e=i.response)==null?void 0:e.data)==null?void 0:n.message)||i.message||"Có lỗi xảy ra khi tạo discount"}}},I=(t,e)=>{const n=d.convertDaysToBitFlags(t.selectedDays),i=d.convertHoursToBitFlags(t.selectedHours),{is_all:o,is_item:s,is_type:r,type_id:a,item_id:p,is_combo:m,combo_id:h}=t.filterState;return{...e,ta_discount:t.discountValue,ots_discount:t.discountValue,is_all:o,is_type:r,is_item:s,type_id:a,item_id:p,discount_type:t.discountType,from_date:t.fromDate.getTime(),to_date:t.toDate.getTime(),time_sale_hour_day:i,time_sale_date_week:n,extra_data:{is_combo:m,combo_id:h},sale_channel_uid:e.source_uid,promotion_partner_auto_gen:1,is_update_same_discounts:!1}},V=async t=>{var e,n;try{return{success:!0,data:(await u.put("/mdata/v1/discount",t)).data}}catch(i){return{success:!1,message:((n=(e=i.response)==null?void 0:e.data)==null?void 0:n.message)||i.message||"Có lỗi xảy ra khi cập nhật discount"}}},O=async t=>{await u.put("/mdata/v1/discount",t)},R={validateFormData:t=>{const e=[];return(!t.discountValue||t.discountValue<=0)&&e.push("Giá trị giảm giá phải lớn hơn 0"),(!t.fromDate||!t.toDate)&&e.push("Vui lòng chọn ngày bắt đầu và kết thúc"),t.fromDate&&t.toDate&&t.fromDate>t.toDate&&e.push("Ngày bắt đầu không được lớn hơn ngày kết thúc"),t.selectedDays.length||e.push("Vui lòng chọn ít nhất một ngày trong tuần"),t.selectedHours.length||e.push("Vui lòng chọn ít nhất một giờ trong ngày"),t.filterState.is_all!==1&&(t.filterState.is_item===1&&t.filterState.item_id.trim()||t.filterState.is_type===1&&t.filterState.type_id.trim()||t.filterState.is_combo===1&&t.filterState.combo_id.trim()||e.push("Vui lòng chọn ít nhất một món, nhóm hoặc combo để áp dụng")),t.saleChannelUid||e.push("Thiếu thông tin kênh bán hàng"),t.promotionUid||e.push("Thiếu thông tin chương trình khuyến mãi"),e}};function L(t,e){const{company:n}=y(),{selectedBrand:i}=g(),o=l();return _({mutationFn:async s=>{if(!(n!=null&&n.id)||!(i!=null&&i.id))throw new Error("Thiếu thông tin company hoặc brand");const r=E(s,t,n.id,i.id),a=await k(r);if(!a.success)throw new Error(a.message||"Có lỗi xảy ra");return a.data},onSuccess:()=>{var s;f.success("Tạo discount thành công"),o.invalidateQueries({queryKey:[c.DISCOUNTS]}),(s=e==null?void 0:e.onSuccess)==null||s.call(e)},onError:s=>{const r=s.message||"Có lỗi xảy ra khi tạo discount";f.error(r)}})}function M(t){const e=l(),{onSuccess:n,onError:i}=t||{};return _({mutationFn:o=>V(o),onSuccess:()=>{e.invalidateQueries({queryKey:[c.DISCOUNTS]}),n==null||n()},onError:i})}function $(){return{validateFormData:R.validateFormData}}function G(t){const{company:e}=y(),{selectedBrand:n}=g();return v({queryKey:[c.DISCOUNTS,t],queryFn:async()=>{if(!(e!=null&&e.id)||!(n!=null&&n.id)||!t)throw new Error("Thiếu thông tin cần thiết");return await x({companyUid:e.id,brandUid:n.id,id:t})},enabled:!!(e!=null&&e.id&&(n!=null&&n.id)&&t),staleTime:5*60*1e3})}function W(){const{selectedBrand:t}=g(),{company:e}=y();return{createPromotion:async({storeUid:i,channelUid:o,channelName:s})=>{if(!(e!=null&&e.id)||!(t!=null&&t.id))return null;try{const r={company_uid:e.id,brand_uid:t.id,promotion_name:`CTKM tự động theo kênh ${s}`,promotion_id:`PROMOTION_${Date.now()}`,source_uid:o,store_uid:i,partner_auto_gen:1};return(await F.post("/mdata/v1/promotion",r)).data}catch{return null}}}}function B(t={}){return v({queryKey:[c.DISCOUNTS,"channel",t],queryFn:()=>q(t),staleTime:2*60*1e3,gcTime:5*60*1e3})}function Y(){const t=l();return _({mutationFn:P,onSuccess:()=>{t.invalidateQueries({queryKey:[c.DISCOUNTS]})}})}function z(){const t=l();return _({mutationFn:O,onSuccess:()=>{t.invalidateQueries({queryKey:[c.DISCOUNTS]})}})}const U=T.createContext(void 0);function J({children:t,value:e}){return b.jsx(U.Provider,{value:e,children:t})}function S(){const t=T.useContext(U);if(t===void 0)throw new Error("useDiscountFormContext must be used within a DiscountFormProvider");return t}function X(){const{formData:t,updateFormData:e}=S();return{formData:t,updateFormData:e}}function Z(){const{handleBack:t,handleSave:e}=S();return{handleBack:t,handleSave:e}}function tt(){const{isFormValid:t,isLoading:e,isEditMode:n}=S();return{isFormValid:t,isLoading:e,isEditMode:n}}export{J as D,z as a,B as b,Z as c,tt as d,G as e,L as f,M as g,$ as h,X as i,W as j,I as t,Y as u};
