import{E as k}from"./exceljs.min-BFFGgdR1.js";import{read as u,utils as _,writeFile as x}from"./xlsx-DkH2s96g.js";function C(p,y={}){const{filename:l=`export-${new Date().toISOString().split("T")[0]}.xlsx`,sheetName:t="Data",columnMapping:n}=y,m=n?p.map(e=>{const o={};return Object.entries(n).forEach(([r,c])=>{o[c]=e[r]}),o}):p,s=_.book_new(),a=_.json_to_sheet(m),i=_.decode_range(a["!ref"]||"A1"),d=[];for(let e=i.s.c;e<=i.e.c;e++){let o=10;for(let r=i.s.r;r<=i.e.r;r++){const c=_.encode_cell({r,c:e}),h=a[c];if(h&&h.v){const w=h.v.toString().length;o=Math.max(o,w)}}d.push({wch:Math.min(o+2,50)})}a["!cols"]=d,_.book_append_sheet(s,a,t),x(s,l)}async function R(p,y="update-combo.xlsx"){const l=new k.Workbook,t=l.addWorksheet("Combos");t.columns=[{header:"ID",key:"id",width:15},{header:"Tên",key:"name",width:30},{header:"Cửa hàng áp dụng",key:"store_name",width:25},{header:"Giá",key:"price",width:15},{header:"CTKM",key:"promotion_name",width:25},{header:"Mã Combo",key:"combo_code",width:15},{header:"VAT (%)",key:"vat_percent",width:12},{header:"Ngày bắt đầu",key:"start_date",width:15},{header:"Ngày kết thúc",key:"end_date",width:15},{header:"Ngày áp dụng",key:"apply_date",width:15},{header:"Giờ áp dụng",key:"apply_time",width:15},{header:"Ảnh",key:"image",width:20},{header:"Tên nhóm",key:"group_name",width:20},{header:"Yêu cầu chọn",key:"require_selection",width:15},{header:"Giới hạn chọn",key:"selection_limit",width:15},{header:"Mã món theo nhóm",key:"group_item_codes",width:20},{header:"Giá món con",key:"item_price",width:15}];const n=t.getRow(1);n.font={bold:!0,color:{argb:"FFFFFF"}},n.fill={type:"pattern",pattern:"solid",fgColor:{argb:"4472C4"}},n.alignment={horizontal:"center",vertical:"middle"},n.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},p.forEach((e,o)=>{const r=t.addRow({id:e.id,name:e.name,store_name:e.store_name,price:e.price,promotion_name:e.promotion_name||"",combo_code:e.combo_code||"",vat_percent:e.vat_percent||0,start_date:e.start_date,end_date:e.end_date,apply_date:e.apply_date||"",apply_time:e.apply_time||"",image:e.image||"",group_name:e.group_name||"",require_selection:e.require_selection||"",selection_limit:e.selection_limit||0,group_item_codes:e.group_item_codes||"",item_price:e.item_price||0});r.alignment={horizontal:"left",vertical:"middle"},o%2===0&&(r.fill={type:"pattern",pattern:"solid",fgColor:{argb:"F8F9FA"}}),r.eachCell(w=>{w.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}}});const c=r.getCell("price"),h=r.getCell("item_price");c.numFmt="#,##0",e.item_price&&(h.numFmt="#,##0")}),t.addRow([]);const m=t.getRow(t.rowCount);m.font={italic:!0,color:{argb:"FF0000"}};const s=await l.xlsx.writeBuffer(),a=new Blob([s],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),i=window.URL.createObjectURL(a),d=document.createElement("a");d.href=i,d.download=y,d.click(),window.URL.revokeObjectURL(i)}async function E(p){return new Promise((y,l)=>{const t=new FileReader;t.onload=n=>{var m;try{const s=new Uint8Array((m=n.target)==null?void 0:m.result),a=u(s,{type:"array"}),i=a.SheetNames[0],d=a.Sheets[i],e=_.sheet_to_json(d,{header:1}),o=e[0],c=e.slice(1).map(h=>{const w={};return o.forEach((g,f)=>{w[g]=h[f]||""}),w});y(c)}catch(s){l(s)}},t.onerror=()=>l(new Error("Failed to read file")),t.readAsArrayBuffer(p)})}export{C as a,R as e,E as r};
