import{r as l,z as o,j as e,c,B as d,L as u}from"./index-Bnt3OGV2.js";import{C as x,a as p,b as j,c as h,d as g,f}from"./card-aL168XsH.js";import{A as F}from"./auth-layout-CNHS5raG.js";import{u as C,F as b,a as y,b as N,c as S,d as w,e as L}from"./form-wT1R35uI.js";import{s as I}from"./zod-CNC8A7Cl.js";import{I as v}from"./input-CiKEYbig.js";const E=o.object({email:o.string().min(1,{message:"Please enter your email"}).email({message:"Invalid email address"})});function P({className:t,...n}){const[i,a]=l.useState(!1),s=C({resolver:I(E),defaultValues:{email:""}});function m(r){a(!0),console.log(r),setTimeout(()=>{a(!1)},3e3)}return e.jsx(b,{...s,children:e.jsxs("form",{onSubmit:s.handleSubmit(m),className:c("grid gap-2",t),...n,children:[e.jsx(y,{control:s.control,name:"email",render:({field:r})=>e.jsxs(N,{className:"space-y-1",children:[e.jsx(S,{children:"Email"}),e.jsx(w,{children:e.jsx(v,{placeholder:"<EMAIL>",...r})}),e.jsx(L,{})]})}),e.jsx(d,{className:"mt-2",disabled:i,children:"Continue"})]})})}function k(){return e.jsx(F,{children:e.jsxs(x,{className:"gap-4",children:[e.jsxs(p,{children:[e.jsx(j,{className:"text-lg tracking-tight",children:"Forgot Password"}),e.jsxs(h,{children:["Enter your registered email and ",e.jsx("br",{})," we will send you a link to reset your password."]})]}),e.jsx(g,{children:e.jsx(P,{})}),e.jsx(f,{children:e.jsxs("p",{className:"text-muted-foreground px-8 text-center text-sm",children:["Don't have an account?"," ",e.jsx(u,{to:"/sign-up",className:"hover:text-primary underline underline-offset-4",children:"Sign up"}),"."]})})]})})}const M=k;export{M as component};
