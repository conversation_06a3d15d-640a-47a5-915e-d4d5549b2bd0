import{R as h,r as ye,j as ie,P as qt,c as he,S as Ht}from"./index-C21OP4ex.js";var ve=e=>e.type==="checkbox",oe=e=>e instanceof Date,H=e=>e==null;const _t=e=>typeof e=="object";var O=e=>!H(e)&&!Array.isArray(e)&&_t(e)&&!oe(e),ht=e=>O(e)&&e.target?ve(e.target)?e.target.checked:e.target.value:e,Kt=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,vt=(e,r)=>e.has(Kt(r)),zt=e=>{const r=e.constructor&&e.constructor.prototype;return O(r)&&r.hasOwnProperty("isPrototypeOf")},He=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function M(e){let r;const t=Array.isArray(e),i=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)r=new Date(e);else if(e instanceof Set)r=new Set(e);else if(!(He&&(e instanceof Blob||i))&&(t||O(e)))if(r=t?[]:{},!t&&!zt(e))r=e;else for(const n in e)e.hasOwnProperty(n)&&(r[n]=M(e[n]));else return e;return r}var be=e=>Array.isArray(e)?e.filter(Boolean):[],R=e=>e===void 0,c=(e,r,t)=>{if(!r||!O(e))return t;const i=be(r.split(/[,[\].]+?/)).reduce((n,l)=>H(n)?n:n[l],e);return R(i)||i===e?R(e[r])?t:e[r]:i},Q=e=>typeof e=="boolean",Ke=e=>/^\w*$/.test(e),bt=e=>be(e.replace(/["|']|\]/g,"").split(/\.|\[/)),C=(e,r,t)=>{let i=-1;const n=Ke(r)?[r]:bt(r),l=n.length,f=l-1;for(;++i<l;){const y=n[i];let F=t;if(i!==f){const V=e[y];F=O(V)||Array.isArray(V)?V:isNaN(+n[i+1])?{}:[]}if(y==="__proto__"||y==="constructor"||y==="prototype")return;e[y]=F,e=e[y]}};const Ve={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},ee={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},ae={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},Ft=h.createContext(null),Fe=()=>h.useContext(Ft),Gt=e=>{const{children:r,...t}=e;return h.createElement(Ft.Provider,{value:t},r)};var At=(e,r,t,i=!0)=>{const n={defaultValues:r._defaultValues};for(const l in e)Object.defineProperty(n,l,{get:()=>{const f=l;return r._proxyFormState[f]!==ee.all&&(r._proxyFormState[f]=!i||ee.all),t&&(t[f]=!0),e[f]}});return n};function xt(e){const r=Fe(),{control:t=r.control,disabled:i,name:n,exact:l}=e||{},[f,y]=h.useState(t._formState),F=h.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1}),V=h.useRef(n);return V.current=n,h.useEffect(()=>t._subscribe({name:V.current,formState:F.current,exact:l,callback:w=>{!i&&y({...t._formState,...w})}}),[t,i,l]),h.useEffect(()=>{F.current.isValid&&t._setValid(!0)},[t]),h.useMemo(()=>At(f,t,F.current,!1),[f,t])}var se=e=>typeof e=="string",Vt=(e,r,t,i,n)=>se(e)?(i&&r.watch.add(e),c(t,e,n)):Array.isArray(e)?e.map(l=>(i&&r.watch.add(l),c(t,l))):(i&&(r.watchAll=!0),t);function Yt(e){const r=Fe(),{control:t=r.control,name:i,defaultValue:n,disabled:l,exact:f}=e||{},y=h.useRef(i),F=h.useRef(n);y.current=i,h.useEffect(()=>t._subscribe({name:y.current,formState:{values:!0},exact:f,callback:E=>!l&&w(Vt(y.current,t._names,E.values||t._formValues,!1,F.current))}),[t,l,f]);const[V,w]=h.useState(t._getWatch(i,n));return h.useEffect(()=>t._removeUnmounted()),V}function Jt(e){const r=Fe(),{name:t,disabled:i,control:n=r.control,shouldUnregister:l}=e,f=vt(n._names.array,t),y=Yt({control:n,name:t,defaultValue:c(n._formValues,t,c(n._defaultValues,t,e.defaultValue)),exact:!0}),F=xt({control:n,name:t,exact:!0}),V=h.useRef(e),w=h.useRef(n.register(t,{...e.rules,value:y,...Q(e.disabled)?{disabled:e.disabled}:{}})),E=h.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!c(F.errors,t)},isDirty:{enumerable:!0,get:()=>!!c(F.dirtyFields,t)},isTouched:{enumerable:!0,get:()=>!!c(F.touchedFields,t)},isValidating:{enumerable:!0,get:()=>!!c(F.validatingFields,t)},error:{enumerable:!0,get:()=>c(F.errors,t)}}),[F,t]),A=h.useCallback(B=>w.current.onChange({target:{value:ht(B),name:t},type:Ve.CHANGE}),[t]),L=h.useCallback(()=>w.current.onBlur({target:{value:c(n._formValues,t),name:t},type:Ve.BLUR}),[t,n._formValues]),G=h.useCallback(B=>{const j=c(n._fields,t);j&&B&&(j._f.ref={focus:()=>B.focus(),select:()=>B.select(),setCustomValidity:b=>B.setCustomValidity(b),reportValidity:()=>B.reportValidity()})},[n._fields,t]),P=h.useMemo(()=>({name:t,value:y,...Q(i)||F.disabled?{disabled:F.disabled||i}:{},onChange:A,onBlur:L,ref:G}),[t,i,F.disabled,A,L,G,y]);return h.useEffect(()=>{const B=n._options.shouldUnregister||l;n.register(t,{...V.current.rules,...Q(V.current.disabled)?{disabled:V.current.disabled}:{}});const j=(b,Y)=>{const q=c(n._fields,b);q&&q._f&&(q._f.mount=Y)};if(j(t,!0),B){const b=M(c(n._options.defaultValues,t));C(n._defaultValues,t,b),R(c(n._formValues,t))&&C(n._formValues,t,b)}return!f&&n.register(t),()=>{(f?B&&!n._state.action:B)?n.unregister(t):j(t,!1)}},[t,n,f,l]),h.useEffect(()=>{n._setDisabledField({disabled:i,name:t})},[i,t,n]),h.useMemo(()=>({field:P,formState:F,fieldState:E}),[P,F,E])}const Qt=e=>e.render(Jt(e));var Xt=(e,r,t,i,n)=>r?{...t[e],types:{...t[e]&&t[e].types?t[e].types:{},[i]:n||!0}}:{},z=e=>Array.isArray(e)?e:[e],lt=()=>{let e=[];return{get observers(){return e},next:n=>{for(const l of e)l.next&&l.next(n)},subscribe:n=>(e.push(n),{unsubscribe:()=>{e=e.filter(l=>l!==n)}}),unsubscribe:()=>{e=[]}}},$e=e=>H(e)||!_t(e);function ue(e,r){if($e(e)||$e(r))return e===r;if(oe(e)&&oe(r))return e.getTime()===r.getTime();const t=Object.keys(e),i=Object.keys(r);if(t.length!==i.length)return!1;for(const n of t){const l=e[n];if(!i.includes(n))return!1;if(n!=="ref"){const f=r[n];if(oe(l)&&oe(f)||O(l)&&O(f)||Array.isArray(l)&&Array.isArray(f)?!ue(l,f):l!==f)return!1}}return!0}var W=e=>O(e)&&!Object.keys(e).length,ze=e=>e.type==="file",te=e=>typeof e=="function",Se=e=>{if(!He)return!1;const r=e?e.ownerDocument:0;return e instanceof(r&&r.defaultView?r.defaultView.HTMLElement:HTMLElement)},St=e=>e.type==="select-multiple",Ge=e=>e.type==="radio",Zt=e=>Ge(e)||ve(e),Me=e=>Se(e)&&e.isConnected;function er(e,r){const t=r.slice(0,-1).length;let i=0;for(;i<t;)e=R(e)?i++:e[r[i++]];return e}function tr(e){for(const r in e)if(e.hasOwnProperty(r)&&!R(e[r]))return!1;return!0}function N(e,r){const t=Array.isArray(r)?r:Ke(r)?[r]:bt(r),i=t.length===1?e:er(e,t),n=t.length-1,l=t[n];return i&&delete i[l],n!==0&&(O(i)&&W(i)||Array.isArray(i)&&tr(i))&&N(e,t.slice(0,-1)),e}var wt=e=>{for(const r in e)if(te(e[r]))return!0;return!1};function we(e,r={}){const t=Array.isArray(e);if(O(e)||t)for(const i in e)Array.isArray(e[i])||O(e[i])&&!wt(e[i])?(r[i]=Array.isArray(e[i])?[]:{},we(e[i],r[i])):H(e[i])||(r[i]=!0);return r}function Dt(e,r,t){const i=Array.isArray(e);if(O(e)||i)for(const n in e)Array.isArray(e[n])||O(e[n])&&!wt(e[n])?R(r)||$e(t[n])?t[n]=Array.isArray(e[n])?we(e[n],[]):{...we(e[n])}:Dt(e[n],H(r)?{}:r[n],t[n]):t[n]=!ue(e[n],r[n]);return t}var me=(e,r)=>Dt(e,r,we(r));const ut={value:!1,isValid:!1},ot={value:!0,isValid:!0};var kt=e=>{if(Array.isArray(e)){if(e.length>1){const r=e.filter(t=>t&&t.checked&&!t.disabled).map(t=>t.value);return{value:r,isValid:!!r.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!R(e[0].attributes.value)?R(e[0].value)||e[0].value===""?ot:{value:e[0].value,isValid:!0}:ot:ut}return ut},pt=(e,{valueAsNumber:r,valueAsDate:t,setValueAs:i})=>R(e)?e:r?e===""?NaN:e&&+e:t&&se(e)?new Date(e):i?i(e):e;const ct={isValid:!1,value:null};var Et=e=>Array.isArray(e)?e.reduce((r,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:r,ct):ct;function dt(e){const r=e.ref;return ze(r)?r.files:Ge(r)?Et(e.refs).value:St(r)?[...r.selectedOptions].map(({value:t})=>t):ve(r)?kt(e.refs).value:pt(R(r.value)?e.ref.value:r.value,e)}var rr=(e,r,t,i)=>{const n={};for(const l of e){const f=c(r,l);f&&C(n,l,f._f)}return{criteriaMode:t,names:[...e],fields:n,shouldUseNativeValidation:i}},De=e=>e instanceof RegExp,_e=e=>R(e)?e:De(e)?e.source:O(e)?De(e.value)?e.value.source:e.value:e,de=e=>({isOnSubmit:!e||e===ee.onSubmit,isOnBlur:e===ee.onBlur,isOnChange:e===ee.onChange,isOnAll:e===ee.all,isOnTouch:e===ee.onTouched});const ft="AsyncFunction";var sr=e=>!!e&&!!e.validate&&!!(te(e.validate)&&e.validate.constructor.name===ft||O(e.validate)&&Object.values(e.validate).find(r=>r.constructor.name===ft)),ir=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),We=(e,r,t)=>!t&&(r.watchAll||r.watch.has(e)||[...r.watch].some(i=>e.startsWith(i)&&/^\.\w+/.test(e.slice(i.length))));const fe=(e,r,t,i)=>{for(const n of t||Object.keys(e)){const l=c(e,n);if(l){const{_f:f,...y}=l;if(f){if(f.refs&&f.refs[0]&&r(f.refs[0],n)&&!i)return!0;if(f.ref&&r(f.ref,f.name)&&!i)return!0;if(fe(y,r))break}else if(O(y)&&fe(y,r))break}}};function yt(e,r,t){const i=c(e,t);if(i||Ke(t))return{error:i,name:t};const n=t.split(".");for(;n.length;){const l=n.join("."),f=c(r,l),y=c(e,l);if(f&&!Array.isArray(f)&&t!==l)return{name:t};if(y&&y.type)return{name:l,error:y};n.pop()}return{name:t}}var ar=(e,r,t,i)=>{t(e);const{name:n,...l}=e;return W(l)||Object.keys(l).length>=Object.keys(r).length||Object.keys(l).find(f=>r[f]===(!i||ee.all))},nr=(e,r,t)=>!e||!r||e===r||z(e).some(i=>i&&(t?i===r:i.startsWith(r)||r.startsWith(i))),lr=(e,r,t,i,n)=>n.isOnAll?!1:!t&&n.isOnTouch?!(r||e):(t?i.isOnBlur:n.isOnBlur)?!e:(t?i.isOnChange:n.isOnChange)?e:!0,ur=(e,r)=>!be(c(e,r)).length&&N(e,r),Ct=(e,r,t)=>{const i=z(c(e,t));return C(i,"root",r[t]),C(e,t,i),e},xe=e=>se(e);function gt(e,r,t="validate"){if(xe(e)||Array.isArray(e)&&e.every(xe)||Q(e)&&!e)return{type:t,message:xe(e)?e:"",ref:r}}var ce=e=>O(e)&&!De(e)?e:{value:e,message:""},qe=async(e,r,t,i,n,l)=>{const{ref:f,refs:y,required:F,maxLength:V,minLength:w,min:E,max:A,pattern:L,validate:G,name:P,valueAsNumber:B,mount:j}=e._f,b=c(t,P);if(!j||r.has(P))return{};const Y=y?y[0]:f,q=p=>{n&&Y.reportValidity&&(Y.setCustomValidity(Q(p)?"":p||""),Y.reportValidity())},T={},v=Ge(f),_=ve(f),x=v||_,k=(B||ze(f))&&R(f.value)&&R(b)||Se(f)&&f.value===""||b===""||Array.isArray(b)&&!b.length,J=Xt.bind(null,P,i,T),X=(p,S,I,K=ae.maxLength,Z=ae.minLength)=>{const re=p?S:I;T[P]={type:p?K:Z,message:re,ref:f,...J(p?K:Z,re)}};if(l?!Array.isArray(b)||!b.length:F&&(!x&&(k||H(b))||Q(b)&&!b||_&&!kt(y).isValid||v&&!Et(y).isValid)){const{value:p,message:S}=xe(F)?{value:!!F,message:F}:ce(F);if(p&&(T[P]={type:ae.required,message:S,ref:Y,...J(ae.required,S)},!i))return q(S),T}if(!k&&(!H(E)||!H(A))){let p,S;const I=ce(A),K=ce(E);if(!H(b)&&!isNaN(b)){const Z=f.valueAsNumber||b&&+b;H(I.value)||(p=Z>I.value),H(K.value)||(S=Z<K.value)}else{const Z=f.valueAsDate||new Date(b),re=Ae=>new Date(new Date().toDateString()+" "+Ae),ne=f.type=="time",ge=f.type=="week";se(I.value)&&b&&(p=ne?re(b)>re(I.value):ge?b>I.value:Z>new Date(I.value)),se(K.value)&&b&&(S=ne?re(b)<re(K.value):ge?b<K.value:Z<new Date(K.value))}if((p||S)&&(X(!!p,I.message,K.message,ae.max,ae.min),!i))return q(T[P].message),T}if((V||w)&&!k&&(se(b)||l&&Array.isArray(b))){const p=ce(V),S=ce(w),I=!H(p.value)&&b.length>+p.value,K=!H(S.value)&&b.length<+S.value;if((I||K)&&(X(I,p.message,S.message),!i))return q(T[P].message),T}if(L&&!k&&se(b)){const{value:p,message:S}=ce(L);if(De(p)&&!b.match(p)&&(T[P]={type:ae.pattern,message:S,ref:f,...J(ae.pattern,S)},!i))return q(S),T}if(G){if(te(G)){const p=await G(b,t),S=gt(p,Y);if(S&&(T[P]={...S,...J(ae.validate,S.message)},!i))return q(S.message),T}else if(O(G)){let p={};for(const S in G){if(!W(p)&&!i)break;const I=gt(await G[S](b,t),Y,S);I&&(p={...I,...J(S,I.message)},q(I.message),i&&(T[P]=p))}if(!W(p)&&(T[P]={ref:Y,...p},!i))return T}}return q(!0),T};const or={mode:ee.onSubmit,reValidateMode:ee.onChange,shouldFocusError:!0};function cr(e={}){let r={...or,...e},t={submitCount:0,isDirty:!1,isLoading:te(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1};const i={};let n=O(r.defaultValues)||O(r.values)?M(r.values||r.defaultValues)||{}:{},l=r.shouldUnregister?{}:M(n),f={action:!1,mount:!1,watch:!1},y={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},F,V=0;const w={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let E={...w};const A={array:lt(),state:lt()},L=de(r.mode),G=de(r.reValidateMode),P=r.criteriaMode===ee.all,B=s=>a=>{clearTimeout(V),V=setTimeout(s,a)},j=async s=>{if(!r.disabled&&(w.isValid||E.isValid||s)){const a=r.resolver?W((await k()).errors):await X(i,!0);a!==t.isValid&&A.state.next({isValid:a})}},b=(s,a)=>{!r.disabled&&(w.isValidating||w.validatingFields||E.isValidating||E.validatingFields)&&((s||Array.from(y.mount)).forEach(u=>{u&&(a?C(t.validatingFields,u,a):N(t.validatingFields,u))}),A.state.next({validatingFields:t.validatingFields,isValidating:!W(t.validatingFields)}))},Y=(s,a=[],u,g,d=!0,o=!0)=>{if(g&&u&&!r.disabled){if(f.action=!0,o&&Array.isArray(c(i,s))){const m=u(c(i,s),g.argA,g.argB);d&&C(i,s,m)}if(o&&Array.isArray(c(t.errors,s))){const m=u(c(t.errors,s),g.argA,g.argB);d&&C(t.errors,s,m),ur(t.errors,s)}if((w.touchedFields||E.touchedFields)&&o&&Array.isArray(c(t.touchedFields,s))){const m=u(c(t.touchedFields,s),g.argA,g.argB);d&&C(t.touchedFields,s,m)}(w.dirtyFields||E.dirtyFields)&&(t.dirtyFields=me(n,l)),A.state.next({name:s,isDirty:S(s,a),dirtyFields:t.dirtyFields,errors:t.errors,isValid:t.isValid})}else C(l,s,a)},q=(s,a)=>{C(t.errors,s,a),A.state.next({errors:t.errors})},T=s=>{t.errors=s,A.state.next({errors:t.errors,isValid:!1})},v=(s,a,u,g)=>{const d=c(i,s);if(d){const o=c(l,s,R(u)?c(n,s):u);R(o)||g&&g.defaultChecked||a?C(l,s,a?o:dt(d._f)):Z(s,o),f.mount&&j()}},_=(s,a,u,g,d)=>{let o=!1,m=!1;const D={name:s};if(!r.disabled){if(!u||g){(w.isDirty||E.isDirty)&&(m=t.isDirty,t.isDirty=D.isDirty=S(),o=m!==D.isDirty);const U=ue(c(n,s),a);m=!!c(t.dirtyFields,s),U?N(t.dirtyFields,s):C(t.dirtyFields,s,!0),D.dirtyFields=t.dirtyFields,o=o||(w.dirtyFields||E.dirtyFields)&&m!==!U}if(u){const U=c(t.touchedFields,s);U||(C(t.touchedFields,s,u),D.touchedFields=t.touchedFields,o=o||(w.touchedFields||E.touchedFields)&&U!==u)}o&&d&&A.state.next(D)}return o?D:{}},x=(s,a,u,g)=>{const d=c(t.errors,s),o=(w.isValid||E.isValid)&&Q(a)&&t.isValid!==a;if(r.delayError&&u?(F=B(()=>q(s,u)),F(r.delayError)):(clearTimeout(V),F=null,u?C(t.errors,s,u):N(t.errors,s)),(u?!ue(d,u):d)||!W(g)||o){const m={...g,...o&&Q(a)?{isValid:a}:{},errors:t.errors,name:s};t={...t,...m},A.state.next(m)}},k=async s=>{b(s,!0);const a=await r.resolver(l,r.context,rr(s||y.mount,i,r.criteriaMode,r.shouldUseNativeValidation));return b(s),a},J=async s=>{const{errors:a}=await k(s);if(s)for(const u of s){const g=c(a,u);g?C(t.errors,u,g):N(t.errors,u)}else t.errors=a;return a},X=async(s,a,u={valid:!0})=>{for(const g in s){const d=s[g];if(d){const{_f:o,...m}=d;if(o){const D=y.array.has(o.name),U=d._f&&sr(d._f);U&&w.validatingFields&&b([g],!0);const $=await qe(d,y.disabled,l,P,r.shouldUseNativeValidation&&!a,D);if(U&&w.validatingFields&&b([g]),$[o.name]&&(u.valid=!1,a))break;!a&&(c($,o.name)?D?Ct(t.errors,$,o.name):C(t.errors,o.name,$[o.name]):N(t.errors,o.name))}!W(m)&&await X(m,a,u)}}return u.valid},p=()=>{for(const s of y.unMount){const a=c(i,s);a&&(a._f.refs?a._f.refs.every(u=>!Me(u)):!Me(a._f.ref))&&Ee(s)}y.unMount=new Set},S=(s,a)=>!r.disabled&&(s&&a&&C(l,s,a),!ue(Ye(),n)),I=(s,a,u)=>Vt(s,y,{...f.mount?l:R(a)?n:se(s)?{[s]:a}:a},u,a),K=s=>be(c(f.mount?l:n,s,r.shouldUnregister?c(n,s,[]):[])),Z=(s,a,u={})=>{const g=c(i,s);let d=a;if(g){const o=g._f;o&&(!o.disabled&&C(l,s,pt(a,o)),d=Se(o.ref)&&H(a)?"":a,St(o.ref)?[...o.ref.options].forEach(m=>m.selected=d.includes(m.value)):o.refs?ve(o.ref)?o.refs.length>1?o.refs.forEach(m=>(!m.defaultChecked||!m.disabled)&&(m.checked=Array.isArray(d)?!!d.find(D=>D===m.value):d===m.value)):o.refs[0]&&(o.refs[0].checked=!!d):o.refs.forEach(m=>m.checked=m.value===d):ze(o.ref)?o.ref.value="":(o.ref.value=d,o.ref.type||A.state.next({name:s,values:M(l)})))}(u.shouldDirty||u.shouldTouch)&&_(s,d,u.shouldTouch,u.shouldDirty,!0),u.shouldValidate&&pe(s)},re=(s,a,u)=>{for(const g in a){const d=a[g],o=`${s}.${g}`,m=c(i,o);(y.array.has(s)||O(d)||m&&!m._f)&&!oe(d)?re(o,d,u):Z(o,d,u)}},ne=(s,a,u={})=>{const g=c(i,s),d=y.array.has(s),o=M(a);C(l,s,o),d?(A.array.next({name:s,values:M(l)}),(w.isDirty||w.dirtyFields||E.isDirty||E.dirtyFields)&&u.shouldDirty&&A.state.next({name:s,dirtyFields:me(n,l),isDirty:S(s,o)})):g&&!g._f&&!H(o)?re(s,o,u):Z(s,o,u),We(s,y)&&A.state.next({...t}),A.state.next({name:f.mount?s:void 0,values:M(l)})},ge=async s=>{f.mount=!0;const a=s.target;let u=a.name,g=!0;const d=c(i,u),o=m=>{g=Number.isNaN(m)||oe(m)&&isNaN(m.getTime())||ue(m,c(l,u,m))};if(d){let m,D;const U=a.type?dt(d._f):ht(s),$=s.type===Ve.BLUR||s.type===Ve.FOCUS_OUT,jt=!ir(d._f)&&!r.resolver&&!c(t.errors,u)&&!d._f.deps||lr($,c(t.touchedFields,u),t.isSubmitted,G,L),Re=We(u,y,$);C(l,u,U),$?(d._f.onBlur&&d._f.onBlur(s),F&&F(0)):d._f.onChange&&d._f.onChange(s);const Le=_(u,U,$),$t=!W(Le)||Re;if(!$&&A.state.next({name:u,type:s.type,values:M(l)}),jt)return(w.isValid||E.isValid)&&(r.mode==="onBlur"?$&&j():$||j()),$t&&A.state.next({name:u,...Re?{}:Le});if(!$&&Re&&A.state.next({...t}),r.resolver){const{errors:at}=await k([u]);if(o(U),g){const Wt=yt(t.errors,i,u),nt=yt(at,i,Wt.name||u);m=nt.error,u=nt.name,D=W(at)}}else b([u],!0),m=(await qe(d,y.disabled,l,P,r.shouldUseNativeValidation))[u],b([u]),o(U),g&&(m?D=!1:(w.isValid||E.isValid)&&(D=await X(i,!0)));g&&(d._f.deps&&pe(d._f.deps),x(u,D,m,Le))}},Ae=(s,a)=>{if(c(t.errors,a)&&s.focus)return s.focus(),1},pe=async(s,a={})=>{let u,g;const d=z(s);if(r.resolver){const o=await J(R(s)?s:d);u=W(o),g=s?!d.some(m=>c(o,m)):u}else s?(g=(await Promise.all(d.map(async o=>{const m=c(i,o);return await X(m&&m._f?{[o]:m}:m)}))).every(Boolean),!(!g&&!t.isValid)&&j()):g=u=await X(i);return A.state.next({...!se(s)||(w.isValid||E.isValid)&&u!==t.isValid?{}:{name:s},...r.resolver||!s?{isValid:u}:{},errors:t.errors}),a.shouldFocus&&!g&&fe(i,Ae,s?d:y.mount),g},Ye=s=>{const a={...f.mount?l:n};return R(s)?a:se(s)?c(a,s):s.map(u=>c(a,u))},Je=(s,a)=>({invalid:!!c((a||t).errors,s),isDirty:!!c((a||t).dirtyFields,s),error:c((a||t).errors,s),isValidating:!!c(t.validatingFields,s),isTouched:!!c((a||t).touchedFields,s)}),It=s=>{s&&z(s).forEach(a=>N(t.errors,a)),A.state.next({errors:s?t.errors:{}})},Qe=(s,a,u)=>{const g=(c(i,s,{_f:{}})._f||{}).ref,d=c(t.errors,s)||{},{ref:o,message:m,type:D,...U}=d;C(t.errors,s,{...U,...a,ref:g}),A.state.next({name:s,errors:t.errors,isValid:!1}),u&&u.shouldFocus&&g&&g.focus&&g.focus()},Ot=(s,a)=>te(s)?A.state.subscribe({next:u=>s(I(void 0,a),u)}):I(s,a,!0),Xe=s=>A.state.subscribe({next:a=>{nr(s.name,a.name,s.exact)&&ar(a,s.formState||w,Bt,s.reRenderRoot)&&s.callback({values:{...l},...t,...a})}}).unsubscribe,Tt=s=>(f.mount=!0,E={...E,...s.formState},Xe({...s,formState:E})),Ee=(s,a={})=>{for(const u of s?z(s):y.mount)y.mount.delete(u),y.array.delete(u),a.keepValue||(N(i,u),N(l,u)),!a.keepError&&N(t.errors,u),!a.keepDirty&&N(t.dirtyFields,u),!a.keepTouched&&N(t.touchedFields,u),!a.keepIsValidating&&N(t.validatingFields,u),!r.shouldUnregister&&!a.keepDefaultValue&&N(n,u);A.state.next({values:M(l)}),A.state.next({...t,...a.keepDirty?{isDirty:S()}:{}}),!a.keepIsValid&&j()},Ze=({disabled:s,name:a})=>{(Q(s)&&f.mount||s||y.disabled.has(a))&&(s?y.disabled.add(a):y.disabled.delete(a))},Ce=(s,a={})=>{let u=c(i,s);const g=Q(a.disabled)||Q(r.disabled);return C(i,s,{...u||{},_f:{...u&&u._f?u._f:{ref:{name:s}},name:s,mount:!0,...a}}),y.mount.add(s),u?Ze({disabled:Q(a.disabled)?a.disabled:r.disabled,name:s}):v(s,!0,a.value),{...g?{disabled:a.disabled||r.disabled}:{},...r.progressive?{required:!!a.required,min:_e(a.min),max:_e(a.max),minLength:_e(a.minLength),maxLength:_e(a.maxLength),pattern:_e(a.pattern)}:{},name:s,onChange:ge,onBlur:ge,ref:d=>{if(d){Ce(s,a),u=c(i,s);const o=R(d.value)&&d.querySelectorAll&&d.querySelectorAll("input,select,textarea")[0]||d,m=Zt(o),D=u._f.refs||[];if(m?D.find(U=>U===o):o===u._f.ref)return;C(i,s,{_f:{...u._f,...m?{refs:[...D.filter(Me),o,...Array.isArray(c(n,s))?[{}]:[]],ref:{type:o.type,name:s}}:{ref:o}}}),v(s,!1,void 0,o)}else u=c(i,s,{}),u._f&&(u._f.mount=!1),(r.shouldUnregister||a.shouldUnregister)&&!(vt(y.array,s)&&f.action)&&y.unMount.add(s)}}},et=()=>r.shouldFocusError&&fe(i,Ae,y.mount),Ut=s=>{Q(s)&&(A.state.next({disabled:s}),fe(i,(a,u)=>{const g=c(i,u);g&&(a.disabled=g._f.disabled||s,Array.isArray(g._f.refs)&&g._f.refs.forEach(d=>{d.disabled=g._f.disabled||s}))},0,!1))},tt=(s,a)=>async u=>{let g;u&&(u.preventDefault&&u.preventDefault(),u.persist&&u.persist());let d=M(l);if(A.state.next({isSubmitting:!0}),r.resolver){const{errors:o,values:m}=await k();t.errors=o,d=m}else await X(i);if(y.disabled.size)for(const o of y.disabled)C(d,o,void 0);if(N(t.errors,"root"),W(t.errors)){A.state.next({errors:{}});try{await s(d,u)}catch(o){g=o}}else a&&await a({...t.errors},u),et(),setTimeout(et);if(A.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:W(t.errors)&&!g,submitCount:t.submitCount+1,errors:t.errors}),g)throw g},Nt=(s,a={})=>{c(i,s)&&(R(a.defaultValue)?ne(s,M(c(n,s))):(ne(s,a.defaultValue),C(n,s,M(a.defaultValue))),a.keepTouched||N(t.touchedFields,s),a.keepDirty||(N(t.dirtyFields,s),t.isDirty=a.defaultValue?S(s,M(c(n,s))):S()),a.keepError||(N(t.errors,s),w.isValid&&j()),A.state.next({...t}))},rt=(s,a={})=>{const u=s?M(s):n,g=M(u),d=W(s),o=d?n:g;if(a.keepDefaultValues||(n=u),!a.keepValues){if(a.keepDirtyValues){const m=new Set([...y.mount,...Object.keys(me(n,l))]);for(const D of Array.from(m))c(t.dirtyFields,D)?C(o,D,c(l,D)):ne(D,c(o,D))}else{if(He&&R(s))for(const m of y.mount){const D=c(i,m);if(D&&D._f){const U=Array.isArray(D._f.refs)?D._f.refs[0]:D._f.ref;if(Se(U)){const $=U.closest("form");if($){$.reset();break}}}}for(const m of y.mount)ne(m,c(o,m))}l=M(o),A.array.next({values:{...o}}),A.state.next({values:{...o}})}y={mount:a.keepDirtyValues?y.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},f.mount=!w.isValid||!!a.keepIsValid||!!a.keepDirtyValues,f.watch=!!r.shouldUnregister,A.state.next({submitCount:a.keepSubmitCount?t.submitCount:0,isDirty:d?!1:a.keepDirty?t.isDirty:!!(a.keepDefaultValues&&!ue(s,n)),isSubmitted:a.keepIsSubmitted?t.isSubmitted:!1,dirtyFields:d?{}:a.keepDirtyValues?a.keepDefaultValues&&l?me(n,l):t.dirtyFields:a.keepDefaultValues&&s?me(n,s):a.keepDirty?t.dirtyFields:{},touchedFields:a.keepTouched?t.touchedFields:{},errors:a.keepErrors?t.errors:{},isSubmitSuccessful:a.keepIsSubmitSuccessful?t.isSubmitSuccessful:!1,isSubmitting:!1})},st=(s,a)=>rt(te(s)?s(l):s,a),Pt=(s,a={})=>{const u=c(i,s),g=u&&u._f;if(g){const d=g.refs?g.refs[0]:g.ref;d.focus&&(d.focus(),a.shouldSelect&&te(d.select)&&d.select())}},Bt=s=>{t={...t,...s}},it={control:{register:Ce,unregister:Ee,getFieldState:Je,handleSubmit:tt,setError:Qe,_subscribe:Xe,_runSchema:k,_getWatch:I,_getDirty:S,_setValid:j,_setFieldArray:Y,_setDisabledField:Ze,_setErrors:T,_getFieldArray:K,_reset:rt,_resetDefaultValues:()=>te(r.defaultValues)&&r.defaultValues().then(s=>{st(s,r.resetOptions),A.state.next({isLoading:!1})}),_removeUnmounted:p,_disableForm:Ut,_subjects:A,_proxyFormState:w,get _fields(){return i},get _formValues(){return l},get _state(){return f},set _state(s){f=s},get _defaultValues(){return n},get _names(){return y},set _names(s){y=s},get _formState(){return t},get _options(){return r},set _options(s){r={...r,...s}}},subscribe:Tt,trigger:pe,register:Ce,handleSubmit:tt,watch:Ot,setValue:ne,getValues:Ye,reset:st,resetField:Nt,clearErrors:It,unregister:Ee,setError:Qe,setFocus:Pt,getFieldState:Je};return{...it,formControl:it}}var le=()=>{const e=typeof performance>"u"?Date.now():performance.now()*1e3;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,r=>{const t=(Math.random()*16+e)%16|0;return(r=="x"?t:t&3|8).toString(16)})},Ie=(e,r,t={})=>t.shouldFocus||R(t.shouldFocus)?t.focusName||`${e}.${R(t.focusIndex)?r:t.focusIndex}.`:"",Oe=(e,r)=>[...e,...z(r)],Te=e=>Array.isArray(e)?e.map(()=>{}):void 0;function Ue(e,r,t){return[...e.slice(0,r),...z(t),...e.slice(r)]}var Ne=(e,r,t)=>Array.isArray(e)?(R(e[t])&&(e[t]=void 0),e.splice(t,0,e.splice(r,1)[0]),e):[],Pe=(e,r)=>[...z(r),...z(e)];function dr(e,r){let t=0;const i=[...e];for(const n of r)i.splice(n-t,1),t++;return be(i).length?i:[]}var Be=(e,r)=>R(r)?[]:dr(e,z(r).sort((t,i)=>t-i)),je=(e,r,t)=>{[e[r],e[t]]=[e[t],e[r]]},mt=(e,r,t)=>(e[r]=t,e);function hr(e){const r=Fe(),{control:t=r.control,name:i,keyName:n="id",shouldUnregister:l,rules:f}=e,[y,F]=h.useState(t._getFieldArray(i)),V=h.useRef(t._getFieldArray(i).map(le)),w=h.useRef(y),E=h.useRef(i),A=h.useRef(!1);E.current=i,w.current=y,t._names.array.add(i),f&&t.register(i,f),h.useEffect(()=>t._subjects.array.subscribe({next:({values:v,name:_})=>{if(_===E.current||!_){const x=c(v,E.current);Array.isArray(x)&&(F(x),V.current=x.map(le))}}}).unsubscribe,[t]);const L=h.useCallback(v=>{A.current=!0,t._setFieldArray(i,v)},[t,i]),G=(v,_)=>{const x=z(M(v)),k=Oe(t._getFieldArray(i),x);t._names.focus=Ie(i,k.length-1,_),V.current=Oe(V.current,x.map(le)),L(k),F(k),t._setFieldArray(i,k,Oe,{argA:Te(v)})},P=(v,_)=>{const x=z(M(v)),k=Pe(t._getFieldArray(i),x);t._names.focus=Ie(i,0,_),V.current=Pe(V.current,x.map(le)),L(k),F(k),t._setFieldArray(i,k,Pe,{argA:Te(v)})},B=v=>{const _=Be(t._getFieldArray(i),v);V.current=Be(V.current,v),L(_),F(_),!Array.isArray(c(t._fields,i))&&C(t._fields,i,void 0),t._setFieldArray(i,_,Be,{argA:v})},j=(v,_,x)=>{const k=z(M(_)),J=Ue(t._getFieldArray(i),v,k);t._names.focus=Ie(i,v,x),V.current=Ue(V.current,v,k.map(le)),L(J),F(J),t._setFieldArray(i,J,Ue,{argA:v,argB:Te(_)})},b=(v,_)=>{const x=t._getFieldArray(i);je(x,v,_),je(V.current,v,_),L(x),F(x),t._setFieldArray(i,x,je,{argA:v,argB:_},!1)},Y=(v,_)=>{const x=t._getFieldArray(i);Ne(x,v,_),Ne(V.current,v,_),L(x),F(x),t._setFieldArray(i,x,Ne,{argA:v,argB:_},!1)},q=(v,_)=>{const x=M(_),k=mt(t._getFieldArray(i),v,x);V.current=[...k].map((J,X)=>!J||X===v?le():V.current[X]),L(k),F([...k]),t._setFieldArray(i,k,mt,{argA:v,argB:x},!0,!1)},T=v=>{const _=z(M(v));V.current=_.map(le),L([..._]),F([..._]),t._setFieldArray(i,[..._],x=>x,{},!0,!1)};return h.useEffect(()=>{if(t._state.action=!1,We(i,t._names)&&t._subjects.state.next({...t._formState}),A.current&&(!de(t._options.mode).isOnSubmit||t._formState.isSubmitted)&&!de(t._options.reValidateMode).isOnSubmit)if(t._options.resolver)t._runSchema([i]).then(v=>{const _=c(v.errors,i),x=c(t._formState.errors,i);(x?!_&&x.type||_&&(x.type!==_.type||x.message!==_.message):_&&_.type)&&(_?C(t._formState.errors,i,_):N(t._formState.errors,i),t._subjects.state.next({errors:t._formState.errors}))});else{const v=c(t._fields,i);v&&v._f&&!(de(t._options.reValidateMode).isOnSubmit&&de(t._options.mode).isOnSubmit)&&qe(v,t._names.disabled,t._formValues,t._options.criteriaMode===ee.all,t._options.shouldUseNativeValidation,!0).then(_=>!W(_)&&t._subjects.state.next({errors:Ct(t._formState.errors,_,i)}))}t._subjects.state.next({name:i,values:M(t._formValues)}),t._names.focus&&fe(t._fields,(v,_)=>{if(t._names.focus&&_.startsWith(t._names.focus)&&v.focus)return v.focus(),1}),t._names.focus="",t._setValid(),A.current=!1},[y,i,t]),h.useEffect(()=>(!c(t._formValues,i)&&t._setFieldArray(i),()=>{const v=(_,x)=>{const k=c(t._fields,_);k&&k._f&&(k._f.mount=x)};t._options.shouldUnregister||l?t.unregister(i):v(i,!1)}),[i,t,n,l]),{swap:h.useCallback(b,[L,i,t]),move:h.useCallback(Y,[L,i,t]),prepend:h.useCallback(P,[L,i,t]),append:h.useCallback(G,[L,i,t]),remove:h.useCallback(B,[L,i,t]),insert:h.useCallback(j,[L,i,t]),update:h.useCallback(q,[L,i,t]),replace:h.useCallback(T,[L,i,t]),fields:h.useMemo(()=>y.map((v,_)=>({...v,[n]:V.current[_]||le()})),[y,n])}}function vr(e={}){const r=h.useRef(void 0),t=h.useRef(void 0),[i,n]=h.useState({isDirty:!1,isValidating:!1,isLoading:te(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,defaultValues:te(e.defaultValues)?void 0:e.defaultValues});r.current||(r.current={...e.formControl?e.formControl:cr(e),formState:i},e.formControl&&e.defaultValues&&!te(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));const l=r.current.control;return l._options=e,h.useLayoutEffect(()=>l._subscribe({formState:l._proxyFormState,callback:()=>n({...l._formState}),reRenderRoot:!0}),[l]),h.useEffect(()=>l._disableForm(e.disabled),[l,e.disabled]),h.useEffect(()=>{if(l._proxyFormState.isDirty){const f=l._getDirty();f!==i.isDirty&&l._subjects.state.next({isDirty:f})}},[l,i.isDirty]),h.useEffect(()=>{e.values&&!ue(e.values,t.current)?(l._reset(e.values,l._options.resetOptions),t.current=e.values,n(f=>({...f}))):l._resetDefaultValues()},[e.values,l]),h.useEffect(()=>{e.errors&&!W(e.errors)&&l._setErrors(e.errors)},[e.errors,l]),h.useEffect(()=>{l._state.mount||(l._setValid(),l._state.mount=!0),l._state.watch&&(l._state.watch=!1,l._subjects.state.next({...l._formState})),l._removeUnmounted()}),h.useEffect(()=>{e.shouldUnregister&&l._subjects.state.next({values:l._getWatch()})},[e.shouldUnregister,l]),r.current.formState=At(i,l),r.current}var fr="Label",Rt=ye.forwardRef((e,r)=>ie.jsx(qt.label,{...e,ref:r,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||((n=e.onMouseDown)==null||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));Rt.displayName=fr;var yr=Rt;function gr({className:e,...r}){return ie.jsx(yr,{"data-slot":"label",className:he("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}const br=Gt,Lt=ye.createContext({}),Fr=({...e})=>ie.jsx(Lt.Provider,{value:{name:e.name},children:ie.jsx(Qt,{...e})}),ke=()=>{const e=ye.useContext(Lt),r=ye.useContext(Mt),{getFieldState:t}=Fe(),i=xt({name:e.name}),n=t(e.name,i);if(!e)throw new Error("useFormField should be used within <FormField>");const{id:l}=r;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...n}},Mt=ye.createContext({});function Ar({className:e,...r}){const t=ye.useId();return ie.jsx(Mt.Provider,{value:{id:t},children:ie.jsx("div",{"data-slot":"form-item",className:he("grid gap-2",e),...r})})}function xr({className:e,...r}){const{error:t,formItemId:i}=ke();return ie.jsx(gr,{"data-slot":"form-label","data-error":!!t,className:he("data-[error=true]:text-destructive",e),htmlFor:i,...r})}function Vr({...e}){const{error:r,formItemId:t,formDescriptionId:i,formMessageId:n}=ke();return ie.jsx(Ht,{"data-slot":"form-control",id:t,"aria-describedby":r?`${i} ${n}`:`${i}`,"aria-invalid":!!r,...e})}function Sr({className:e,...r}){const{formDescriptionId:t}=ke();return ie.jsx("p",{"data-slot":"form-description",id:t,className:he("text-muted-foreground text-sm",e),...r})}function wr({className:e,...r}){const{error:t,formMessageId:i}=ke(),n=t?String((t==null?void 0:t.message)??""):r.children;return n?ie.jsx("p",{"data-slot":"form-message",id:i,className:he("text-destructive text-sm",e),...r,children:n}):null}export{br as F,gr as L,Fr as a,Ar as b,xr as c,Vr as d,wr as e,hr as f,Sr as g,c as h,Xt as i,Fe as j,Yt as k,C as s,vr as u};
