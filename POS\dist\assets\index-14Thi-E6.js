import{aR as m,j as i}from"./index-Bnt3OGV2.js";import"./pos-api-BwpRFGce.js";import"./vietqr-api-DAENYiJ_.js";import"./user-oq7iQk7S.js";import"./crm-api-Dd9UhSCJ.js";import"./header-stuEr_6l.js";import"./main-Dj7NWzIf.js";import"./search-context-DLufo9i0.js";import"./date-range-picker-CVvofQC0.js";import"./form-wT1R35uI.js";import{C as p}from"./create-table-form-DdIhsDbV.js";import"./separator-CClVRZ9M.js";import"./command-ByfqjQDn.js";import"./calendar-CzR6WBaB.js";import"./createLucideIcon-CNa_hh6B.js";import"./index-BT7Z3RDV.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-hQ-PVOWr.js";import"./search-BVQVKwPC.js";import"./createReactComponent-BD5R5KSl.js";import"./scroll-area-BeVbW7LP.js";import"./index-Bl1CGAiZ.js";import"./select-Czd7KcZQ.js";import"./index-C2T2k_Lh.js";import"./check-apx2eTVC.js";import"./IconChevronRight-BM-o6vT_.js";import"./chevron-right-sZt3EK3r.js";import"./react-icons.esm-B7rNr9e-.js";import"./popover-C2mvzdeD.js";import"./use-areas-DwZKIlN8.js";import"./useQuery-DSrD7NAp.js";import"./utils-km2FGkQ4.js";import"./useMutation-d67-fNFq.js";import"./images-api-ij8RVLRT.js";import"./query-keys-3lmd-xp6.js";import"./use-sales-channels-B5hwCj0P.js";import"./use-tables-DZtz_Itl.js";import"./input-CiKEYbig.js";import"./checkbox-BiVztVsP.js";import"./collapsible-CfXqqCTe.js";import"./use-items-in-store-data-CtPl2AOB.js";import"./use-item-types-Q-0SzpLA.js";import"./use-item-classes-CsFb8pem.js";import"./use-units-DeWvOXUG.js";import"./use-removed-items-niCKPB6t.js";import"./items-in-store-api-BIg85alI.js";import"./xlsx-DkH2s96g.js";import"./copy-Bu1_Kjt5.js";import"./plus-CGatRjL7.js";import"./minus-DkyqE5xV.js";const mo=function(){const{store_uid:o,area_uid:t,tableLayout:r}=m.useSearch();return i.jsx(p,{storeUid:o,areaId:t,fromTableLayout:r})};export{mo as component};
