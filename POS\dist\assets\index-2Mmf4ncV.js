import{r as h,h as be,j as e,B as y,c as _e,T as Oe,o as He,p as Ae,q as Ue,R as ye,a4 as V,u as Ke,l as Ee}from"./index-C21OP4ex.js";import"./pos-api-D5WM5mnz.js";import"./vietqr-api-ruJT0-tj.js";import{u as Le}from"./use-customizations-ZELiZ0he.js";import"./user-CgNoQQSj.js";import"./crm-api-BUMUQ8t4.js";import{H as qe}from"./header-DNPEfjkR.js";import{M as $e}from"./main-DRnqW_wu.js";import{P as Xe}from"./profile-dropdown-DlMxjxHH.js";import{S as Ge,T as We}from"./search-5KGATEvM.js";import{u as ee,a as Je,b as Qe,c as Ye,d as Ze,g as es,e as ss,I as ts,f as as,h as is,i as ls,C as ns,B as cs}from"./customization-dialog-BcXdEAE1.js";import"./exceljs.min-BFFGgdR1.js";import{h as rs,x as me,G as he,H as os,J as ds,K as ms,i as hs,A as xs,a as us,C as ps,B as fs}from"./react-icons.esm-DB-kGUq7.js";import{D as gs,a as js,b as _s,c as $}from"./dropdown-menu-DINyPmco.js";import{D as O}from"./data-table-column-header-7_8s6zRv.js";import{B as we}from"./badge-gtDUxDTX.js";import{S as ys}from"./status-badge-DUaAKf8K.js";import"./date-range-picker-B1pgj5D_.js";import"./form-usWdQ_Nt.js";import{C as ve}from"./checkbox-DUpnJ1Rx.js";import{S as Q}from"./settings-B2dEoYrB.js";import{I as ws}from"./IconCopy-Bg3cADqD.js";import{I as vs}from"./IconTrash-Cb-q_mwO.js";import{u as Ce,g as Ns,a as bs,b as Cs,d as ks,e as ke,f as Y}from"./index-B_FCwlUM.js";import{S as Se,a as de}from"./scroll-area-DKiYF9x5.js";import{T as xe,a as ue,b as A,c as Z,d as pe,e as X}from"./table-BIu4Pah2.js";import{C as Ie}from"./confirm-dialog-AXI5ANMc.js";import{a as Ss,C as Is}from"./chevron-right-BAjIoZMb.js";import{u as fe}from"./use-item-types-DA0U4OWS.js";import{u as ge}from"./use-removed-items-Ck-YNHyo.js";import{I as Ts}from"./input-4sMIt001.js";import{S as le,a as ne,b as ce,c as re,d as W}from"./select-B8Pw9rS-.js";import{M as Ds}from"./multi-select-DQY8oleQ.js";import{T as Te}from"./trash-2-C_5rhUMO.js";import{I as Ms}from"./IconFilter-72UvVUhz.js";import{X as zs}from"./calendar-BiBi2kQF.js";import{S as d}from"./skeleton-xUyFo20h.js";import{read as De,utils as Me}from"./xlsx-DkH2s96g.js";import{u as ze}from"./use-item-classes-_IC65iw9.js";import{u as Re}from"./use-units-dM9GTfDw.js";import{D as se,a as te,b as ae,c as ie}from"./dialog-DXjwjGKV.js";import{C as oe}from"./combobox-DyrNt90A.js";import"./useQuery-BNGphiae.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bh5DVQPI.js";import"./query-keys-3lmd-xp6.js";import"./separator-ZoxOB1XH.js";import"./avatar-C98m2_SM.js";import"./search-context-DtMZc3QX.js";import"./command-BnLmWlRk.js";import"./search-DHRhj6_i.js";import"./createLucideIcon-CL0CQOA1.js";import"./createReactComponent-zh6rKAzG.js";import"./IconChevronRight-Bwbz4HuV.js";import"./IconSearch-CyZD7dtp.js";import"./use-dialog-state-BYP8UC3r.js";import"./modal-CFi1vCFt.js";import"./zod-B4gLZVLM.js";import"./index-Bh-UeytL.js";import"./index-Ct3V_iCU.js";import"./index-UJ-79IIJ.js";import"./check-DcHT8QEO.js";import"./popover-BtedB187.js";import"./isSameMonth-C8JQo-AN.js";import"./index-DuT2Ibxp.js";import"./alert-dialog-CzdUByb-.js";import"./circle-x-CqN3rxdF.js";import"./chevrons-up-down-ChTQDbCM.js";function Rs(){const[n,s]=h.useState(!1),[i,c]=h.useState(!1),[a,o]=h.useState(null),[m,f]=h.useState(!1),[g,M]=h.useState([]),[T,C]=h.useState("all"),[k,u]=h.useState("all"),[x,_]=h.useState([]),[S,w]=h.useState("all");return{isCustomizationDialogOpen:n,isBuffetItem:i,selectedMenuItem:a,isBuffetConfigModalOpen:m,selectedBuffetMenuItem:g,selectedItemTypeUid:T,selectedCityUid:k,selectedDaysOfWeek:x,selectedStatus:S,setIsCustomizationDialogOpen:s,setIsBuffetItem:c,setSelectedMenuItem:o,setIsBuffetConfigModalOpen:f,setSelectedBuffetMenuItem:M,setSelectedItemTypeUid:C,setSelectedCityUid:u,setSelectedDaysOfWeek:_,setSelectedStatus:w}}function Bs(){const{setOpen:n}=ee(),s=be(),i=()=>{s({to:"/menu/items/items-in-city/detail"})},c=()=>{n("export-dialog")},a=()=>{n("import")},o=()=>{},m=()=>{},f=()=>{},g=()=>{};return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(gs,{children:[e.jsx(js,{asChild:!0,children:e.jsxs(y,{variant:"outline",size:"sm",children:["Tiện ích",e.jsx(rs,{className:"ml-2 h-4 w-4"})]})}),e.jsxs(_s,{align:"end",className:"w-56",children:[e.jsxs($,{onClick:c,children:[e.jsx(me,{className:"mr-2 h-4 w-4"}),"Xuất, sửa thực đơn"]}),e.jsxs($,{onClick:a,children:[e.jsx(he,{className:"mr-2 h-4 w-4"}),"Thêm món từ file"]}),e.jsxs($,{onClick:o,children:[e.jsx(os,{className:"mr-2 h-4 w-4"}),"Cấu hình giá theo nguồn"]}),e.jsxs($,{onClick:m,children:[e.jsx(ds,{className:"mr-2 h-4 w-4"}),"Sắp xếp thực đơn"]}),e.jsxs($,{onClick:f,children:[e.jsx(ms,{className:"mr-2 h-4 w-4"}),"Sao chép thực đơn"]}),e.jsxs($,{onClick:g,children:[e.jsx(hs,{className:"mr-2 h-4 w-4"}),"Cấu hình khung thời gian"]})]})]}),e.jsx(y,{variant:"default",size:"sm",onClick:i,children:"Tạo món"})]})})}function Ne({column:n,title:s,className:i,defaultSort:c="desc"}){if(!n.getCanSort())return e.jsx("div",{className:_e(i),children:s});const a=()=>{const o=n.getIsSorted();o?o==="desc"?n.toggleSorting(!1):n.toggleSorting(!0):n.toggleSorting(c==="desc")};return e.jsx("div",{className:_e("flex items-center space-x-2",i),children:e.jsxs(y,{variant:"ghost",size:"sm",className:"-ml-3 h-8 hover:bg-accent",onClick:a,children:[e.jsx("span",{children:s}),n.getIsSorted()==="desc"?e.jsx(xs,{className:"ml-2 h-4 w-4"}):n.getIsSorted()==="asc"?e.jsx(us,{className:"ml-2 h-4 w-4"}):e.jsx(ps,{className:"ml-2 h-4 w-4"})]})})}const Fs=({onBuffetConfigClick:n})=>[{id:"select",header:({table:s})=>e.jsx(ve,{checked:s.getIsAllPageRowsSelected(),onCheckedChange:i=>s.toggleAllPageRowsSelected(!!i),"aria-label":"Select all"}),cell:({row:s})=>e.jsx(ve,{checked:s.getIsSelected(),onCheckedChange:i=>s.toggleSelected(!!i),"aria-label":"Select row",onClick:i=>i.stopPropagation()}),enableSorting:!1,enableHiding:!1,size:50},{id:"index",header:"#",cell:({row:s})=>e.jsx("div",{className:"w-[50px]",children:s.index+1}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"code",header:({column:s})=>e.jsx(O,{column:s,title:"Mã món"}),cell:({row:s})=>e.jsx("div",{className:"text-sm font-medium",children:s.getValue("code")}),enableSorting:!1,enableHiding:!0},{accessorKey:"name",header:({column:s})=>e.jsx(O,{column:s,title:"Tên món"}),cell:({row:s})=>e.jsx("div",{className:"max-w-[200px] truncate text-sm font-medium",children:s.getValue("name")}),enableSorting:!1,enableHiding:!0},{accessorKey:"price",header:({column:s})=>e.jsx(O,{column:s,title:"Giá"}),cell:({row:s})=>{const i=s.getValue("price");return e.jsx("div",{className:"text-sm font-medium",children:new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(i)})},enableSorting:!1,enableHiding:!0},{accessorKey:"vatPercent",header:({column:s})=>e.jsx(O,{column:s,title:"VAT (%)"}),cell:({row:s})=>{const i=s.getValue("vatPercent");return e.jsx("div",{className:"text-right text-sm",children:i*100})},enableSorting:!1,enableHiding:!0},{accessorKey:"itemType",header:({column:s})=>e.jsx(O,{column:s,title:"Nhóm món"}),cell:({row:s})=>e.jsx(we,{variant:"outline",className:"text-xs",children:s.getValue("itemType")}),enableSorting:!1,enableHiding:!0},{accessorKey:"itemClass",header:({column:s})=>e.jsx(O,{column:s,title:"Loại món"}),cell:({row:s})=>s.getValue("itemClass")&&e.jsx(we,{variant:"outline",className:"text-center text-xs",children:s.getValue("itemClass")}),enableSorting:!1,enableHiding:!0},{accessorKey:"unit",header:({column:s})=>e.jsx(O,{column:s,title:"Đơn vị tính"}),cell:({row:s})=>e.jsx("div",{className:"text-sm",children:s.getValue("unit")}),enableSorting:!1,enableHiding:!0},{accessorKey:"sideItems",header:({column:s})=>e.jsx(Ne,{column:s,title:"Món ăn kèm",defaultSort:"desc"}),cell:({row:s})=>{const i=s.getValue("sideItems");if(!i)return e.jsx("div",{children:"Món chính"});const c=i==="Món ăn kèm"?"Món ăn kèm":i;return e.jsx(Oe,{children:e.jsxs(He,{children:[e.jsx(Ae,{asChild:!0,children:e.jsx("div",{className:"max-w-[120px] cursor-help truncate text-sm",children:c})}),e.jsx(Ue,{children:e.jsx("p",{className:"max-w-[300px]",children:c})})]})})},enableSorting:!0,enableHiding:!0},{accessorKey:"city",header:({column:s})=>e.jsx(O,{column:s,title:"Thành phố"}),cell:({row:s})=>e.jsx("div",{className:"text-sm",children:s.getValue("city")}),enableSorting:!1,enableHiding:!0},{accessorKey:"buffetConfig",header:({column:s})=>e.jsx(O,{column:s,title:"Cấu hình buffet"}),cell:({row:s})=>{var a;const i=s.original;return((a=i.extra_data)==null?void 0:a.is_buffet_item)===1?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:"Đã cấu hình"}),e.jsx(y,{variant:"outline",size:"sm",onClick:()=>n(i),className:"h-6 px-2 text-xs",children:e.jsx(Q,{className:"h-3 w-3"})})]}):e.jsxs(y,{variant:"outline",size:"sm",onClick:()=>n(i),className:"h-7 px-2 text-xs",children:[e.jsx(Q,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{accessorKey:"customization",header:({column:s})=>e.jsx(O,{column:s,title:"Customization"}),cell:({row:s,table:i})=>{var f;const c=s.original,a=i.options.meta,o=c.customization_uid,m=(f=a==null?void 0:a.customizations)==null?void 0:f.find(g=>g.id===o);return m?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:m.name}),e.jsx(y,{variant:"outline",size:"sm",onClick:()=>{var g;return(g=a==null?void 0:a.onCustomizationClick)==null?void 0:g.call(a,c)},className:"h-6 px-2 text-xs",children:e.jsx(Q,{className:"h-3 w-3"})})]}):e.jsxs(y,{variant:"outline",size:"sm",onClick:()=>{var g;return(g=a==null?void 0:a.onCustomizationClick)==null?void 0:g.call(a,c)},className:"h-7 px-2 text-xs",children:[e.jsx(Q,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{id:"copy",header:"Sao chép tạo món mới",cell:({row:s,table:i})=>{const c=s.original,a=i.options.meta;return e.jsxs(y,{variant:"ghost",size:"sm",className:"ml-14 h-8 w-8",onClick:o=>{var m;o.stopPropagation(),(m=a==null?void 0:a.onCopyClick)==null||m.call(a,c)},children:[e.jsx(ws,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Sao chép thiết bị ",c.item_name]})]})},enableSorting:!1,enableHiding:!0},{accessorKey:"isActive",header:({column:s})=>e.jsx(Ne,{column:s,title:"Thao tác",defaultSort:"desc"}),enableSorting:!0,cell:({row:s,table:i})=>{const c=s.original,a=s.getValue("isActive"),o=i.options.meta;return e.jsx("div",{onClick:m=>{var f;m.stopPropagation(),(f=o==null?void 0:o.onToggleStatus)==null||f.call(o,c)},className:"cursor-pointer",children:e.jsx(ys,{isActive:a,activeText:"Active",inactiveText:"Deactive"})})},enableHiding:!0},{id:"actions",cell:({row:s,table:i})=>{const c=s.original,a=i.options.meta;return e.jsx("div",{className:"flex items-center justify-center",children:e.jsxs(y,{variant:"ghost",size:"sm",onClick:o=>{var m;o.stopPropagation(),(m=a==null?void 0:a.onDeleteClick)==null||m.call(a,c)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:[e.jsx(vs,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Xóa món ",c.item_name]})]})})},enableSorting:!1,enableHiding:!1,size:80}],Ps=Fs;function Vs({currentPage:n,onPageChange:s,hasNextPage:i}){const c=()=>{n>1&&s(n-1)},a=()=>{i&&s(n+1)};return e.jsxs("div",{className:"flex items-center justify-center gap-4 py-4",children:[e.jsxs(y,{variant:"outline",size:"sm",onClick:c,disabled:n===1,className:"flex items-center gap-2",children:[e.jsx(Ss,{className:"h-4 w-4"}),"Trước"]}),e.jsx("span",{className:"text-sm font-medium",children:n}),e.jsxs(y,{variant:"outline",size:"sm",onClick:a,disabled:!i,className:"flex items-center gap-2",children:["Sau",e.jsx(Is,{className:"h-4 w-4"})]})]})}const Os=[{label:"Thứ 2",value:"2"},{label:"Thứ 3",value:"3"},{label:"Thứ 4",value:"4"},{label:"Thứ 5",value:"5"},{label:"Thứ 6",value:"6"},{label:"Thứ 7",value:"7"},{label:"Chủ Nhật",value:"1"}],Hs=[{label:"Tất cả trạng thái",value:"all"},{label:"Active",value:"1"},{label:"Deactive",value:"0"}];function As({table:n,selectedItemTypeUid:s="all",onItemTypeChange:i,selectedCityUid:c="all",onCityChange:a,selectedDaysOfWeek:o=[],onDaysOfWeekChange:m,selectedStatus:f="all",onStatusChange:g,onDeleteSelected:M}){var r;const[T,C]=h.useState(!1),{data:k=[]}=fe(),{data:u=[]}=ge(),x=u.filter(t=>t.active===1),_=x.map(t=>({label:t.city_name,value:t.id})),S=x.map(t=>t.id).join(",");h.useEffect(()=>{c==="all"&&S&&a&&a(S)},[c,S,a]);const w=n.getState().columnFilters.length>0,v=n.getFilteredSelectedRowModel().rows.length;return e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[v>0&&e.jsxs(y,{variant:"destructive",size:"sm",onClick:M,className:"h-9",children:[e.jsx(Te,{}),"Xóa món (",v,")"]}),e.jsx(Ts,{placeholder:"Tìm kiếm món ăn...",value:((r=n.getColumn("name"))==null?void 0:r.getFilterValue())??"",onChange:t=>{var l;return(l=n.getColumn("name"))==null?void 0:l.setFilterValue(t.target.value)},className:"h-9 w-[150px] lg:w-[250px]"}),e.jsxs(le,{value:c,onValueChange:t=>{a&&a(t)},children:[e.jsx(ne,{className:"h-10 w-[180px]",children:e.jsx(ce,{placeholder:"Chọn thành phố"})}),e.jsxs(re,{children:[e.jsx(W,{value:S,children:"Tất cả thành phố"}),_.map(t=>e.jsx(W,{value:t.value,children:t.label},t.value))]})]}),e.jsxs(le,{value:f,onValueChange:g,children:[e.jsx(ne,{className:"h-10 w-[180px]",children:e.jsx(ce,{placeholder:"Chọn Trạng thái"})}),e.jsx(re,{children:Hs.map(t=>e.jsx(W,{value:t.value,children:t.label},t.value))})]}),e.jsxs(y,{variant:"outline",size:"sm",onClick:()=>C(!T),className:"h-9",children:[e.jsx(Ms,{className:"h-4 w-4"}),"Nâng cao"]}),w&&e.jsxs(y,{variant:"ghost",onClick:()=>n.resetColumnFilters(),className:"h-10 px-2 lg:px-3",children:["Reset",e.jsx(zs,{className:"ml-2 h-4 w-4"})]})]})}),T&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(le,{value:s,onValueChange:i,children:[e.jsx(ne,{className:"h-10 w-[180px]",children:e.jsx(ce,{placeholder:"Chọn loại món"})}),e.jsxs(re,{children:[e.jsx(W,{value:"all",children:"Tất cả nhóm món"}),k.filter(t=>t.active===1).map(t=>({label:t.item_type_name,value:t.id})).map(t=>e.jsx(W,{value:t.value,children:t.label},t.value))]})]}),e.jsx(Ds,{options:Os,value:o,onValueChange:m||(()=>{}),placeholder:"Chọn ngày trong tuần",className:"min-h-9 w-[300px]",maxCount:1})]})]})}function Us({columns:n,data:s,onCustomizationClick:i,onCopyClick:c,onToggleStatus:a,onRowClick:o,onDeleteClick:m,customizations:f,selectedItemTypeUid:g,onItemTypeChange:M,selectedCityUid:T,onCityChange:C,selectedDaysOfWeek:k,onDaysOfWeekChange:u,selectedStatus:x,onStatusChange:_,hasNextPageOverride:S,currentPage:w,onPageChange:v}){var L;const[r,t]=h.useState({}),[l,p]=h.useState({}),[z,H]=h.useState([]),[j,B]=h.useState([]),[N,R]=h.useState(!1),{deleteMultipleItemsAsync:G}=Je(),U=()=>{R(!0)},K=async()=>{try{const I=F.getFilteredSelectedRowModel().rows.map(P=>P.original.id);await G(I),R(!1),F.resetRowSelection()}catch{}},E=(D,I)=>{const P=I.target;P.closest('input[type="checkbox"]')||P.closest("button")||P.closest('[role="button"]')||P.closest(".badge")||P.tagName==="BUTTON"||o==null||o(D)},F=Ce({data:s,columns:n,state:{sorting:j,columnVisibility:l,rowSelection:r,columnFilters:z},enableRowSelection:!0,onRowSelectionChange:t,onSortingChange:B,onColumnFiltersChange:H,onColumnVisibilityChange:p,getCoreRowModel:ke(),getFilteredRowModel:ks(),getSortedRowModel:Cs(),getFacetedRowModel:bs(),getFacetedUniqueValues:Ns(),meta:{onCustomizationClick:i,onCopyClick:c,onToggleStatus:a,onDeleteClick:m,customizations:f}});return e.jsxs("div",{className:"space-y-4",children:[e.jsx(As,{table:F,selectedItemTypeUid:g,onItemTypeChange:M,selectedCityUid:T,onCityChange:C,selectedDaysOfWeek:k,onDaysOfWeekChange:u,selectedStatus:x,onStatusChange:_,onDeleteSelected:U}),e.jsxs(Se,{className:"rounded-md border",children:[e.jsxs(xe,{className:"relative",children:[e.jsx(ue,{children:F.getHeaderGroups().map(D=>e.jsx(A,{children:D.headers.map(I=>e.jsx(Z,{colSpan:I.colSpan,children:I.isPlaceholder?null:Y(I.column.columnDef.header,I.getContext())},I.id))},D.id))}),e.jsx(pe,{children:(L=F.getRowModel().rows)!=null&&L.length?F.getRowModel().rows.map(D=>e.jsx(A,{"data-state":D.getIsSelected()&&"selected",className:"hover:bg-muted/50 cursor-pointer",onClick:I=>E(D.original,I),children:D.getVisibleCells().map(I=>e.jsx(X,{children:Y(I.column.columnDef.cell,I.getContext())},I.id))},D.id)):e.jsx(A,{children:e.jsx(X,{colSpan:n.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]}),e.jsx(de,{orientation:"horizontal"})]}),e.jsx(Vs,{currentPage:w??1,onPageChange:D=>v&&v(D),hasNextPage:!!S}),e.jsx(Ie,{open:N,onOpenChange:R,title:`Bạn có chắc muốn xóa ${F.getFilteredSelectedRowModel().rows.length} món đã chọn`,desc:"Hành động này không thể hoàn tác.",confirmText:"Xóa",cancelBtnText:"Hủy",className:"top-[30%] translate-y-[-50%]",handleConfirm:K,destructive:!0})]})}function Ks(){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(d,{className:"h-8 w-[250px]"}),e.jsx(d,{className:"h-8 w-[100px]"}),e.jsx(d,{className:"h-8 w-[100px]"}),e.jsx(d,{className:"h-8 w-[100px]"})]}),e.jsx(d,{className:"h-8 w-[100px]"})]}),e.jsxs("div",{className:"rounded-md border",children:[e.jsx("div",{className:"border-b p-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(d,{className:"h-4 w-8"}),e.jsx(d,{className:"h-4 w-20"}),e.jsx(d,{className:"h-4 w-32"}),e.jsx(d,{className:"h-4 w-20"}),e.jsx(d,{className:"h-4 w-16"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-20"}),e.jsx(d,{className:"h-4 w-16"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-20"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-16"})]})}),Array.from({length:10}).map((n,s)=>e.jsx("div",{className:"border-b p-4 last:border-b-0",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(d,{className:"h-4 w-8"}),e.jsx(d,{className:"h-4 w-20"}),e.jsx(d,{className:"h-4 w-32"}),e.jsx(d,{className:"h-4 w-20"}),e.jsx(d,{className:"h-4 w-16"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-20"}),e.jsx(d,{className:"h-4 w-16"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-20"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-24"}),e.jsx(d,{className:"h-4 w-16"})]})},s))]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(d,{className:"h-8 w-[200px]"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(d,{className:"h-8 w-[100px]"}),e.jsx(d,{className:"h-8 w-8"}),e.jsx(d,{className:"h-8 w-8"})]})]})]})}function Es({open:n,onOpenChange:s,data:i,onSave:c}){var k;const[a,o]=h.useState(i);ye.useEffect(()=>{o(i)},[i]);const m=()=>{c(a),V.success("Data saved successfully"),s(!1)},f=()=>{s(!1)},g=ye.useCallback(u=>{const x=a.filter((_,S)=>S!==u);o(x)},[a]),{tableData:M,columns:T}=h.useMemo(()=>{if(!a||a.length===0)return{tableData:[],columns:[]};const u=a[0]||[],x=a.slice(1),_=[{id:"actions",header:"-",cell:({row:w})=>e.jsx(y,{variant:"ghost",size:"sm",onClick:()=>g(w.original._originalIndex),className:"h-6 w-6 p-0 text-red-500 hover:text-red-700",children:e.jsx(fs,{className:"h-4 w-4"})}),enableSorting:!1,enableHiding:!1,size:50,meta:{className:"w-12 text-center sticky left-0 bg-background z-20 border-r"}},...u.map((w,v)=>({id:`col_${v}`,accessorKey:`col_${v}`,header:String(w),cell:({row:r})=>e.jsx("div",{className:"min-w-[150px] whitespace-nowrap",children:r.getValue(`col_${v}`)}),enableSorting:!1,enableHiding:!1,meta:{className:"min-w-[150px] px-4 whitespace-nowrap"}}))];return{tableData:x.map((w,v)=>{const r={_originalIndex:v+1};return w.forEach((t,l)=>{r[`col_${l}`]=t}),r}),columns:_}},[a,g]),C=Ce({data:M,columns:T,getCoreRowModel:ke()});return!a||a.length===0?null:e.jsx(se,{open:n,onOpenChange:s,children:e.jsx(te,{className:"h-[525px] w-[1140px] !max-w-[1140px] overflow-hidden p-0",children:e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsx(ae,{className:"shrink-0 border-b px-6 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(ie,{className:"text-xl font-semibold",children:"Thêm món từ file"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(y,{variant:"outline",size:"sm",onClick:f,children:"Đóng"}),e.jsx(y,{size:"sm",className:"bg-green-600 hover:bg-green-700",onClick:m,children:"Lưu"})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden p-6",children:e.jsx("div",{className:"bg-background h-full w-full overflow-auto rounded-lg border",children:e.jsx("div",{className:"min-w-max",children:e.jsxs(xe,{children:[e.jsx(ue,{className:"bg-muted/50 sticky top-0 z-10",children:C.getHeaderGroups().map(u=>e.jsx(A,{children:u.headers.map(x=>{var _;return e.jsx(Z,{className:((_=x.column.columnDef.meta)==null?void 0:_.className)||"",children:x.isPlaceholder?null:Y(x.column.columnDef.header,x.getContext())},x.id)})},u.id))}),e.jsx(pe,{children:(k=C.getRowModel().rows)!=null&&k.length?C.getRowModel().rows.map(u=>e.jsx(A,{className:"hover:bg-muted/50",children:u.getVisibleCells().map(x=>{var _;return e.jsx(X,{className:((_=x.column.columnDef.meta)==null?void 0:_.className)||"",children:Y(x.column.columnDef.cell,x.getContext())},x.id)})},u.id)):e.jsx(A,{children:e.jsx(X,{colSpan:T.length,className:"h-24 text-center",children:"No data."})})})]})})})})]})})})}function Ls(){const{open:n,setOpen:s}=ee(),[i,c]=h.useState(!1),[a,o]=h.useState([]),m=h.useRef(null),{downloadImportTemplateAsync:f,isPending:g}=Qe(),M=async()=>{try{await f({})}catch{V.error("Lỗi khi tải template")}},T=()=>{var u;(u=m.current)==null||u.click()},C=u=>{var S;const x=(S=u.target.files)==null?void 0:S[0];if(!x)return;const _=new FileReader;_.onload=w=>{var v;try{const r=new Uint8Array((v=w.target)==null?void 0:v.result),t=De(r,{type:"array"}),l=t.SheetNames[0],p=t.Sheets[l],z=Me.sheet_to_json(p,{header:1,defval:"",raw:!1});if(z.length===0){V.error("File không có dữ liệu");return}o(z),s(null),c(!0),m.current&&(m.current.value="")}catch{V.error("Lỗi khi đọc file. Vui lòng kiểm tra định dạng file.")}},_.readAsArrayBuffer(x)},k=()=>{V.success("Dữ liệu đã được lưu thành công!"),c(!1),s(null)};return e.jsxs(e.Fragment,{children:[e.jsx(se,{open:n==="import",onOpenChange:u=>s(u?"import":null),children:e.jsxs(te,{className:"max-w-2xl",children:[e.jsx(ae,{children:e.jsx(ie,{children:"Thêm món"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Tải file mẫu"}),e.jsx(y,{variant:"outline",size:"sm",onClick:M,disabled:g,className:"flex items-center gap-2",children:g?"Đang tải...":e.jsxs(e.Fragment,{children:["Tải xuống",e.jsx(me,{className:"h-4 w-4"})]})})]})}),e.jsxs("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Thêm món vào file"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("p",{children:["Không được để trống các cột ",e.jsx("span",{className:"font-mono text-blue-600",children:"Tên, Thành phố"}),"."]}),e.jsxs("p",{children:["Các cột còn lại có thể để trống, để gán nhóm, loại, đơn vị cho món: Nhập mã nhóm, mã loại, mã đơn vị đã có vào cột ",e.jsx("span",{className:"font-mono text-blue-600",children:"Nhóm, Loại món"}),"."]}),e.jsxs("p",{children:["Mã đơn vị món, mã thành phố có thể xem trong sheet"," ",e.jsx("span",{className:"font-mono text-blue-600",children:"Guide"})," của file mẫu."]})]})]}),e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Tải file thực đơn lên"}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Sau khi đã điền đầy đủ thực đơn bạn có thể tải file lên"})]}),e.jsxs(y,{variant:"outline",size:"sm",onClick:T,className:"flex items-center gap-2",children:["Tải file lên",e.jsx(he,{className:"h-4 w-4"})]})]})})]})]})}),e.jsx("input",{ref:m,type:"file",accept:".xlsx,.xls",onChange:C,style:{display:"none"}}),e.jsx(Es,{open:i,onOpenChange:c,data:a,onSave:k})]})}function qs({open:n,onOpenChange:s,data:i}){const[c,a]=h.useState(i),[o,m]=h.useState(!1),{user:f,company:g}=Ke(r=>r.auth),{selectedBrand:M}=Ee(),{importItemsAsync:T,isPending:C}=Ye(),{data:k=[]}=fe({skip_limit:!0}),{data:u=[]}=ze({skip_limit:!0}),{data:x=[]}=Re(),{data:_=[]}=ge();h.useEffect(()=>{a(i)},[i]);const S=r=>{a(t=>t.filter((l,p)=>p!==r))},w=async()=>{if(!g||!M){V.error("Thiếu thông tin cần thiết để cập nhật");return}m(!0);const r=c.map(t=>{const l=x.find(N=>N.unit_id===t.unit_id),p=_.find(N=>N.city_name===t.city_name),z=k.find(N=>N.item_type_id===t.item_type_id||N.item_type_name===t.item_type_name),H=u.find(N=>N.item_class_id===t.item_class_id||N.item_class_name===t.item_class_name),j=x.find(N=>N.unit_id==="MON"),B=k.find(N=>N.item_type_name==="LOẠI KHÁC");return{id:t.id,item_id:t.item_id,item_name:t.item_name,description:t.description||"",ots_price:t.ots_price||0,ots_tax:(t.ots_tax||0)/100,ta_price:t.ots_price||0,ta_tax:(t.ots_tax||0)/100,time_sale_hour_day:Number(t.time_sale_hour_day??0),time_sale_date_week:Number(t.time_sale_date_week??0),allow_take_away:1,is_eat_with:t.is_eat_with||0,image_path:t.image_path||"",image_path_thumb:t.image_path?`${t.image_path}?width=185`:"",item_color:"",list_order:t.list_order||0,is_service:0,is_material:0,active:t.active||1,user_id:"",is_foreign:0,quantity_default:0,price_change:t.price_change||0,currency_type_id:"",point:0,is_gift:0,is_fc:0,show_on_web:0,show_price_on_web:0,cost_price:0,is_print_label:0,quantity_limit:0,is_kit:0,time_cooking:t.time_cooking||0,item_id_barcode:t.item_id_barcode||"",process_index:0,is_allow_discount:0,quantity_per_day:0,item_id_eat_with:"",is_parent:0,is_sub:0,item_id_mapping:String(t.sku||""),effective_date:0,expire_date:0,sort:t.list_order||1,extra_data:{formula_qrcode:t.inqr_formula||"",is_buffet_item:t.is_buffet_item||0,up_size_buffet:[],is_item_service:t.is_item_service||0,is_virtual_item:t.is_virtual_item||0,price_by_source:[],enable_edit_price:t.price_change||0,exclude_items_buffet:[],no_update_quantity_toping:t.no_update_quantity_toping||0},revision:0,unit_uid:(l==null?void 0:l.id)||(j==null?void 0:j.id)||"",unit_secondary_uid:null,item_type_uid:(z==null?void 0:z.id)||(B==null?void 0:B.id)||"",item_class_uid:(H==null?void 0:H.id)||void 0,source_uid:null,brand_uid:M.id,company_uid:g.id,customization_uid:"",is_fabi:1,deleted:!1,created_by:(f==null?void 0:f.email)||"",updated_by:(f==null?void 0:f.email)||"",deleted_by:null,created_at:Math.floor(Date.now()/1e3),updated_at:Math.floor(Date.now()/1e3),deleted_at:null,apply_with_store:2,cities:p?[{id:p.id,city_id:p.city_id||"",fb_city_id:p.fb_city_id||"",city_name:p.city_name,image_path:p.image_path,description:p.description||"",active:p.active||1,extra_data:p.extra_data,revision:p.revision||0,sort:p.sort||0,created_by:p.created_by,updated_by:p.updated_by,deleted_by:p.deleted_by,created_at:p.created_at||0,updated_at:p.updated_at||0,deleted_at:p.deleted_at,items_cities:{item_uid:t.id,city_uid:p.id}}]:[],status_trigger_disabled:!1}});try{await T(r),m(!1),s(!1)}catch(t){console.error("Error updating items:",t),V.error(`Có lỗi xảy ra khi cập nhật món ăn: ${t}`),m(!1)}},v=[{key:"item_id",label:"Mã món",width:"120px"},{key:"city_name",label:"Thành phố",width:"120px"},{key:"item_name",label:"Tên",width:"200px"},{key:"ots_price",label:"Giá",width:"100px"},{key:"active",label:"Trạng thái",width:"100px"},{key:"item_id_barcode",label:"Mã barcode",width:"120px"},{key:"is_eat_with",label:"Món ăn kèm",width:"120px"},{key:"no_update_quantity_toping",label:"Không cập nhật số lượng",width:"180px"},{key:"unit_name",label:"Đơn vị",width:"100px"},{key:"item_type_id",label:"Nhóm",width:"120px"},{key:"item_type_name",label:"Tên nhóm",width:"150px"},{key:"item_class_id",label:"Loại món",width:"120px"},{key:"item_class_name",label:"Tên loại",width:"150px"},{key:"description",label:"Mô tả",width:"200px"},{key:"sku",label:"SKU",width:"100px"},{key:"ots_tax",label:"VAT (%)",width:"80px"},{key:"time_cooking",label:"Thời gian chế biến (phút)",width:"180px"},{key:"price_change",label:"Cho phép sửa giá khi bán",width:"180px"},{key:"is_virtual_item",label:"Cấu hình món ảo",width:"150px"},{key:"is_item_service",label:"Cấu hình món dịch vụ",width:"180px"},{key:"is_buffet_item",label:"Cấu hình món ăn là vé buffet",width:"200px"},{key:"time_sale_hour_day",label:"Giờ",width:"80px"},{key:"time_sale_date_week",label:"Ngày",width:"80px"},{key:"list_order",label:"Thứ tự",width:"80px"},{key:"image_path",label:"Hình ảnh",width:"120px"},{key:"inqr_formula",label:"Công thức inQR cho máy pha trà",width:"220px"}];return e.jsx(se,{open:n,onOpenChange:s,children:e.jsxs(te,{className:"max-h-[90vh] max-w-7xl sm:max-w-4xl",children:[e.jsx(ae,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(ie,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})}),e.jsxs("div",{className:"space-y-4 overflow-hidden",children:[e.jsxs(Se,{className:"h-[60vh] w-full rounded-md border",children:[e.jsxs(xe,{children:[e.jsx(ue,{className:"sticky top-0 z-10 bg-white",children:e.jsxs(A,{children:[e.jsx(Z,{className:"w-12"}),v.map(r=>e.jsx(Z,{style:{width:r.width},children:r.label},r.key))]})}),e.jsx(pe,{children:c.map((r,t)=>e.jsxs(A,{children:[e.jsx(X,{children:e.jsx(y,{variant:"ghost",size:"icon",onClick:()=>S(t),className:"h-8 w-8 text-red-500 hover:text-red-700",children:e.jsx(Te,{className:"h-4 w-4"})})}),v.map(l=>{var p;return e.jsxs(X,{style:{width:l.width},children:[l.key==="ots_price"&&e.jsxs("span",{className:"text-right",children:[(p=r[l.key])==null?void 0:p.toLocaleString("vi-VN")," ₫"]}),l.key==="active"&&e.jsx("span",{children:r[l.key]}),(l.key==="item_id"||l.key==="item_id_barcode")&&e.jsx("span",{className:"font-mono text-sm",children:r[l.key]}),l.key==="item_name"&&e.jsx("span",{className:"font-medium",children:r[l.key]}),(l.key==="is_eat_with"||l.key==="no_update_quantity_toping"||l.key==="price_change"||l.key==="is_virtual_item"||l.key==="is_item_service"||l.key==="is_buffet_item")&&e.jsx("span",{className:"text-center",children:r[l.key]}),l.key!=="ots_price"&&l.key!=="active"&&l.key!=="item_id"&&l.key!=="item_id_barcode"&&l.key!=="item_name"&&l.key!=="is_eat_with"&&l.key!=="no_update_quantity_toping"&&l.key!=="price_change"&&l.key!=="is_virtual_item"&&l.key!=="is_item_service"&&l.key!=="is_buffet_item"&&e.jsx("span",{children:r[l.key]||""})]},l.key)})]},t))})]}),e.jsx(de,{orientation:"horizontal"}),e.jsx(de,{orientation:"vertical"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx(y,{variant:"outline",onClick:()=>s(!1),children:"Đóng"}),e.jsx(y,{onClick:w,disabled:o||C,children:o||C?"Đang lưu...":"Lưu"})]})]})]})})}function $s({open:n,onOpenChange:s}){const[i,c]=h.useState("all"),[a,o]=h.useState("all"),[m,f]=h.useState("all"),[g,M]=h.useState([]),[T,C]=h.useState(!1),k=h.useRef(null),{data:u=[]}=fe(),{data:x=[]}=ze(),{data:_=[]}=Re(),{data:S=[]}=ge(),{fetchItemsDataAsync:w,isPending:v}=Ze(),r=[{label:"Tất cả nhóm món",value:"all"},...u.filter(j=>j.active===1).map(j=>({label:j.item_type_name,value:j.id}))],t=[{label:"Tất cả thành phố",value:"all"},...S.filter(j=>j.active===1).map(j=>({label:j.city_name,value:j.id}))],l=[{label:"Tất cả trạng thái",value:"all"},{label:"Active",value:"1"},{label:"Deactive",value:"0"}],p=async()=>{try{const j=await w({city_uid:i!=="all"?i:void 0,item_type_uid:a!=="all"?a:void 0,active:m!=="all"?m:void 0});await es({itemTypes:u,itemClasses:x,units:_},j),V.success("Tải file thành công!")}catch{V.error("Lỗi khi tải file")}},z=j=>{var R;const B=(R=j.target.files)==null?void 0:R[0];if(!B)return;const N=new FileReader;N.onload=G=>{var U;try{const K=new Uint8Array((U=G.target)==null?void 0:U.result),E=De(K,{type:"array"}),F=E.SheetNames[0],L=E.Sheets[F],D=Me.sheet_to_json(L,{header:1});if(D.length>0){const I=D,P=I[0]||[],q=I.slice(1).map((J,Be)=>{const je={id:`temp_${Be}`};return P.forEach((Fe,Pe)=>{const Ve=String(Fe).toLowerCase().replace(/\s+/g,"_");je[Ve]=J[Pe]||""}),je});M(q),C(!0),V.success("File uploaded successfully")}}catch{V.error("Error parsing file")}},N.readAsArrayBuffer(B)},H=()=>{var j;(j=k.current)==null||j.click()};return e.jsxs(e.Fragment,{children:[e.jsx(se,{open:n,onOpenChange:s,children:e.jsxs(te,{className:"max-w-2xl lg:max-w-xl",children:[e.jsx(ae,{children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(ie,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Chỉnh bộ lọc để xuất file"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(oe,{options:t,value:i,onValueChange:c,placeholder:"Tất cả thành phố",searchPlaceholder:"Tìm thành phố...",className:"flex-1"}),e.jsx(oe,{options:r,value:a,onValueChange:o,placeholder:"Tất cả nhóm món",searchPlaceholder:"Tìm nhóm món...",className:"flex-1"}),e.jsx(oe,{options:l,value:m,onValueChange:f,placeholder:"Tất cả trạng thái",searchPlaceholder:"Tìm trạng thái...",className:"flex-1"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Tải file dữ liệu"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Tải xuống"}),e.jsxs(y,{variant:"outline",size:"sm",onClick:p,disabled:v,className:"flex items-center gap-2",children:[e.jsx(me,{className:"h-4 w-4"}),v&&"Đang tải..."]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-muted-foreground text-sm",children:"Không sửa các cột :"}),e.jsx("p",{className:"font-mono text-sm text-blue-600",children:"ID, Mã món, Thành phố, Đơn vị, Tên nhóm, Tên loại."})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 4. Tải file lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),e.jsxs(y,{variant:"outline",size:"sm",onClick:H,className:"flex items-center gap-2",children:["Tải file lên",e.jsx(he,{className:"h-4 w-4"})]}),e.jsx("input",{ref:k,type:"file",accept:".xlsx,.xls",onChange:z,style:{display:"none"}})]})]})]})]})}),e.jsx(qs,{open:T,onOpenChange:C,data:g})]})}function Xs(){const{open:n,setOpen:s,currentRow:i,setCurrentRow:c}=ee(),{deleteItemAsync:a}=ss();return e.jsxs(e.Fragment,{children:[e.jsx($s,{open:n==="export-dialog",onOpenChange:()=>s(null)}),e.jsx(Ls,{}),i&&e.jsx(e.Fragment,{children:e.jsx(Ie,{destructive:!0,open:n==="delete",onOpenChange:o=>{o||(s(null),setTimeout(()=>{c(null)},500))},handleConfirm:async()=>{s(null),setTimeout(()=>{c(null)},500),await a(i.id||"")},className:"max-w-md",title:"Bạn có muốn xoá ?",desc:e.jsx(e.Fragment,{children:"Hành động không thể hoàn tác."}),confirmText:"Xoá"},"quantity-day-delete")})]})}function Gs(){const n=be(),[s,i]=h.useState(1),{setOpen:c,setCurrentRow:a}=ee(),{updateStatusAsync:o}=as(),{updateItemAsync:m}=is(),{isCustomizationDialogOpen:f,isBuffetItem:g,isBuffetConfigModalOpen:M,setIsCustomizationDialogOpen:T,setIsBuffetItem:C,selectedMenuItem:k,setSelectedMenuItem:u,setIsBuffetConfigModalOpen:x,selectedBuffetMenuItem:_,setSelectedBuffetMenuItem:S,selectedItemTypeUid:w,setSelectedItemTypeUid:v,selectedCityUid:r,setSelectedCityUid:t,selectedDaysOfWeek:l,setSelectedDaysOfWeek:p,selectedStatus:z,setSelectedStatus:H}=Rs(),j=h.useMemo(()=>({...w!=="all"&&{item_type_uid:w},...r!=="all"&&{city_uid:r},...l.length>0&&{time_sale_date_week:l.join(",")},...z!=="all"&&{active:parseInt(z,10)},page:s}),[w,r,l,z,s]);h.useEffect(()=>{i(1)},[w,r,l,z]);const{data:B=[],isLoading:N,error:R,hasNextPage:G}=ls({params:j}),{data:U=[]}=Le({skip_limit:!0,list_city_uid:r!=="all"?[r]:void 0}),K=b=>{u(b),T(!0)},E=b=>{var q,J;u(b),S(((q=b==null?void 0:b.extra_data)==null?void 0:q.exclude_items_buffet)||[]),C(((J=b==null?void 0:b.extra_data)==null?void 0:J.is_buffet_item)===1),x(!0)},F=b=>{n({to:"/menu/items/items-in-city/detail",search:{id:b.id||""}})},L=b=>{a(b),c("delete")},D=b=>{n({to:"/menu/items/items-in-city/detail/$id",params:{id:b.id||""}})},I=async b=>{const q=b.active?0:1;await o({id:b.id||"",active:q})},P=Ps({onBuffetConfigClick:E});return R?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-muted-foreground mb-2 text-sm",children:"Có lỗi xảy ra khi tải dữ liệu"}),e.jsx("p",{className:"text-muted-foreground text-xs",children:R&&`Món ăn: ${(R==null?void 0:R.message)||"Lỗi không xác định"}`})]})}):e.jsxs(e.Fragment,{children:[e.jsx(qe,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Ge,{}),e.jsx(We,{}),e.jsx(Xe,{})]})}),e.jsxs($e,{children:[e.jsxs("div",{className:"mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Món ăn tại thành phố"})}),e.jsx(Bs,{})]}),e.jsxs("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12",children:[N&&e.jsx(Ks,{}),!N&&e.jsx(Us,{columns:P,data:B,onCustomizationClick:K,onCopyClick:F,onToggleStatus:I,onRowClick:D,onDeleteClick:L,customizations:U,selectedItemTypeUid:w,onItemTypeChange:v,selectedCityUid:r,onCityChange:t,selectedDaysOfWeek:l,onDaysOfWeekChange:p,selectedStatus:z,onStatusChange:H,hasNextPageOverride:G,currentPage:s,onPageChange:i})]})]}),e.jsx(Xs,{}),f&&k&&e.jsx(ns,{open:f,onOpenChange:T,item:k,customizations:U}),M&&_&&e.jsx(cs,{itemsBuffet:_,open:M,onOpenChange:x,onItemsChange:async b=>{await m({...k,extra_data:{is_buffet_item:g?1:0,exclude_items_buffet:b}})},items:B,hide:!1,enable:g,onEnableChange:C})]})}function Ws(){return e.jsx(ts,{children:e.jsx(Gs,{})})}const da=Ws;export{da as component};
