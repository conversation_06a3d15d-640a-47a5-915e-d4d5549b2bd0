import{aA as xe,j as e,B as j,a as ge,r as y,l as pe,b as fe,c as G,T as je,o as X,p as J,q as Z,h as ye}from"./index-Bnt3OGV2.js";import{b as _e,c as le,d as oe,u as ve,e as L,f as Ne,h as Ce,i as Te,j as be,R as Se}from"./discount-form-context-CGo27PDF.js";import{D as De}from"./discount-toggle-button-ByRMDsJu.js";import{v as H}from"./date-range-picker-CVvofQC0.js";import{L as v}from"./form-wT1R35uI.js";import{X as we,C as B}from"./calendar-CzR6WBaB.js";import{I as ce}from"./input-CiKEYbig.js";import{S as re,a as de,b as me,c as ue,d as he,C as w}from"./select-Czd7KcZQ.js";import{T as ke,a as Pe,c as ee}from"./tabs-DqOllQAY.js";import{u as Y}from"./useQuery-DSrD7NAp.js";import{b as Q}from"./pos-api-BwpRFGce.js";import{Q as W}from"./query-keys-3lmd-xp6.js";import{P as Ae}from"./modal-B0J8RkN-.js";import{C as b}from"./checkbox-BiVztVsP.js";import{C as k,a as P,b as A}from"./collapsible-CfXqqCTe.js";import{C as E}from"./chevron-right-sZt3EK3r.js";import{P as se,a as te,b as ae}from"./popover-C2mvzdeD.js";import{C as ne}from"./calendar-BgsBunwq.js";import{f as I}from"./isSameMonth-C8JQo-AN.js";import{C as ie}from"./circle-help-vaGE5apo.js";import{j as T}from"./date-utils-DBbLjCz0.js";function Ee(){const{handleBack:a,handleSave:u}=_e(),{isEditMode:o,isLoading:m,isFormValid:l}=le(),c=xe({strict:!1}).id,{data:s}=oe(c||""),i=ve(),f=async()=>{if(!o||!s)return;const g={id:s.id,created_at:s.created_at,created_by:s.created_by,updated_at:s.updated_at,updated_by:s.updated_by,deleted:s.deleted||!1,deleted_at:s.deleted_at||null,deleted_by:s.deleted_by||null,ta_discount:s.ta_discount,ots_discount:s.ots_discount,is_all:s.is_all,is_type:s.is_type,is_item:s.is_item,type_id:s.type_id,item_id:s.item_id,discount_type:s.discount_type,from_date:s.from_date,to_date:s.to_date,time_sale_hour_day:s.time_sale_hour_day||0,time_sale_date_week:s.time_sale_date_week||0,description:s.description,extra_data:s.extra_data,active:s.active===1?0:1,revision:s.revision||null,promotion_uid:s.promotion_uid,brand_uid:s.brand_uid||"",company_uid:s.company_uid||"",sort:s.sort||1e3,store_uid:s.store_uid,discount_clone_id:s.discount_clone_id||null,source_uid:s.source_uid||"",promotion:s.promotion,source:s.source};await i.mutateAsync(g)};return e.jsxs("div",{className:"mb-8 flex items-center justify-between",children:[e.jsx(j,{variant:"ghost",size:"sm",onClick:a,className:"flex items-center",children:e.jsx(we,{className:"h-4 w-4"})}),e.jsxs("h1",{className:"text-3xl font-bold",children:[o&&"Chỉnh sửa CTKM",!o&&"Tạo CTKM"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[o&&s&&e.jsx(De,{isActive:s.active===1,onToggle:f,disabled:m||i.isPending}),e.jsxs(j,{type:"button",disabled:m||!l,className:"min-w-[100px]",onClick:u,children:[m&&o&&"Đang cập nhật...",m&&!o&&"Đang tạo...",!m&&o&&"Cập nhật",!m&&!o&&"Lưu"]})]})]})}function Fe({promotions:a,isLoading:u,disabled:o}){const{formData:m,updateFormData:l}=L(),r=c=>{if(c.startsWith("promotion-"))return;const s=a.find(i=>i.promotion_uid===c);console.log("🔥 Selected promotion:",s),l({promotionUid:c,promotionName:(s==null?void 0:s.promotion_name)||""})};return e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(v,{className:"min-w-[200px] text-sm font-medium",children:["Chương trình khuyến mãi ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(re,{value:m.promotionUid,onValueChange:r,disabled:o||!m.storeUid||u,children:[e.jsx(de,{className:"flex-1",children:e.jsx(me,{placeholder:m.storeUid?u?"Đang tải...":a.length===0?"Không có chương trình khuyến mãi":"Chọn chương trình khuyến mãi":"Chọn cửa hàng trước"})}),e.jsx(ue,{children:a.length===0?e.jsx("div",{className:"px-2 py-1.5 text-sm text-gray-500",children:m.storeUid?"Không có chương trình khuyến mãi":"Vui lòng chọn cửa hàng trước"}):a.filter(c=>c.promotion_uid).map(c=>e.jsx(he,{value:c.promotion_uid,children:c.promotion_name||"Chương trình không tên"},c.promotion_uid))})]})]})}function Re(){const{formData:a,updateFormData:u}=L(),{isEditMode:o}=le(),{promotions:m,isLoadingPromotions:l}=Ne(),{currentBrandStores:r}=ge();return e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Thông tin giảm giá"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(v,{className:"min-w-[200px] text-sm font-medium",children:["Cửa hàng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(re,{value:a.storeUid,onValueChange:c=>{u({storeUid:c,promotionUid:"",promotionName:""})},disabled:o,children:[e.jsx(de,{className:"flex-1",children:e.jsx(me,{placeholder:"Chọn cửa hàng"})}),e.jsx(ue,{children:r.map(c=>e.jsx(he,{value:c.id,children:c.store_name},c.id))})]})]}),e.jsx(Fe,{promotions:m,isLoading:l,disabled:l||!a.storeUid}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(v,{className:"min-w-[200px] text-sm font-medium",children:[a.discountType==="PERCENT"?"Phần trăm giảm giá":"Số tiền giảm giá"," ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"flex flex-1 gap-2",children:[e.jsx(ce,{type:"number",min:"0",max:a.discountType==="PERCENT"?"100":void 0,value:a.discountType==="PERCENT"?a.discountPercentage||"":a.discountAmount||"",onChange:c=>{const s=Number(c.target.value);if(a.discountType==="PERCENT"){const i=s>100?100:s;u({discountPercentage:i})}else u({discountAmount:s})},placeholder:"0",className:"flex-1"}),e.jsx(ke,{value:a.discountType,onValueChange:c=>u({discountType:c}),className:"w-auto",children:e.jsxs(Pe,{className:"grid w-fit grid-cols-2",children:[e.jsx(ee,{value:"PERCENT",children:"%"}),e.jsx(ee,{value:"AMOUNT",children:"đ"})]})})]})]})]})}function Le({itemTypes:a,selectedItems:u,searchTerm:o,onItemToggle:m}){const[l,r]=y.useState(!1),[c,s]=y.useState(!1),i=(Array.isArray(a)?a:[]).filter(n=>{const h=n.item_type_name||n.name||"";return h.toLowerCase()!=="uncategory"&&h.toLowerCase().includes(o.toLowerCase())}),f=i.filter(n=>u.includes(n.item_type_id||n.id)),g=i.filter(n=>!u.includes(n.item_type_id||n.id));return e.jsxs("div",{className:"space-y-4",children:[e.jsxs(k,{open:!l,onOpenChange:n=>r(!n),children:[e.jsx(P,{asChild:!0,children:e.jsxs(j,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",f.length,")"]}),l&&e.jsx(E,{className:"h-4 w-4"}),!l&&e.jsx(w,{className:"h-4 w-4"})]})}),e.jsxs(A,{className:"max-h-40 space-y-2 overflow-y-auto",children:[f.map(n=>e.jsxs("div",{className:`flex items-center space-x-2 p-2 ${n.active===0?"opacity-50":""}`,children:[e.jsx(b,{checked:!0,onCheckedChange:()=>m(n.item_type_id||n.id),disabled:n.active===0}),e.jsx("span",{className:"text-sm",children:n.item_type_name||n.name||"Không có tên"})]},n.id)),f.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn nhóm nào"})]})]}),e.jsxs(k,{open:!c,onOpenChange:n=>s(!n),children:[e.jsx(P,{asChild:!0,children:e.jsxs(j,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",g.length,")"]}),c&&e.jsx(E,{className:"h-4 w-4"}),!c&&e.jsx(w,{className:"h-4 w-4"})]})}),e.jsxs(A,{className:"max-h-40 space-y-2 overflow-y-auto",children:[g.map(n=>e.jsxs("div",{className:`flex items-center space-x-2 p-2 ${n.active===0?"opacity-50":""}`,children:[e.jsx(b,{checked:!1,onCheckedChange:()=>m(n.item_type_id||n.id),disabled:n.active===0}),e.jsx("span",{className:"text-sm",children:n.item_type_name||n.name||"Không có tên"})]},n.id)),g.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có nhóm nào"})]})]})]})}function Me({items:a,selectedItems:u,searchTerm:o,isLoading:m,onItemToggle:l}){const[r,c]=y.useState(!1),[s,i]=y.useState(!1),g=(Array.isArray(a)?a:[]).filter(t=>{const x=(t==null?void 0:t.item_name)||(t==null?void 0:t.name)||"";return t.active!==0&&x.toLowerCase().includes(o.toLowerCase())}).map(t=>({...t,name:t.item_name||t.name||"Không có tên"})),n=g.filter(t=>u.includes(t.item_id||t.id)),h=g.filter(t=>!u.includes(t.item_id||t.id));return m?e.jsx("div",{className:"py-4 text-center text-sm text-gray-500",children:"Đang tải..."}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs(k,{open:!r,onOpenChange:t=>c(!t),children:[e.jsx(P,{asChild:!0,children:e.jsxs(j,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",n.length,")"]}),r&&e.jsx(E,{className:"h-4 w-4"}),!r&&e.jsx(w,{className:"h-4 w-4"})]})}),e.jsxs(A,{className:"max-h-40 space-y-2 overflow-y-auto",children:[n.map(t=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(b,{checked:!0,onCheckedChange:()=>l(t.item_id||t.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:t.name||"Không có tên"})})]},t.id)),n.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn món ăn nào"})]})]}),e.jsxs(k,{open:!s,onOpenChange:t=>i(!t),children:[e.jsx(P,{asChild:!0,children:e.jsxs(j,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",h.length,")"]}),s&&e.jsx(E,{className:"h-4 w-4"}),!s&&e.jsx(w,{className:"h-4 w-4"})]})}),e.jsxs(A,{className:"max-h-40 space-y-2 overflow-y-auto",children:[h.map(t=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(b,{checked:!1,onCheckedChange:()=>l(t.item_id||t.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:t.name||"Không có tên"})})]},t.id)),h.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có món ăn nào"})]})]})]})}function Ke({packages:a,selectedItems:u,searchTerm:o,isLoading:m,onItemToggle:l}){const[r,c]=y.useState(!1),[s,i]=y.useState(!1),g=(Array.isArray(a)?a:[]).filter(t=>{const N=((t==null?void 0:t.package_name)||(t==null?void 0:t.name)||"").toLowerCase().includes(o.toLowerCase());return t.active!==0&&N}).map(t=>({...t,name:t.package_name||t.name||"Không có tên"})),n=g.filter(t=>u.includes(t.package_id||t.id)),h=g.filter(t=>!u.includes(t.package_id||t.id));return m?e.jsx("div",{className:"py-4 text-center text-sm text-gray-500",children:"Đang tải..."}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs(k,{open:!r,onOpenChange:t=>c(!t),children:[e.jsx(P,{asChild:!0,children:e.jsxs(j,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",n.length,")"]}),r&&e.jsx(E,{className:"h-4 w-4"}),!r&&e.jsx(w,{className:"h-4 w-4"})]})}),e.jsxs(A,{className:"max-h-40 space-y-2 overflow-y-auto",children:[n.map(t=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(b,{checked:!0,onCheckedChange:()=>l(t.package_id||t.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:t.name||"Không có tên"})})]},t.id)),n.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn combo nào"})]})]}),e.jsxs(k,{open:!s,onOpenChange:t=>i(!t),children:[e.jsx(P,{asChild:!0,children:e.jsxs(j,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",h.length,")"]}),s&&e.jsx(E,{className:"h-4 w-4"}),!s&&e.jsx(w,{className:"h-4 w-4"})]})}),e.jsxs(A,{className:"max-h-40 space-y-2 overflow-y-auto",children:[h.map(t=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(b,{checked:!1,onCheckedChange:()=>l(t.package_id||t.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:t.name||"Không có tên"})})]},t.id)),h.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có combo nào"})]})]})]})}function Ue({open:a,onOpenChange:u,storeUid:o,onSave:m,initialApplyToAll:l=!1,initialActiveFilter:r=null,initialSelectedItems:c=[]}){const{selectedBrand:s}=pe(),{company:i}=fe(),[f,g]=y.useState(""),[n,h]=y.useState(!1),[t,x]=y.useState(null),[N,F]=y.useState([]),{data:$=[],isLoading:O}=Y({queryKey:[W.ITEM_TYPES,i==null?void 0:i.id,s==null?void 0:s.id,o],queryFn:async()=>!(i!=null&&i.id)||!(s!=null&&s.id)||!o?[]:(await Q.get(`/mdata/v1/item-types?skip_limit=true&company_uid=${i.id}&brand_uid=${s.id}&store_uid=${o}`)).data.data||[],enabled:a&&!!(i!=null&&i.id)&&!!(s!=null&&s.id)&&!!o}),{data:M=[],isLoading:V}=Y({queryKey:[W.ITEMS,i==null?void 0:i.id,s==null?void 0:s.id,o],queryFn:async()=>!(i!=null&&i.id)||!(s!=null&&s.id)||!o?[]:(await Q.get(`/mdata/v1/items?skip_limit=true&company_uid=${i.id}&brand_uid=${s.id}&list_store_uid=${o}`)).data.data||[],enabled:a&&!!(i!=null&&i.id)&&!!(s!=null&&s.id)&&!!o}),{data:q=[],isLoading:S}=Y({queryKey:[W.PACKAGES,i==null?void 0:i.id,s==null?void 0:s.id,o],queryFn:async()=>!(i!=null&&i.id)||!(s!=null&&s.id)||!o?[]:(await Q.get(`/mdata/v1/packages?skip_limit=true&company_uid=${i.id}&brand_uid=${s.id}&store_uid=${o}`)).data.data||[],enabled:a&&!!(i!=null&&i.id)&&!!(s!=null&&s.id)&&!!o}),d=O||V||S;y.useEffect(()=>{a&&(g(""),h(l),x(r),F(c))},[a,l,r,c]);const _=p=>{h(p),p&&x(null)},D=p=>{h(!1),x(C=>{const U=C===p?null:p;return U!==C&&F([]),U})},R=p=>{F(C=>C.includes(p)?C.filter(U=>U!==p):[...C,p])},z=()=>{u(!1)},K=()=>{n?m(["all"],!0,null):m(N,!1,t),u(!1)};return e.jsx(Ae,{title:"Áp dụng cho",open:a,onOpenChange:u,onCancel:z,onConfirm:K,cancelText:"Hủy",confirmText:"Lưu",maxWidth:"sm:max-w-2xl",isLoading:d,confirmDisabled:!n&&N.length===0,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(b,{id:"apply-all",checked:n,onCheckedChange:_}),e.jsx(v,{htmlFor:"apply-all",className:"text-sm font-medium",children:"Áp dụng cho tất cả món và nhóm"})]}),e.jsx(ce,{placeholder:"Tìm kiếm",value:f,onChange:p=>g(p.target.value),className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(v,{className:"text-sm font-medium",children:"Áp dụng cho"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(j,{type:"button",variant:t==="groups"?"default":"outline",size:"sm",onClick:()=>D("groups"),children:"Nhóm"}),e.jsx(j,{type:"button",variant:t==="items"?"default":"outline",size:"sm",onClick:()=>D("items"),children:"Món ăn"}),e.jsx(j,{type:"button",variant:t==="packages"?"default":"outline",size:"sm",onClick:()=>D("packages"),children:"Combo"})]})]}),!n&&e.jsxs("div",{className:"space-y-2",children:[t==="groups"&&e.jsx(Le,{itemTypes:$,selectedItems:N,searchTerm:f,onItemToggle:R}),t==="items"&&e.jsx(Me,{items:M,selectedItems:N,searchTerm:f,isLoading:d,onItemToggle:R}),t==="packages"&&e.jsx(Ke,{packages:q,selectedItems:N,searchTerm:f,isLoading:d,onItemToggle:R})]})]})})}function He(){var g;const{formData:a,updateFormData:u}=L(),[o,m]=y.useState(!1),l=()=>{m(!0)},r=(n,h,t)=>{const x={...a.filterState,is_all:h?1:0,is_type:0,is_item:0,is_combo:0,type_id:"",item_id:"",combo_id:""};!h&&n.length>0&&t&&(t==="groups"?(x.is_type=1,x.type_id=n.join(",")):t==="items"?(x.is_item=1,x.item_id=n.join(",")):t==="packages"&&(x.is_combo=1,x.combo_id=n.join(","))),u({filterState:x})},c=((g=a.filterState)==null?void 0:g.is_all)===1,s=()=>{var n,h,t;return((n=a.filterState)==null?void 0:n.is_type)===1?"groups":((h=a.filterState)==null?void 0:h.is_item)===1?"items":((t=a.filterState)==null?void 0:t.is_combo)===1?"packages":null},i=()=>{var n,h,t;return((n=a.filterState)==null?void 0:n.is_type)===1&&a.filterState.type_id?a.filterState.type_id.split(",").filter(x=>x.trim()):((h=a.filterState)==null?void 0:h.is_item)===1&&a.filterState.item_id?a.filterState.item_id.split(",").filter(x=>x.trim()):((t=a.filterState)==null?void 0:t.is_combo)===1&&a.filterState.combo_id?a.filterState.combo_id.split(",").filter(x=>x.trim()):[]},f=()=>{if(c)return"Áp dụng cho tất cả";const n=i();if(n.length>0){const h=s(),t=h==="groups"?"nhóm":h==="items"?"món":"combo";return`${n.length} ${t}`}return"Thêm"};return e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Tuỳ chỉnh"}),e.jsx("div",{className:"mb-4 text-sm text-gray-600",children:"Áp dụng giảm giá tự động cho các món hoặc nhóm món, combo cụ thể"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(v,{className:"min-w-[200px] text-sm font-medium",children:["Áp dụng cho ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(j,{type:"button",variant:"outline",onClick:l,disabled:!a.storeUid,className:"flex-1 justify-start",children:f()})]}),e.jsx(Ue,{open:o,onOpenChange:m,storeUid:a.storeUid,onSave:r,initialApplyToAll:c,initialActiveFilter:s(),initialSelectedItems:i()})]})}function Ie(){const{formData:a,updateFormData:u}=L(),o=new Date;o.setHours(0,0,0,0);const m=a.startDate?new Date(a.startDate):void 0,l=a.endDate?new Date(a.endDate):void 0,r=s=>{if(s){const i=I(s,"yyyy-MM-dd");u({startDate:i})}},c=s=>{if(s){const i=I(s,"yyyy-MM-dd");u({endDate:i})}};return e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Ngày áp dụng"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex flex-1 items-center gap-4",children:[e.jsx(v,{className:"min-w-[120px] text-sm font-medium",children:"Ngày bắt đầu"}),e.jsxs(se,{children:[e.jsx(te,{asChild:!0,children:e.jsxs(j,{variant:"outline",className:G("flex-1 justify-start text-left font-normal",!m&&"text-muted-foreground"),children:[e.jsx(ne,{className:"mr-2 h-4 w-4"}),m?I(m,"dd/MM/yyyy",{locale:H}):"Chọn ngày bắt đầu"]})}),e.jsx(ae,{className:"w-auto p-0",align:"start",children:e.jsx(B,{mode:"single",selected:m,onSelect:r,disabled:s=>s>o,initialFocus:!0,locale:H})})]})]}),e.jsxs("div",{className:"flex flex-1 items-center gap-4",children:[e.jsx(v,{className:"min-w-[120px] text-sm font-medium",children:"Ngày kết thúc"}),e.jsxs(se,{children:[e.jsx(te,{asChild:!0,children:e.jsxs(j,{variant:"outline",className:G("flex-1 justify-start text-left font-normal",!l&&"text-muted-foreground"),children:[e.jsx(ne,{className:"mr-2 h-4 w-4"}),l?I(l,"dd/MM/yyyy",{locale:H}):"Chọn ngày kết thúc"]})}),e.jsx(ae,{className:"w-auto p-0",align:"start",children:e.jsx(B,{mode:"single",selected:l,onSelect:c,disabled:s=>s<o,initialFocus:!0,locale:H})})]})]})]})]})}const $e=[{label:"T2",value:"0"},{label:"T3",value:"1"},{label:"T4",value:"2"},{label:"T5",value:"3"},{label:"T6",value:"4"},{label:"T7",value:"5"},{label:"CN",value:"6"}],Oe=[{value:"0"},{value:"1"},{value:"2"},{value:"3"},{value:"4"},{value:"5"},{value:"6"},{value:"7"},{value:"8"},{value:"9"},{value:"10"},{value:"11"},{value:"12"},{value:"13"},{value:"14"},{value:"15"},{value:"16"},{value:"17"},{value:"18"},{value:"19"},{value:"20"},{value:"21"},{value:"22"},{value:"23"}];function Ve(){const{formData:a,updateFormData:u}=L(),o=l=>{const r=a.marketingDays||[],c=r.includes(l)?r.filter(s=>s!==l):[...r,l];u({marketingDays:c})},m=l=>{const r=a.marketingHours||[],c=r.includes(l)?r.filter(s=>s!==l):[...r,l];u({marketingHours:c})};return e.jsx(je,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Khung thời gian áp dụng"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(v,{className:"text-sm font-medium text-gray-500",children:"Chọn ngày"}),e.jsxs(X,{children:[e.jsx(J,{asChild:!0,children:e.jsx(ie,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(Z,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu để trống mặc định sẽ áp dụng tất cả các ngày trong tuần"})})]})]}),e.jsx("div",{className:"flex gap-2",children:$e.map(l=>{var r;return e.jsx(j,{type:"button",variant:(r=a.marketingDays)!=null&&r.includes(l.value)?"default":"outline",size:"sm",onClick:()=>o(l.value),className:"flex-1",children:l.label},l.value)})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(v,{className:"text-sm font-medium text-gray-500",children:"Chọn giờ"}),e.jsxs(X,{children:[e.jsx(J,{asChild:!0,children:e.jsx(ie,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(Z,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu để trống mặc định sẽ áp dụng tất cả các giờ trong ngày"})})]})]}),e.jsx("div",{className:"grid grid-cols-6 gap-2",children:Oe.map(l=>{var r;return e.jsxs(j,{type:"button",variant:(r=a.marketingHours)!=null&&r.includes(l.value)?"default":"outline",size:"sm",onClick:()=>m(l.value),className:"text-xs",children:[l.value,":00"]},l.value)})})]})]})})}function qe({discountId:a,initialStoreUid:u,enablePromotionsFetch:o=!1}={}){const m=ye(),l=!!a,{data:r,isLoading:c}=oe(a||""),[s,i]=y.useState({storeUid:u||"",discountType:"PERCENT",discountPercentage:0,discountAmount:0,startDate:new Date().toISOString().split("T")[0],endDate:new Date().toISOString().split("T")[0],marketingDays:[],marketingHours:[],promotionUid:"",promotionName:"",filterState:{is_all:0,is_item:0,is_type:0,type_id:"",item_id:"",is_combo:0,combo_id:""}}),{data:f=[],isLoading:g}=Ce(s.storeUid,{enabled:o&&!!s.storeUid}),n=d=>{const _=new Date(d);return new Date(_.getTime()+7*60*60*1e3).toISOString().split("T")[0]},h=d=>new Date(d+"T00:00:00+07:00"),t=d=>{var K,p,C;const _=n(d.from_date),D=n(d.to_date),R=T.convertNumbersToDayStrings(T.convertBitFlagsToDays(d.time_sale_date_week)),z=T.convertNumbersToHourStrings(T.convertBitFlagsToHours(d.time_sale_hour_day));return{storeUid:d.store_uid,discountType:d.discount_type,discountPercentage:d.discount_type==="PERCENT"?d.ta_discount*100:0,discountAmount:d.discount_type==="AMOUNT"?d.ta_discount:0,startDate:_,endDate:D,marketingDays:R,marketingHours:z,promotionUid:d.promotion_uid,promotionName:((K=d.promotion)==null?void 0:K.promotion_name)||"",filterState:{is_all:d.is_all,is_item:d.is_item,is_type:d.is_type,type_id:d.type_id,item_id:d.item_id,is_combo:(p=d.extra_data)==null?void 0:p.is_combo,combo_id:((C=d.extra_data)==null?void 0:C.combo_id)||""}}};y.useEffect(()=>{if(r&&l){const d=t(r);i(d)}},[r,l]);const{mutate:x,isPending:N}=Te(s.storeUid||"temp",{onSuccess:()=>{m({to:"/sale/discount/regular"})},onError:()=>{}}),{mutate:F,isPending:$}=be(a,{onSuccess:()=>{m({to:"/sale/discount/regular"})},onError:()=>{}}),O=()=>{m({to:"/sale/discount/regular"})},M=()=>{const d=s.marketingDays.length>0?T.convertDayStringsToNumbers(s.marketingDays):[0,1,2,3,4,5,6],_=s.marketingHours.length>0?T.convertHourStringsToNumbers(s.marketingHours):T.getAllHours();return{discountType:s.discountType,discountValue:s.discountType==="PERCENT"?s.discountPercentage/100:s.discountAmount,fromDate:h(s.startDate),toDate:h(s.endDate),selectedDays:d,selectedHours:_,saleChannelUid:"",promotionUid:s.promotionUid||"1576be99-992c-4085-a68c-105f7fbf0fff",filterState:s.filterState}},V=async()=>{if(console.log("🔥 Regular Discount handleSave called"),console.log("🔥 isFormValid:",S),console.log("🔥 formData:",s),!S){console.log("🔥 Form validation failed, stopping submit");return}if(l&&r){const d=M();F(d)}else{const d=M();console.log("🔥 Calling createRegularDiscount with:",d),x(d)}},q=d=>{i(_=>({..._,...d}))},S=s.storeUid!==""&&s.promotionUid!==""&&(s.discountType==="PERCENT"?s.discountPercentage>0:s.discountAmount>0);return console.log("🔥 Form validation details:"),console.log("🔥 storeUid:",s.storeUid),console.log("🔥 promotionUid:",s.promotionUid),console.log("🔥 discountType:",s.discountType),console.log("🔥 discountPercentage:",s.discountPercentage),console.log("🔥 discountAmount:",s.discountAmount),console.log("🔥 isFormValid result:",S),{formData:s,updateFormData:q,handleBack:O,handleSave:V,isFormValid:S,isLoading:N||$||c,isEditMode:l,promotions:o?f:[],isLoadingPromotions:o?g:!1}}function us({discountId:a,storeUid:u}={}){const o=qe({discountId:a,initialStoreUid:u,enablePromotionsFetch:!0});return e.jsx(Se,{value:{...o,promotions:o.promotions||[],isLoadingPromotions:o.isLoadingPromotions||!1},children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(Ee,{}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx(Re,{}),e.jsx(He,{}),e.jsx(Ie,{}),e.jsx(Ve,{})]})})})]})})}export{us as R};
