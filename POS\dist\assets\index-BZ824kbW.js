import{r as N,a3 as qe,u as ne,l as pe,a4 as B,h as $e,j as e,B as E,c as Ke,T as xt,o as pt,p as gt,q as ft,a as _t}from"./index-Bnt3OGV2.js";import{b as Se}from"./pos-api-BwpRFGce.js";import"./vietqr-api-DAENYiJ_.js";import{u as yt}from"./use-customizations-DcL8Qx9h.js";import"./user-oq7iQk7S.js";import"./crm-api-Dd9UhSCJ.js";import{H as bt}from"./header-stuEr_6l.js";import{M as jt}from"./main-Dj7NWzIf.js";import{P as vt}from"./profile-dropdown-BQhzNXaW.js";import{S as Nt,T as wt}from"./search-BwtQTbOQ.js";import{u as Te,a as kt,b as Ie,p as z,c as Ue,d as Ct,g as He,e as St,s as We,S as Qe,v as Tt,f as Ye,h as Xe,r as It,i as Ft,B as Je,I as Mt,j as Dt,k as Et,C as Ot}from"./price-source-dialog-DvgG7drx.js";import{u as Fe,a as Ze}from"./use-items-in-store-data-CtPl2AOB.js";import{h as Pt,x as Bt,G as Rt,H as Vt,J as At,K as Kt,i as Ht,A as Lt,a as zt,C as Gt}from"./react-icons.esm-B7rNr9e-.js";import{D as qt,a as $t,b as Ut,c as de}from"./dropdown-menu-BjJ0HZAV.js";import{D as L}from"./data-table-column-header-vLDb8q5h.js";import{B as Le}from"./badge-u6qfWPSj.js";import{S as Wt}from"./status-badge-BaFhTlYc.js";import"./date-range-picker-CVvofQC0.js";import"./form-wT1R35uI.js";import{C as xe}from"./checkbox-BiVztVsP.js";import{S as fe}from"./settings-BQRdpeYm.js";import{I as Qt}from"./IconCopy-BBR5s6Iq.js";import{I as Yt}from"./IconTrash-BSzFfssj.js";import{u as Xt,g as Jt,a as Zt,b as es,d as ts,e as ss,f as ze}from"./index-BUjvK8mU.js";import{S as ae,a as q}from"./scroll-area-BeVbW7LP.js";import{T as je,a as ve,b as X,c as re,d as Ne,e as J}from"./table-C602nYEy.js";import{C as et}from"./confirm-dialog-D0ceQYlS.js";import{a as ns,C as as}from"./chevron-right-sZt3EK3r.js";import{i as is,u as ce}from"./use-item-types-Q-0SzpLA.js";import{I as Me}from"./input-CiKEYbig.js";import{M as tt}from"./multi-select-DmQbcyz3.js";import{C as _e}from"./combobox-Dfida1wD.js";import{T as De}from"./trash-2-ChT0a0C6.js";import{I as ls}from"./IconFilter-Po0o3cnl.js";import{X as rs}from"./calendar-CzR6WBaB.js";import{S as P}from"./skeleton-C-doKLMW.js";import{E as st}from"./exceljs.min-Da2DFgpf.js";import{c as nt,u as ge}from"./use-stores-IzzNh8VY.js";import{D as $,a as U,b as W,c as Q,e as Ee}from"./dialog-hQ-PVOWr.js";import{S as Z,a as ee,b as te,c as se,d as G,C as cs}from"./select-Czd7KcZQ.js";import{C as os,a as ds,b as hs}from"./collapsible-CfXqqCTe.js";import{S as at}from"./search-BVQVKwPC.js";import{read as Oe,utils as he,writeFile as ms}from"./xlsx-DkH2s96g.js";import{u as we}from"./use-item-classes-CsFb8pem.js";import{u as ke}from"./use-units-DeWvOXUG.js";import{u as us}from"./use-removed-items-niCKPB6t.js";import{D as Pe}from"./download-hLMxLFI2.js";import{U as Be}from"./upload-BdXwcNQr.js";import{u as Re}from"./useQuery-DSrD7NAp.js";import{u as it}from"./useMutation-d67-fNFq.js";import{i as xs}from"./item-api-aC51KELB.js";import{s as ps}from"./sources-api-DQD3aj-a.js";import{Q as me}from"./query-keys-3lmd-xp6.js";import{b as lt,c as be,K as rt,P as ct,D as ot,d as dt,C as ht}from"./core.esm-BuIOEJpF.js";import{c as mt}from"./createReactComponent-BD5R5KSl.js";import{C as gs,d as fs}from"./card-aL168XsH.js";import{C as Ce}from"./check-apx2eTVC.js";import"./separator-CClVRZ9M.js";import"./avatar-D33aZz95.js";import"./search-context-DLufo9i0.js";import"./command-ByfqjQDn.js";import"./IconChevronRight-BM-o6vT_.js";import"./IconSearch-DCdKv7Cy.js";import"./use-dialog-state-jYRzWcqs.js";import"./modal-B0J8RkN-.js";import"./zod-CNC8A7Cl.js";import"./items-in-store-api-BIg85alI.js";import"./date-picker-DhOVzRMj.js";import"./popover-C2mvzdeD.js";import"./index-BT7Z3RDV.js";import"./calendar-BgsBunwq.js";import"./createLucideIcon-CNa_hh6B.js";import"./isSameMonth-C8JQo-AN.js";import"./circle-help-vaGE5apo.js";import"./index-Bl1CGAiZ.js";import"./index-UiaF_xtq.js";import"./index-C2T2k_Lh.js";import"./alert-dialog-BfzPuNaL.js";import"./circle-x-Cw52ZI-1.js";import"./chevrons-up-down-BYsm4o8o.js";import"./stores-api-C8uGNHTJ.js";import"./utils-km2FGkQ4.js";import"./sources-CfiQ7039.js";/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var _s=mt("outline","caret-up-down","IconCaretUpDown",[["path",{d:"M18 10l-6 -6l-6 6h12",key:"svg-0"}],["path",{d:"M18 14l-6 6l-6 -6h12",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var ys=mt("outline","pinned","IconPinned",[["path",{d:"M9 4v6l-2 4v2h10v-2l-2 -4v-6",key:"svg-0"}],["path",{d:"M12 16l0 5",key:"svg-1"}],["path",{d:"M8 4l8 0",key:"svg-2"}]]);function ut(){const[i,t]=N.useState(!1),[s,n]=N.useState(!1),[l,h]=N.useState(null),[m,x]=N.useState(!1),[o,_]=N.useState([]),[w,M]=N.useState("all"),[y,f]=N.useState(""),[k,p]=N.useState([]),[I,D]=N.useState("all"),[T,u]=N.useState("-1");return{isCustomizationDialogOpen:i,isBuffetItem:s,selectedMenuItem:l,isBuffetConfigModalOpen:m,selectedBuffetMenuItem:o,selectedItemTypeUid:w,selectedStoreUid:y,selectedDaysOfWeek:k,selectedStatus:I,selectedApplyWithStore:T,setIsCustomizationDialogOpen:t,setIsBuffetItem:n,setSelectedMenuItem:h,setIsBuffetConfigModalOpen:x,setSelectedBuffetMenuItem:_,setSelectedItemTypeUid:M,setSelectedStoreUid:f,setSelectedDaysOfWeek:p,setSelectedStatus:D,setSelectedApplyWithStore:u}}function bs(){const i=qe(),{company:t}=ne(n=>n.auth),{selectedBrand:s}=pe();return it({mutationFn:async n=>{if(!(t!=null&&t.id)||!(s!=null&&s.id)){B.error("Thiếu thông tin công ty hoặc thương hiệu");return}const l={company_uid:t.id,brand_uid:s.id,store_uid:n.store_uid,data:n.data};await Se.put("/mdata/v1/item-types/sort",l)},onSuccess:()=>{i.invalidateQueries({queryKey:[me.ITEM_TYPES]}),B.success("Sắp xếp nhóm món thành công!")},onError:n=>{B.error(`Lỗi khi sắp xếp nhóm món: ${n==null?void 0:n.message}`)}})}const js=(i,t=!0)=>{const{company:s,brands:n}=ne(h=>h.auth),l=n==null?void 0:n[0];return Re({queryKey:[me.ITEMS_LIST,"price-by-source",i],queryFn:async()=>{const h={company_uid:(s==null?void 0:s.id)||"",brand_uid:(l==null?void 0:l.id)||"",list_store_uid:i,skip_limit:!0,active:1};return(await xs.getItems(h)).data||[]},enabled:t&&!!(s!=null&&s.id&&(l!=null&&l.id)&&i),staleTime:5*60*1e3,gcTime:10*60*1e3})},vs=(i,t=!0)=>{const{company:s,brands:n}=ne(h=>h.auth),l=n==null?void 0:n[0];return Re({queryKey:[me.SOURCES,"price-by-source",i],queryFn:async()=>{const h={company_uid:(s==null?void 0:s.id)||"",brand_uid:(l==null?void 0:l.id)||"",store_uid:i,skip_limit:!0};return await ps.getSources(h)||[]},enabled:t&&!!(s!=null&&s.id&&(l!=null&&l.id)&&i),staleTime:5*60*1e3,gcTime:10*60*1e3})},Ns=(i,t=!0)=>{const{company:s,brands:n}=ne(h=>h.auth),l=n==null?void 0:n[0];return Re({queryKey:[me.ITEM_TYPES,"price-by-source",i],queryFn:async()=>{const h={company_uid:(s==null?void 0:s.id)||"",brand_uid:(l==null?void 0:l.id)||"",store_uid:i,skip_limit:!0,active:1};return(await is.getItemTypes(h)).data||[]},enabled:t&&!!(s!=null&&s.id&&(l!=null&&l.id)&&i),staleTime:5*60*1e3,gcTime:10*60*1e3})},ws=(i,t=!0)=>{const s=js(i,t),n=vs(i,t),l=Ns(i,t);return{items:s.data||[],sources:n.data||[],itemTypes:l.data||[],isLoading:s.isLoading||n.isLoading||l.isLoading,isError:s.isError||n.isError||l.isError,error:s.error||n.error||l.error,refetch:()=>{s.refetch(),n.refetch(),l.refetch()}}},ks=(i,t,s)=>i.map(n=>{const l=t.find(x=>x.id===n.item_uid);if(!l)throw new Error(`Original item not found for ${n.item_uid}`);const h=[],m={};return s.forEach(x=>{const o=`${x.sourceName} [${x.sourceId}]`;m[o]=x.sourceId}),Object.entries(m).forEach(([x,o])=>{const _=n[x];if(_!=null&&_!==""){const w=typeof _=="string"?parseFloat(_):_;!isNaN(w)&&w>0&&h.push({source_id:o,price:w})}}),{...l,ots_price:n.ots_price,ots_tax:n.ots_tax/100,ta_price:n.ots_price,ta_tax:n.ots_tax/100,extra_data:{...l.extra_data,price_by_source:h}}}),Cs=()=>{const i=qe(),{mutate:t,isPending:s}=it({mutationFn:async n=>{const l=ks(n.previewItems,n.originalItems,n.sources),h=await Se.put("/mdata/v1/items",l);return h.data.data||h.data},onSuccess:()=>{i.invalidateQueries({queryKey:[me.ITEMS_LIST],refetchType:"none"}),setTimeout(()=>{i.refetchQueries({queryKey:[me.ITEMS_LIST]})},100),B.success("Đã cập nhật cấu hình giá theo nguồn thành công!")},onError:n=>{B.error(`Có lỗi xảy ra khi lưu cấu hình: ${n.message}`)}});return{bulkUpdatePriceBySource:t,isUpdating:s}};function Ss(){const{setOpen:i,selectedStoreUid:t}=Te(),s=$e(),n=()=>{s({to:"/menu/items/items-in-store/detail",state:w=>({...w,store_uid:t})})},l=()=>{i("export")},h=()=>{i("import")},m=()=>{i("config-price-by-source")},x=()=>{i("sort-menu")},o=()=>{i("copy-menu")},_=()=>{i("config-time-frame")};return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(qt,{children:[e.jsx($t,{asChild:!0,children:e.jsxs(E,{variant:"outline",size:"sm",children:["Tiện ích",e.jsx(Pt,{className:"ml-2 h-4 w-4"})]})}),e.jsxs(Ut,{align:"end",className:"w-56",children:[e.jsxs(de,{onClick:l,children:[e.jsx(Bt,{className:"mr-2 h-4 w-4"}),"Xuất, sửa thực đơn"]}),e.jsxs(de,{onClick:h,children:[e.jsx(Rt,{className:"mr-2 h-4 w-4"}),"Thêm món từ file"]}),e.jsxs(de,{onClick:m,children:[e.jsx(Vt,{className:"mr-2 h-4 w-4"}),"Cấu hình giá theo nguồn"]}),e.jsxs(de,{onClick:x,children:[e.jsx(At,{className:"mr-2 h-4 w-4"}),"Sắp xếp thực đơn"]}),e.jsxs(de,{onClick:o,children:[e.jsx(Kt,{className:"mr-2 h-4 w-4"}),"Sao chép thực đơn"]}),e.jsxs(de,{onClick:_,children:[e.jsx(Ht,{className:"mr-2 h-4 w-4"}),"Cấu hình khung thời gian"]})]})]}),e.jsx(E,{variant:"default",size:"sm",onClick:n,children:"Tạo món"})]})})}function Ge({column:i,title:t,className:s,defaultSort:n="desc"}){if(!i.getCanSort())return e.jsx("div",{className:Ke(s),children:t});const l=()=>{const h=i.getIsSorted();h?h==="desc"?i.toggleSorting(!1):i.toggleSorting(!0):i.toggleSorting(n==="desc")};return e.jsx("div",{className:Ke("flex items-center space-x-2",s),children:e.jsxs(E,{variant:"ghost",size:"sm",className:"hover:bg-accent -ml-3 h-8",onClick:l,children:[e.jsx("span",{children:t}),i.getIsSorted()==="desc"?e.jsx(Lt,{className:"ml-2 h-4 w-4"}):i.getIsSorted()==="asc"?e.jsx(zt,{className:"ml-2 h-4 w-4"}):e.jsx(Gt,{className:"ml-2 h-4 w-4"})]})})}const Ts=({onBuffetConfigClick:i})=>[{id:"select",header:({table:t})=>e.jsx(xe,{checked:t.getIsAllPageRowsSelected(),onCheckedChange:s=>t.toggleAllPageRowsSelected(!!s),"aria-label":"Select all"}),cell:({row:t})=>e.jsx(xe,{checked:t.getIsSelected(),onCheckedChange:s=>t.toggleSelected(!!s),"aria-label":"Select row",onClick:s=>s.stopPropagation()}),enableSorting:!1,enableHiding:!1,size:50},{id:"index",header:"#",cell:({row:t})=>e.jsx("div",{className:"w-[50px]",children:t.index+1}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"code",header:({column:t})=>e.jsx(L,{column:t,title:"Mã món"}),cell:({row:t})=>e.jsx("div",{className:"text-sm font-medium",children:t.getValue("code")}),enableSorting:!1,enableHiding:!0},{accessorKey:"name",header:({column:t})=>e.jsx(L,{column:t,title:"Tên món"}),cell:({row:t})=>e.jsx("div",{className:"max-w-[200px] truncate text-sm font-medium",children:t.getValue("name")}),enableSorting:!1,enableHiding:!0},{accessorKey:"price",header:({column:t})=>e.jsx(L,{column:t,title:"Giá"}),cell:({row:t})=>{const s=t.getValue("price");return e.jsx("div",{className:"text-sm font-medium",children:new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(s)})},enableSorting:!1,enableHiding:!0},{accessorKey:"vatPercent",header:({column:t})=>e.jsx(L,{column:t,title:"VAT (%)"}),cell:({row:t})=>{const s=t.getValue("vatPercent");return e.jsx("div",{className:"text-right text-sm",children:s*100})},enableSorting:!1,enableHiding:!0},{accessorKey:"itemType",header:({column:t})=>e.jsx(L,{column:t,title:"Nhóm món"}),cell:({row:t})=>e.jsx(Le,{variant:"outline",className:"text-xs",children:t.getValue("itemType")}),enableSorting:!1,enableHiding:!0},{accessorKey:"itemClass",header:({column:t})=>e.jsx(L,{column:t,title:"Loại món"}),cell:({row:t})=>t.getValue("itemClass")&&e.jsx(Le,{variant:"outline",className:"text-center text-xs",children:t.getValue("itemClass")}),enableSorting:!1,enableHiding:!0},{accessorKey:"unit",header:({column:t})=>e.jsx(L,{column:t,title:"Đơn vị tính"}),cell:({row:t})=>e.jsx("div",{className:"text-sm",children:t.getValue("unit")}),enableSorting:!1,enableHiding:!0},{accessorKey:"sideItems",header:({column:t})=>e.jsx(Ge,{column:t,title:"Món ăn kèm",defaultSort:"desc"}),cell:({row:t})=>{const s=t.getValue("sideItems");if(!s)return e.jsx("div",{children:"Món chính"});const n=s==="Món ăn kèm"?"Món ăn kèm":s;return e.jsx(xt,{children:e.jsxs(pt,{children:[e.jsx(gt,{asChild:!0,children:e.jsx("div",{className:"max-w-[120px] cursor-help truncate text-sm",children:n})}),e.jsx(ft,{children:e.jsx("p",{className:"max-w-[300px]",children:n})})]})})},enableSorting:!0,enableHiding:!0},{accessorKey:"city",header:({column:t})=>e.jsx(L,{column:t,title:"Thành phố"}),cell:({row:t})=>e.jsx("div",{className:"text-sm",children:t.getValue("city")}),enableSorting:!1,enableHiding:!0},{accessorKey:"applyWithStore",header:({column:t})=>e.jsx(L,{column:t,title:"Áp dụng tại"}),cell:({row:t})=>e.jsx("div",{className:"text-sm",children:t.getValue("applyWithStore")?"Cửa hàng":""}),enableSorting:!1,enableHiding:!0},{accessorKey:"status",header:({column:t})=>e.jsx(L,{column:t,title:"Trạng thái"}),cell:({row:t})=>e.jsx("div",{className:"text-sm",children:t.getValue("applyWithStore")===1?"Sửa từ món gốc":"Món mới"}),enableSorting:!1,enableHiding:!0},{accessorKey:"buffetConfig",header:({column:t})=>e.jsx(L,{column:t,title:"Cấu hình buffet"}),cell:({row:t})=>{var l;const s=t.original;return((l=s.extra_data)==null?void 0:l.is_buffet_item)===1?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:"Đã cấu hình"}),e.jsx(E,{variant:"outline",size:"sm",onClick:()=>i(s),className:"h-6 px-2 text-xs",children:e.jsx(fe,{className:"h-3 w-3"})})]}):e.jsxs(E,{variant:"outline",size:"sm",onClick:()=>i(s),className:"h-7 px-2 text-xs",children:[e.jsx(fe,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{accessorKey:"customization",header:({column:t})=>e.jsx(L,{column:t,title:"Customization"}),cell:({row:t,table:s})=>{var x;const n=t.original,l=s.options.meta,h=n.customization_uid,m=(x=l==null?void 0:l.customizations)==null?void 0:x.find(o=>o.id===h);return m?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:m.name}),e.jsx(E,{variant:"outline",size:"sm",onClick:()=>{var o;return(o=l==null?void 0:l.onCustomizationClick)==null?void 0:o.call(l,n)},className:"h-6 px-2 text-xs",children:e.jsx(fe,{className:"h-3 w-3"})})]}):e.jsxs(E,{variant:"outline",size:"sm",onClick:()=>{var o;return(o=l==null?void 0:l.onCustomizationClick)==null?void 0:o.call(l,n)},className:"h-7 px-2 text-xs",children:[e.jsx(fe,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{id:"copy",header:"Sao chép tạo món mới",cell:({row:t,table:s})=>{const n=t.original,l=s.options.meta;return e.jsxs(E,{variant:"ghost",size:"sm",className:"ml-14 h-8 w-8",onClick:h=>{var m;h.stopPropagation(),(m=l==null?void 0:l.onCopyClick)==null||m.call(l,n)},children:[e.jsx(Qt,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Sao chép thiết bị ",n.item_name]})]})},enableSorting:!1,enableHiding:!0},{accessorKey:"isActive",header:({column:t})=>e.jsx(Ge,{column:t,title:"Thao tác",defaultSort:"desc"}),enableSorting:!0,cell:({row:t,table:s})=>{const n=t.original,l=t.getValue("isActive"),h=s.options.meta;return e.jsx("div",{onClick:m=>{var x;m.stopPropagation(),(x=h==null?void 0:h.onToggleStatus)==null||x.call(h,n)},className:"cursor-pointer",children:e.jsx(Wt,{isActive:l,activeText:"Active",inactiveText:"Deactive"})})},enableHiding:!0},{id:"actions",cell:({row:t,table:s})=>{const n=t.original,l=s.options.meta;return e.jsx("div",{className:"flex items-center justify-center",children:e.jsxs(E,{variant:"ghost",size:"sm",onClick:h=>{var m;h.stopPropagation(),(m=l==null?void 0:l.onDeleteClick)==null||m.call(l,n)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:[e.jsx(Yt,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Xóa món ",n.item_name]})]})})},enableSorting:!1,enableHiding:!1,size:80}],Is=Ts;function Fs({currentPage:i,onPageChange:t,hasNextPage:s}){const n=()=>{i>1&&t(i-1)},l=()=>{s&&t(i+1)};return e.jsxs("div",{className:"flex items-center justify-center gap-4 py-4",children:[e.jsxs(E,{variant:"outline",size:"sm",onClick:n,disabled:i===1,className:"flex items-center gap-2",children:[e.jsx(ns,{className:"h-4 w-4"}),"Trước"]}),e.jsx("span",{className:"text-sm font-medium",children:i}),e.jsxs(E,{variant:"outline",size:"sm",onClick:l,disabled:!s,className:"flex items-center gap-2",children:["Sau",e.jsx(as,{className:"h-4 w-4"})]})]})}const Ms=[{label:"Thứ 2",value:"2"},{label:"Thứ 3",value:"3"},{label:"Thứ 4",value:"4"},{label:"Thứ 5",value:"5"},{label:"Thứ 6",value:"6"},{label:"Thứ 7",value:"7"},{label:"Chủ Nhật",value:"1"}],Ds=[{label:"Tất cả trạng thái",value:"all"},{label:"Active",value:"1"},{label:"Deactive",value:"0"}],ye=[{label:"Cửa hàng và thành phố",value:"0"},{label:"Cửa hàng",value:"-1"}];function Es({table:i,selectedItemTypeUid:t="all",onItemTypeChange:s,selectedStoreUid:n="",onStoreChange:l,selectedDaysOfWeek:h=[],onDaysOfWeekChange:m,selectedStatus:x="all",onStatusChange:o,selectedApplyWithStore:_="-1",onApplyWithStoreChange:w,onDeleteSelected:M}){var b;const[y,f]=N.useState(!1),{currentBrandStores:k}=_t(),p=k.filter(d=>d.active===1).map(d=>({label:d.store_name,value:d.id})),{data:I=[]}=ce(),D=I.filter(d=>d.active===1).map(d=>({label:d.item_type_name,value:d.id}));N.useEffect(()=>{var c;const d=(c=p[0])==null?void 0:c.value,r=p.some(a=>a.value===n);d&&(!n||!r)&&l&&l(d)},[n,p,l]),N.useEffect(()=>{var c;const d=(c=ye[0])==null?void 0:c.value,r=ye.some(a=>a.value===_);d&&(!_||!r)&&w&&w(d)},[_,ye,w]);const T=i.getState().columnFilters.length>0,u=i.getFilteredSelectedRowModel().rows.length;return e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[u>0&&e.jsxs(E,{variant:"destructive",size:"sm",onClick:M,className:"h-9",children:[e.jsx(De,{}),"Xóa món (",u,")"]}),e.jsx(Me,{placeholder:"Tìm kiếm món ăn...",value:((b=i.getColumn("name"))==null?void 0:b.getFilterValue())??"",onChange:d=>{var r;return(r=i.getColumn("name"))==null?void 0:r.setFilterValue(d.target.value)},className:"h-9 w-[150px] lg:w-[250px]"}),e.jsx(_e,{value:n,onValueChange:d=>l&&l(d),options:p,placeholder:"Chọn cửa hàng",searchPlaceholder:"Tìm cửa hàng...",emptyText:"Không có dữ liệu",className:"w-[260px]"}),e.jsx(_e,{value:t,onValueChange:d=>s&&s(d),options:[{label:"Tất cả nhóm món",value:"all"},...D],placeholder:"Chọn loại món",searchPlaceholder:"Tìm nhóm món...",emptyText:"Không có dữ liệu",className:"w-[260px]"}),e.jsxs(E,{variant:"outline",size:"sm",onClick:()=>f(!y),className:"h-9",children:[e.jsx(ls,{className:"h-4 w-4"}),"Nâng cao"]}),T&&e.jsxs(E,{variant:"ghost",onClick:()=>i.resetColumnFilters(),className:"h-10 px-2 lg:px-3",children:["Reset",e.jsx(rs,{className:"ml-2 h-4 w-4"})]})]})}),y&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(tt,{options:Ms,value:h,onValueChange:m||(()=>{}),placeholder:"Chọn ngày trong tuần",className:"min-h-9 w-[300px]",maxCount:1}),e.jsx(_e,{value:_,onValueChange:d=>w&&w(d),options:ye,placeholder:"Chọn áp dụng tại",searchPlaceholder:"Tìm áp dụng tại...",emptyText:"Không có dữ liệu",className:"w-[220px]"}),e.jsx(_e,{value:x,onValueChange:d=>o&&o(d),options:Ds,placeholder:"Chọn trạng thái",searchPlaceholder:"Tìm trạng thái...",emptyText:"Không có dữ liệu",className:"w-[180px]"})]})]})}function Os({columns:i,data:t,onCustomizationClick:s,onCopyClick:n,onToggleStatus:l,onRowClick:h,onDeleteClick:m,customizations:x,selectedItemTypeUid:o,onItemTypeChange:_,selectedStoreUid:w,onStoreChange:M,selectedDaysOfWeek:y,onDaysOfWeekChange:f,selectedStatus:k,onStatusChange:p,selectedApplyWithStore:I,onApplyWithStoreChange:D,hasNextPageOverride:T,currentPage:u,onPageChange:b}){var ie;const[d,r]=N.useState({}),[c,a]=N.useState({}),[g,j]=N.useState([]),[S,v]=N.useState([]),[V,R]=N.useState(!1),{deleteMultipleItemsAsync:H}=kt(),O=()=>{R(!0)},ue=async()=>{try{const F=Y.getFilteredSelectedRowModel().rows.map(K=>K.original.id);await H(F),R(!1),Y.resetRowSelection()}catch{}},oe=(C,F)=>{const K=F.target;K.closest('input[type="checkbox"]')||K.closest("button")||K.closest('[role="button"]')||K.closest(".badge")||K.tagName==="BUTTON"||h==null||h(C)},Y=Xt({data:t,columns:i,state:{sorting:S,columnVisibility:c,rowSelection:d,columnFilters:g},enableRowSelection:!0,onRowSelectionChange:r,onSortingChange:v,onColumnFiltersChange:j,onColumnVisibilityChange:a,getCoreRowModel:ss(),getFilteredRowModel:ts(),getSortedRowModel:es(),getFacetedRowModel:Zt(),getFacetedUniqueValues:Jt(),meta:{onCustomizationClick:s,onCopyClick:n,onToggleStatus:l,onDeleteClick:m,customizations:x}});return e.jsxs("div",{className:"space-y-4",children:[e.jsx(Es,{table:Y,selectedItemTypeUid:o,onItemTypeChange:_,selectedStoreUid:w,onStoreChange:M,selectedDaysOfWeek:y,onDaysOfWeekChange:f,selectedStatus:k,onStatusChange:p,onDeleteSelected:O,selectedApplyWithStore:I,onApplyWithStoreChange:D}),e.jsxs(ae,{className:"rounded-md border",children:[e.jsxs(je,{className:"relative",children:[e.jsx(ve,{children:Y.getHeaderGroups().map(C=>e.jsx(X,{children:C.headers.map(F=>e.jsx(re,{colSpan:F.colSpan,children:F.isPlaceholder?null:ze(F.column.columnDef.header,F.getContext())},F.id))},C.id))}),e.jsx(Ne,{children:(ie=Y.getRowModel().rows)!=null&&ie.length?Y.getRowModel().rows.map(C=>e.jsx(X,{"data-state":C.getIsSelected()&&"selected",className:"hover:bg-muted/50 cursor-pointer",onClick:F=>oe(C.original,F),children:C.getVisibleCells().map(F=>e.jsx(J,{children:ze(F.column.columnDef.cell,F.getContext())},F.id))},C.id)):e.jsx(X,{children:e.jsx(J,{colSpan:i.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]}),e.jsx(q,{orientation:"horizontal"})]}),e.jsx(Fs,{currentPage:u??1,onPageChange:C=>b&&b(C),hasNextPage:!!T}),e.jsx(et,{open:V,onOpenChange:R,title:`Bạn có chắc muốn xóa ${Y.getFilteredSelectedRowModel().rows.length} món đã chọn`,desc:"Hành động này không thể hoàn tác.",confirmText:"Xóa",cancelBtnText:"Hủy",className:"top-[30%] translate-y-[-50%]",handleConfirm:ue,destructive:!0})]})}function Ps(){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(P,{className:"h-8 w-[250px]"}),e.jsx(P,{className:"h-8 w-[100px]"}),e.jsx(P,{className:"h-8 w-[100px]"}),e.jsx(P,{className:"h-8 w-[100px]"})]}),e.jsx(P,{className:"h-8 w-[100px]"})]}),e.jsxs("div",{className:"rounded-md border",children:[e.jsx("div",{className:"border-b p-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(P,{className:"h-4 w-8"}),e.jsx(P,{className:"h-4 w-20"}),e.jsx(P,{className:"h-4 w-32"}),e.jsx(P,{className:"h-4 w-20"}),e.jsx(P,{className:"h-4 w-16"}),e.jsx(P,{className:"h-4 w-24"}),e.jsx(P,{className:"h-4 w-20"}),e.jsx(P,{className:"h-4 w-16"}),e.jsx(P,{className:"h-4 w-24"}),e.jsx(P,{className:"h-4 w-20"}),e.jsx(P,{className:"h-4 w-24"}),e.jsx(P,{className:"h-4 w-24"}),e.jsx(P,{className:"h-4 w-24"}),e.jsx(P,{className:"h-4 w-16"})]})}),Array.from({length:10}).map((i,t)=>e.jsx("div",{className:"border-b p-4 last:border-b-0",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(P,{className:"h-4 w-8"}),e.jsx(P,{className:"h-4 w-20"}),e.jsx(P,{className:"h-4 w-32"}),e.jsx(P,{className:"h-4 w-20"}),e.jsx(P,{className:"h-4 w-16"}),e.jsx(P,{className:"h-4 w-24"}),e.jsx(P,{className:"h-4 w-20"}),e.jsx(P,{className:"h-4 w-16"}),e.jsx(P,{className:"h-4 w-24"}),e.jsx(P,{className:"h-4 w-20"}),e.jsx(P,{className:"h-4 w-24"}),e.jsx(P,{className:"h-4 w-24"}),e.jsx(P,{className:"h-4 w-24"}),e.jsx(P,{className:"h-4 w-16"})]})},t))]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(P,{className:"h-8 w-[200px]"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(P,{className:"h-8 w-[100px]"}),e.jsx(P,{className:"h-8 w-8"}),e.jsx(P,{className:"h-8 w-8"})]})]})]})}const Bs=()=>{const i=new st.Workbook;return i.creator="POS System",i.lastModifiedBy="POS System",i.created=new Date,i.modified=new Date,i},Rs=()=>["Tên","Giá","Mã món","Mã barcode","Món ăn kèm","Không cập nhật số lượng món ăn kèm","Nhóm","Loại món","Mô tả","SKU","Đơn vị","VAT (%)","Thời gian chế biến (phút)","Cho phép sửa giá khi bán","Cấu hình món ảo","Cấu hình món dịch vụ","Cấu hình món ăn là vé buffet","Ngày","Giờ","Hình ảnh","Công thức inQR cho máy pha trà"],Vs=i=>{const t=Rs(),s=i.addWorksheet("Menu");return s.addRow(t).eachCell(h=>{h.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},h.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},h.alignment={horizontal:"center",vertical:"middle"},h.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),[25,12,15,15,15,35,12,12,25,15,12,12,25,25,20,25,35,12,12,25,35].forEach((h,m)=>{s.getColumn(m+1).width=h}),s},As=()=>["Tên","Giá (Mặc định 0)","Mã món","Mã barcode (Tối đa 13)","Món ăn kèm (Mặc định 0)","Không cập nhật số lượng món ăn kèm (Mặc định 0)","Nhóm (Mặc định LOẠI KHÁC)","Loại món (Mặc định rỗng)","Mô tả","SKU (Tối đa 50)","Đơn vị (Mặc định MON)","VAT (%) (Mặc định 0)","Thời gian chế biến (phút) (Mặc định 0)","Cho phép sửa giá khi bán (Mặc định 0)","Cấu hình món ảo (Mặc định 0)","Cấu hình món dịch vụ (Mặc định 0)","Cấu hình món ăn là vé buffet (Mặc định 0)","Ngày (Mặc định 0)","Giờ (Mặc định 0)","Hình ảnh"],Ks=()=>[["Món 1",1e5,"TRA_SUA_SO_1_YQ4P","",0,0,"MONN_KEMVK84","ITEM_CLASS-9ZKE","Món đắt","item-121","COC",8,12,0,0,0,0,124,12,"https://img.foodbook.vn/images/20191202/1575271193305-image.jpg"],["Món 2",99999,"ITEM_KFHE","1281-II",1,1,"ITEMTYPE_BC56","ITEM_CLASS-12OP","Món ăn ngon","ITEM111","LANG",12,20,1,1,1,1,0,0,"https://img.foodbook.vn/images/20191107/1573097661136-image.jpg"]],Hs=()=>[["Chủ nhật","2"],["Thứ 2","4"],["Thứ 3","8"],["Thứ 4","16"],["Thứ 5","32"],["Thứ 6","64"],["Thứ 7","128"],["Ví dụ: CN, T2, T5 = 2 + 4 + 32","38"]],Ls=()=>[["0h","1"],["1h","2"],["2h","4"],["3h","8"],["4h","16"],["5h","32"],["6h","64"],["7h","128"],["8h","256"],["9h","512"],["10h","1024"],["11h","2048"],["12h","4096"],["13h","8192"],["14h","16384"],["15h","32768"],["16h","65536"],["17h","131072"],["18h","262144"],["19h","524288"],["20h","1048576"],["21h","2097152"],["22h","4194304"],["23h","8388608"],["Ví dụ: 0h, 1h, 3h = 1 + 2 + 8","11"]],zs=i=>{const t=i.filter(s=>s.active===1).map(s=>[s.item_type_id,s.item_type_name]);return t.length>0?t:[["Không có dữ liệu",""]]},Gs=i=>{const t=i.filter(s=>s.active===1).map(s=>[s.item_class_id,s.item_class_name]);return t.length>0?t:[["Không có dữ liệu",""]]},qs=i=>{if(!i||i.length===0)return[["Không có dữ liệu",""]];const t=i.map(s=>[s.unit_id,s.unit_name]);return t.length>0?t:[["Không có dữ liệu",""]]},$s=(i,t)=>{var T,u,b,d,r,c,a,g,j,S;const s=As(),n=i.addWorksheet("Template"),l=n.addRow(['Đây là sheet mẫu để tham khảo. Vui lòng quay lại sheet "Menu" để nhập dữ liệu.']);n.mergeCells(`A${l.number}:T${l.number}`),l.getCell(1).fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},l.getCell(1).font={color:{argb:"FFFFFFFF"},bold:!0,size:11},l.getCell(1).alignment={horizontal:"left",vertical:"middle"},l.getCell(1).border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}},n.addRow(s).eachCell(v=>{v.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF79ABE3"}},v.font={bold:!0,size:11},v.alignment={horizontal:"center",vertical:"middle"},v.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),Ks().forEach(v=>{n.addRow(v).eachCell(R=>{R.font={size:10},R.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}}})}),n.addRow([]);const x=n.addRow(["BẢNG THAM CHIẾU NGÀY","","","BẢNG THAM CHIẾU GIỜ","","","BẢNG THAM CHIẾU NHÓM MÓN","","","BẢNG THAM CHIẾU LOẠI MÓN","","","BẢNG THAM CHIẾU ĐƠN VỊ",""]),o=x.number;n.mergeCells(`A${o}:B${o}`),n.mergeCells(`D${o}:E${o}`),n.mergeCells(`G${o}:H${o}`),n.mergeCells(`J${o}:K${o}`),n.mergeCells(`M${o}:N${o}`),x.eachCell(v=>{v.value&&v.value.toString().trim()!==""&&(v.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},v.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},v.alignment={horizontal:"center",vertical:"middle"},v.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}})}),n.addRow(["Thời gian","Giá trị","","Thời gian","Giá trị","","Mã nhóm","Tên nhóm","","Mã loại món","Tên loại món","","Mã đơn vị","Tên đơn vị"]).eachCell(v=>{v.value&&v.value.toString().trim()!==""&&(v.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF79ABE3"}},v.font={bold:!0,size:10},v.alignment={horizontal:"center",vertical:"middle"},v.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}})});const w=Hs(),M=Ls(),y=t!=null&&t.itemTypes?zs(t.itemTypes):[["Không có dữ liệu",""]],f=t!=null&&t.itemClasses?Gs(t.itemClasses):[["Không có dữ liệu",""]],k=t!=null&&t.units?qs(t.units):[["Không có dữ liệu",""]],p=Math.max(w.length,M.length,y.length,f.length,k.length),I=[];for(let v=0;v<p;v++){const V=[((T=w[v])==null?void 0:T[0])||"",((u=w[v])==null?void 0:u[1])||"","",((b=M[v])==null?void 0:b[0])||"",((d=M[v])==null?void 0:d[1])||"","",((r=y[v])==null?void 0:r[0])||"",((c=y[v])==null?void 0:c[1])||"","",((a=f[v])==null?void 0:a[0])||"",((g=f[v])==null?void 0:g[1])||"","",((j=k[v])==null?void 0:j[0])||"",((S=k[v])==null?void 0:S[1])||""];I.push(V)}return I.forEach(v=>{n.addRow(v).eachCell(R=>{R.value&&R.value.toString().trim()!==""&&(R.font={size:10},R.alignment={horizontal:"left",vertical:"middle"},R.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}})})}),[25,12,15,15,15,35,12,12,25,15,12,12,25,25,20,25,35,12,12,25].forEach((v,V)=>{n.getColumn(V+1).width=v}),n},Us=async i=>{try{const t=Bs();Vs(t),$s(t,i);const s=await t.xlsx.writeBuffer(),n=new Blob([s],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),h=`items_import_template_${new Date().toISOString().slice(0,19).replace(/:/g,"-")}.xlsx`,m=window.URL.createObjectURL(n),x=document.createElement("a");return x.href=m,x.download=h,document.body.appendChild(x),x.click(),document.body.removeChild(x),window.URL.revokeObjectURL(m),Promise.resolve()}catch(t){return console.error("Error creating Excel file:",t),Promise.reject(t)}},Ws=["item_uid","item_id","item_name","Nhóm món","Giá gốc","Vat (%)","ZALO [10000045]","FACEBOOK [10000049]","SO [10000134]","CRM [10000162]","VNPAY [10000165]","GOJEK (GOVIET) [10000168]","ShopeeFood [10000169]","MANG VỀ [10000171]","TẠI CHỖ [10000172]","CALL CENTER [10000176]","O2O [10000216]","BEFOOD [10000253]"],Qs=async i=>new Promise((t,s)=>{const n=new FileReader;n.onload=l=>{var h;try{const m=(h=l.target)==null?void 0:h.result;if(!m){s(new Error("Không thể đọc file"));return}const x=Oe(m,{type:"array"}),o=x.SheetNames[0];if(!o){s(new Error("File Excel không có sheet nào"));return}const _=x.Sheets[o],w=he.sheet_to_json(_,{header:1});if(w.length<2){s(new Error("File Excel không có dữ liệu"));return}const M=w[0],y=Ws.filter(k=>!M.includes(k));y.length>0&&console.warn("Missing headers:",y);const f=[];for(let k=1;k<w.length;k++){const p=w[k];if(!p||p.length===0)continue;const I={item_uid:"",item_id:"",item_name:"",item_type_name:"",ots_price:0,ots_tax:0};M.forEach((D,T)=>{const u=p[T];switch(D){case"item_uid":I.item_uid=String(u||"");break;case"item_id":I.item_id=String(u||"");break;case"item_name":I.item_name=String(u||"");break;case"Nhóm món":I.item_type_name=String(u||"");break;case"Giá gốc":I.ots_price=parseFloat(String(u||"0"))||0;break;case"Vat (%)":I.ots_tax=parseFloat(String(u||"0"))||0;break;case"ZALO [10000045]":case"FACEBOOK [10000049]":case"SO [10000134]":case"CRM [10000162]":case"VNPAY [10000165]":case"GOJEK (GOVIET) [10000168]":case"ShopeeFood [10000169]":case"MANG VỀ [10000171]":case"TẠI CHỖ [10000172]":case"CALL CENTER [10000176]":case"O2O [10000216]":case"BEFOOD [10000253]":if(u!=null&&u!==""){const b=parseFloat(String(u));isNaN(b)||(I[D]=b)}break;default:u!=null&&(I[D]=u);break}}),I.item_uid&&I.item_id&&I.item_name&&f.push(I)}console.log("📊 Parsed Excel data:",{totalRows:w.length-1,validItems:f.length,headers:M,sampleItem:f[0]}),t(f)}catch(m){console.error("Error parsing Excel file:",m),s(new Error("Có lỗi xảy ra khi đọc file Excel"))}},n.onerror=()=>{s(new Error("Có lỗi xảy ra khi đọc file"))},n.readAsArrayBuffer(i)}),Ys=i=>{if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel","application/octet-stream"].includes(i.type)&&!i.name.match(/\.(xlsx|xls)$/i))return{isValid:!1,error:"File phải có định dạng Excel (.xlsx hoặc .xls)"};const s=10*1024*1024;return i.size>s?{isValid:!1,error:"File không được vượt quá 10MB"}:{isValid:!0}},Ve=[{sourceId:"10000045",name:"ZALO"},{sourceId:"10000049",name:"FACEBOOK"},{sourceId:"10000134",name:"SO"},{sourceId:"10000162",name:"CRM"},{sourceId:"10000165",name:"VNPAY"},{sourceId:"10000168",name:"GOJEK (GOVIET)"},{sourceId:"10000169",name:"ShopeeFood"},{sourceId:"10000171",name:"MANG VỀ"},{sourceId:"10000172",name:"TẠI CHỖ"},{sourceId:"10000176",name:"CALL CENTER"},{sourceId:"10000216",name:"O2O"},{sourceId:"10000253",name:"BEFOOD"}],Xs=()=>{const i=new st.Workbook;return i.creator="POS System",i.lastModifiedBy="POS System",i.created=new Date,i.modified=new Date,i},Js=()=>{const i=["item_uid","item_id","item_name","Nhóm món","Giá gốc","Vat (%)"],t=Ve.map(s=>`${s.name} [${s.sourceId}]`);return[...i,...t]},Zs=(i,t)=>{const n=i.map(m=>{const x=t.find(_=>_.id===m.item_type_uid),o=(x==null?void 0:x.item_type_name)||"Uncategory";return{item:m,itemTypeName:o}}).reduce((m,{item:x,itemTypeName:o})=>(m[o]||(m[o]=[]),m[o].push({item:x,itemTypeName:o}),m),{}),l=Object.keys(n).sort(),h=[];return l.forEach(m=>{const x=n[m];x.sort((o,_)=>o.item.item_name.localeCompare(_.item.item_name)),x.forEach(({item:o,itemTypeName:_})=>{const w=[o.id,o.item_id,o.item_name,_,o.ots_price||0,(o.ots_tax||0)*100];Ve.forEach(M=>{var f,k;const y=(k=(f=o.extra_data)==null?void 0:f.price_by_source)==null?void 0:k.find(p=>p.source_id===M.sourceId);w.push((y==null?void 0:y.price)||"")}),h.push(w)})}),h},en=(i,t,s)=>{const n=i.addWorksheet("Sheet"),l=Js(),h=Zs(t,s);return n.addRow(l).eachCell(o=>{o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},o.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},o.alignment={horizontal:"center",vertical:"middle"},o.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),h.forEach(o=>{n.addRow(o).eachCell(w=>{w.font={size:10},w.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}}})}),[40,15,25,15,12,10,...Ve.map(()=>15)].forEach((o,_)=>{n.getColumn(_+1).width=o}),n},tn=async(i,t,s)=>{try{const n=Xs();en(n,i,t);const l=await n.xlsx.writeBuffer(),h=new Blob([l],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),m=new Date().toISOString().slice(0,19).replace(/:/g,"-"),x=`import-price-by-source_${s}_${m}.xlsx`,o=window.URL.createObjectURL(h),_=document.createElement("a");return _.href=o,_.download=x,document.body.appendChild(_),_.click(),document.body.removeChild(_),window.URL.revokeObjectURL(o),Promise.resolve()}catch(n){return console.error("Error creating price by source Excel file:",n),Promise.reject(n)}};function sn({open:i,onOpenChange:t,data:s,storeUid:n}){const[l,h]=N.useState(s),[m,x]=N.useState(!1),{user:o,company:_}=ne(b=>b.auth),{selectedBrand:w}=pe(),{mutate:M,isPending:y}=Ie(),{data:f}=nt(n||"",!!n),{data:k=[]}=ce({skip_limit:!0,...n&&n!=="all"?{store_uid:n}:{}}),{data:p=[]}=we({skip_limit:!0}),{data:I=[]}=ke();N.useEffect(()=>{h(s)},[s]);const D=b=>{h(d=>d.filter((r,c)=>c!==b))},T=async()=>{if(!n||!_||!w){B.error("Thiếu thông tin cần thiết để cập nhật");return}x(!0);const b=l.map(d=>{const r=I.find(S=>S.unit_id===d.unit_id),c=k.find(S=>S.item_type_id===d.item_type_id||S.item_type_name===d.item_type_name),a=p.find(S=>S.item_class_id===d.item_class_id||S.item_class_name===d.item_class_name),g=I.find(S=>S.unit_id==="MON"),j=k.find(S=>S.item_type_name==="LOẠI KHÁC");return{id:d.id,item_id:d.item_id,item_name:d.item_name,description:d.description||"",ots_price:d.ots_price||0,ots_tax:(d.ots_tax||0)/100,ta_price:d.ots_price||0,ta_tax:(d.ots_tax||0)/100,time_sale_hour_day:Number(d.time_sale_hour_day??0),time_sale_date_week:Number(d.time_sale_date_week??0),allow_take_away:1,is_eat_with:d.is_eat_with||0,image_path:d.image_path||"",image_path_thumb:d.image_path?`${d.image_path}?width=185`:"",item_color:"",list_order:d.list_order||0,is_service:0,is_material:0,active:d.active||1,user_id:"",is_foreign:0,quantity_default:0,price_change:d.price_change||0,currency_type_id:"",point:0,is_gift:0,is_fc:0,show_on_web:0,show_price_on_web:0,cost_price:0,is_print_label:0,quantity_limit:0,is_kit:0,time_cooking:d.time_cooking||0,item_id_barcode:d.item_id_barcode||"",process_index:0,is_allow_discount:0,quantity_per_day:0,item_id_eat_with:"",is_parent:0,is_sub:0,item_id_mapping:String(d.sku||""),effective_date:0,expire_date:0,sort:d.list_order||1,extra_data:{formula_qrcode:d.inqr_formula||"",is_buffet_item:d.is_buffet_item||0,up_size_buffet:[],is_item_service:d.is_item_service||0,is_virtual_item:d.is_virtual_item||0,price_by_source:[],enable_edit_price:d.price_change||0,exclude_items_buffet:[],no_update_quantity_toping:d.no_update_quantity_toping||0},revision:0,unit_uid:(r==null?void 0:r.id)||(g==null?void 0:g.id)||"",unit_secondary_uid:null,item_type_uid:(c==null?void 0:c.id)||(j==null?void 0:j.id)||"",item_class_uid:(a==null?void 0:a.id)||void 0,source_uid:null,brand_uid:w.id,company_uid:_.id,customization_uid:"",is_fabi:1,deleted:!1,created_by:(o==null?void 0:o.email)||"",updated_by:(o==null?void 0:o.email)||"",deleted_by:null,created_at:Math.floor(Date.now()/1e3),updated_at:Math.floor(Date.now()/1e3),deleted_at:null,apply_with_store:2,store_uid:n,city_uid:(f==null?void 0:f.cityId)||""}});M(b,{onSuccess:()=>{x(!1),t(!1)},onError:d=>{console.error("Error updating items:",d),B.error(`Có lỗi xảy ra khi cập nhật món ăn: ${d}`),x(!1)}})},u=[{key:"item_id",label:"Mã món",width:"120px"},{key:"city_name",label:"Thành phố",width:"120px"},{key:"store_name",label:"Cửa hàng",width:"140px"},{key:"item_name",label:"Tên",width:"200px"},{key:"ots_price",label:"Giá",width:"100px"},{key:"active",label:"Trạng thái",width:"100px"},{key:"item_id_barcode",label:"Mã barcode",width:"120px"},{key:"is_eat_with",label:"Món ăn kèm",width:"120px"},{key:"no_update_quantity_toping",label:"Không cập nhật số lượng",width:"180px"},{key:"unit_name",label:"Đơn vị",width:"100px"},{key:"item_type_id",label:"Nhóm",width:"120px"},{key:"item_type_name",label:"Tên nhóm",width:"150px"},{key:"item_class_id",label:"Loại món",width:"120px"},{key:"item_class_name",label:"Tên loại",width:"150px"},{key:"description",label:"Mô tả",width:"200px"},{key:"sku",label:"SKU",width:"100px"},{key:"ots_tax",label:"VAT (%)",width:"80px"},{key:"time_cooking",label:"Thời gian chế biến (phút)",width:"180px"},{key:"price_change",label:"Cho phép sửa giá khi bán",width:"180px"},{key:"is_virtual_item",label:"Cấu hình món ảo",width:"150px"},{key:"is_item_service",label:"Cấu hình món dịch vụ",width:"180px"},{key:"is_buffet_item",label:"Cấu hình món ăn là vé buffet",width:"200px"},{key:"time_sale_hour_day",label:"Giờ",width:"80px"},{key:"time_sale_date_week",label:"Ngày",width:"80px"},{key:"list_order",label:"Thứ tự",width:"80px"},{key:"image_path",label:"Hình ảnh",width:"120px"},{key:"inqr_formula",label:"Công thức inQR cho máy pha trà",width:"220px"}];return e.jsx($,{open:i,onOpenChange:t,children:e.jsxs(U,{className:"max-h-[90vh] max-w-7xl sm:max-w-4xl",children:[e.jsx(W,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(Q,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})}),e.jsxs("div",{className:"space-y-4 overflow-hidden",children:[e.jsxs(ae,{className:"h-[60vh] w-full rounded-md border",children:[e.jsxs(je,{children:[e.jsx(ve,{className:"sticky top-0 z-10 bg-white",children:e.jsxs(X,{children:[e.jsx(re,{className:"w-12"}),u.map(b=>e.jsx(re,{style:{width:b.width},children:b.label},b.key))]})}),e.jsx(Ne,{children:l.map((b,d)=>e.jsxs(X,{children:[e.jsx(J,{children:e.jsx(E,{variant:"ghost",size:"icon",onClick:()=>D(d),className:"h-8 w-8 text-red-500 hover:text-red-700",children:e.jsx(De,{className:"h-4 w-4"})})}),u.map(r=>{var c;return e.jsxs(J,{style:{width:r.width},children:[r.key==="ots_price"&&e.jsxs("span",{className:"text-right",children:[(c=b[r.key])==null?void 0:c.toLocaleString("vi-VN")," ₫"]}),r.key==="active"&&e.jsx("span",{children:b[r.key]}),(r.key==="item_id"||r.key==="item_id_barcode")&&e.jsx("span",{className:"font-mono text-sm",children:b[r.key]}),r.key==="item_name"&&e.jsx("span",{className:"font-medium",children:b[r.key]}),(r.key==="is_eat_with"||r.key==="no_update_quantity_toping"||r.key==="price_change"||r.key==="is_virtual_item"||r.key==="is_item_service"||r.key==="is_buffet_item")&&e.jsx("span",{className:"text-center",children:b[r.key]}),r.key!=="ots_price"&&r.key!=="active"&&r.key!=="item_id"&&r.key!=="item_id_barcode"&&r.key!=="item_name"&&r.key!=="is_eat_with"&&r.key!=="no_update_quantity_toping"&&r.key!=="price_change"&&r.key!=="is_virtual_item"&&r.key!=="is_item_service"&&r.key!=="is_buffet_item"&&e.jsx("span",{children:b[r.key]||""})]},r.key)})]},d))})]}),e.jsx(q,{orientation:"horizontal"}),e.jsx(q,{orientation:"vertical"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx(E,{variant:"outline",onClick:()=>t(!1),children:"Đóng"}),e.jsx(E,{onClick:T,disabled:m||y,children:m||y?"Đang lưu...":"Lưu"})]})]})]})})}function nn({open:i,onOpenChange:t}){const[s,n]=N.useState(""),[l,h]=N.useState(""),[m,x]=N.useState(!1),[o,_]=N.useState(!1),[w,M]=N.useState([]),{company:y}=ne(c=>c.auth),{selectedBrand:f}=pe(),{data:k=[]}=ge(),{data:p=[]}=ce({...s&&s!=="all"?{store_uid:s}:{}}),{data:I=[]}=we({skip_limit:!0}),{data:D=[]}=ke(),{data:T=[]}=us(),u=k.map(c=>({value:c.id,label:c.name||c.id})),b=[{value:"all",label:"Tất cả nhóm món"},...p.map(c=>({value:c.id,label:c.item_type_name||c.id}))];N.useEffect(()=>{s&&h("all")},[s]);const d=async()=>{var c;try{if(!(y!=null&&y.id)||!(f!=null&&f.id)||!s){B.error("Vui lòng chọn cửa hàng trước khi tải file");return}x(!0);const a={skip_limit:"true",company_uid:y.id,brand_uid:f.id,store_uid:s};l&&l!=="all"&&(a.item_type_uid=l);const g=await Se.get("/mdata/v1/items",{params:a}),j=Array.isArray((c=g.data)==null?void 0:c.data)?g.data.data:[];if(j.length===0){B.error("Không có dữ liệu để xuất");return}const S=C=>{const F=k.find(K=>K.id===C);return{name:(F==null?void 0:F.name)||C,id:(F==null?void 0:F.id)||C}},v=C=>{const F=T.find(K=>K.id===C);return{name:(F==null?void 0:F.city_name)||C,id:(F==null?void 0:F.id)||C}},V=C=>{const F=p.find(K=>K.id===C);return{name:(F==null?void 0:F.item_type_name)||C,id:(F==null?void 0:F.item_type_id)||C}},R=C=>{const F=I.find(K=>K.id===C);return{name:(F==null?void 0:F.item_class_name)||C,id:(F==null?void 0:F.item_class_id)||C}},H=C=>{const F=D.find(K=>K.id===C);return{name:(F==null?void 0:F.unit_name)||C,id:(F==null?void 0:F.unit_id)||C}},O=j.map(C=>{var F,K,A,le;return{ID:C.id,"Mã món":C.item_id,"Thành phố":v(C.city_uid).name,"Cửa hàng":S(C.store_uid).name,Tên:C.item_name,Giá:C.ots_price,"Trạng thái":C.active,"Mã barcode":C.item_id_barcode,"Món ăn kèm":C.is_eat_with,"Không cập nhật số lượng món ăn kèm":(F=C.extra_data)==null?void 0:F.no_update_quantity_toping,"Đơn vị":H(C.unit_uid).id,Nhóm:V(C.item_type_uid).id,"Tên nhóm":V(C.item_type_uid).name,"Loại món":R(C.item_class_uid).id,"Tên loại":R(C.item_class_uid).name,"Mô tả":C.description,SKU:"","VAT (%)":C.ots_tax*100,"Thời gian chế biến (phút)":Math.round(C.time_cooking/6e4),"Cho phép sửa giá khi bán":C.price_change,"Cấu hình món ảo":(K=C.extra_data)==null?void 0:K.is_virtual_item,"Cấu hình món dịch vụ":(A=C.extra_data)==null?void 0:A.is_item_service,"Cấu hình món ăn là vé buffet":(le=C.extra_data)==null?void 0:le.is_buffet_item,Giờ:C.time_sale_hour_day,Ngày:C.time_sale_date_week,"Thứ tự":C.sort,"Hình ảnh":C.image_path,"Công thức inQR cho máy pha trà":""}}),ue=he.json_to_sheet(O),oe=he.book_new();he.book_append_sheet(oe,ue,"Items");const ie=`items_export_${new Date().toISOString().slice(0,19).replace(/:/g,"-")}.xlsx`;ms(oe,ie),B.success(`Đã tải xuống file ${ie}`)}catch{B.error("Có lỗi xảy ra khi tải file")}finally{x(!1)}},r=()=>{const c=document.createElement("input");c.type="file",c.accept=".xlsx,.xls",c.onchange=async a=>{var j;const g=(j=a.target.files)==null?void 0:j[0];if(g)try{const S=await g.arrayBuffer(),v=Oe(S,{type:"array"}),V=v.Sheets[v.SheetNames[0]],H=he.sheet_to_json(V).map(O=>({id:O.ID||"",item_id:O["Mã món"]||"",city_name:O["Thành phố"]||"",store_name:O["Cửa hàng"]||"",item_name:O.Tên||"",ots_price:O.Giá||0,active:Ue(O["Trạng thái"]),item_id_barcode:O["Mã barcode"]||"",is_eat_with:z(O["Món ăn kèm"]),no_update_quantity_toping:z(O["Không cập nhật số lượng món ăn kèm"]),unit_id:O["Đơn vị"]||"",item_type_id:O.Nhóm||"",item_type_name:O["Tên nhóm"]||"",item_class_id:O["Loại món"]||"",item_class_name:O["Tên loại"]||"",description:O["Mô tả"]||"",sku:O.SKU||"",ots_tax:(O["VAT (%)"]||0)/100,time_cooking:(O["Thời gian chế biến (phút)"]||0)*6e4,price_change:z(O["Cho phép sửa giá khi bán"]),is_virtual_item:z(O["Cấu hình món ảo"]),is_item_service:z(O["Cấu hình món dịch vụ"]),is_buffet_item:z(O["Cấu hình món ăn là vé buffet"]),time_sale_hour_day:O.Giờ||0,time_sale_date_week:O.Ngày||0,list_order:O["Thứ tự"]||0,image_path:O["Hình ảnh"]||"",inqr_formula:O["Công thức inQR cho máy pha trà"]||"",originalData:O}));M(H),_(!0),t(!1)}catch{B.error("Có lỗi xảy ra khi đọc file Excel")}},c.click()};return e.jsxs($,{open:i,onOpenChange:t,children:[e.jsxs(U,{className:"max-w-2xl",children:[e.jsx(W,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(Q,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"mb-4 text-lg font-medium",children:"Bước 1. Chỉnh bộ lọc để xuất file"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx("div",{children:e.jsxs(Z,{value:s,onValueChange:n,children:[e.jsx(ee,{className:"w-full",children:e.jsx(te,{placeholder:"Chọn cửa hàng"})}),e.jsx(se,{children:u.map(c=>e.jsx(G,{value:c.value,children:c.label},c.value))})]})}),e.jsx("div",{children:e.jsxs(Z,{value:l,onValueChange:h,children:[e.jsx(ee,{className:"w-full",children:e.jsx(te,{placeholder:"Tất cả nhóm món"})}),e.jsx(se,{children:b.map(c=>e.jsx(G,{value:c.value,children:c.label},c.value))})]})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-4 text-lg font-medium",children:"Bước 2. Tải file dữ liệu"}),e.jsxs("div",{className:"flex items-center justify-between rounded-lg border p-4",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Tải xuống"}),e.jsx(E,{variant:"outline",size:"icon",onClick:d,disabled:m||!s,className:"h-10 w-10",children:e.jsx(Pe,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-4 text-lg font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-muted-foreground text-sm",children:"Không sửa các cột:"}),e.jsx("p",{className:"font-mono text-sm text-blue-600",children:"ID, Mã món, Tên (Với món sửa từ món gốc), Mô tả, Thành phố, SKU, Cửa hàng, Đơn vị, Tên nhóm, Tên loại."})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-4 text-lg font-medium",children:"Bước 4. Tải file lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-muted-foreground text-sm",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),e.jsxs(E,{onClick:r,className:"flex items-center gap-2",children:[e.jsx(Be,{className:"h-4 w-4"}),"Tải file lên"]})]})]})]})]}),e.jsx(sn,{open:o,onOpenChange:_,data:w,storeUid:s})]})}function an({open:i,onOpenChange:t,data:s,storeUid:n}){const[l,h]=N.useState(s),[m,x]=N.useState(!1),{company:o}=ne(T=>T.auth),{selectedBrand:_}=pe(),{bulkCreateItemsInStore:w,isBulkCreating:M}=Ct(),{data:y}=nt(n||"",!!n),{data:f=[]}=ce({skip_limit:!0,...n&&n!=="all"?{store_uid:n}:{}}),{data:k=[]}=we({skip_limit:!0}),{data:p=[]}=ke();N.useEffect(()=>{h(s)},[s]);const I=async()=>{if(!n||!(o!=null&&o.id)||!(_!=null&&_.id)){B.error("Thiếu thông tin cửa hàng hoặc thương hiệu");return}if(l.length===0){B.error("Không có dữ liệu để import");return}x(!0);try{const T=l.map(u=>{const b=p.find(g=>g.unit_id===u.unit_id),d=f.find(g=>g.item_type_id===u.item_type_id||g.item_type_name===u.item_type_name),r=k.find(g=>g.item_class_id===u.item_class_id||g.item_class_name===u.item_class_name),c=p.find(g=>g.unit_id==="MON"),a=f.find(g=>g.item_type_name==="LOẠI KHÁC");return{store_uid:n,apply_with_store:2,company_uid:o.id,brand_uid:_.id,city_uid:(y==null?void 0:y.cityId)||"",item_id:u.item_id,unit_uid:(b==null?void 0:b.id)||(c==null?void 0:c.id)||"",ots_price:u.ots_price||0,ta_price:u.ots_price||0,ots_tax:(u.ots_tax||0)/100,ta_tax:(u.ots_tax||0)/100,item_name:u.item_name,item_id_barcode:u.item_id_barcode||"",is_eat_with:u.is_eat_with||0,item_type_uid:(d==null?void 0:d.id)||(a==null?void 0:a.id)||"",item_class_uid:(r==null?void 0:r.id)||null,description:u.description||"",item_id_mapping:String(u.sku||""),time_cooking:u.time_cooking||0,time_sale_date_week:u.time_sale_date_week||0,time_sale_hour_day:u.time_sale_hour_day||0,sort:u.list_order||1,image_path_thumb:"",image_path:u.image_path||"",extra_data:{no_update_quantity_toping:u.no_update_quantity_toping||0,enable_edit_price:u.price_change||0,is_virtual_item:u.is_virtual_item||0,is_item_service:u.is_item_service||0,is_buffet_item:u.is_buffet_item||0}}});await w(T),t(!1)}catch(T){console.error("Error creating items:",T),B.error("Có lỗi xảy ra khi tạo món ăn")}finally{x(!1)}},D=[{key:"item_name",label:"Tên",width:"200px"},{key:"ots_price",label:"Giá",width:"100px"},{key:"item_id",label:"Mã món",width:"120px"},{key:"item_id_barcode",label:"Mã barcode",width:"120px"},{key:"is_eat_with",label:"Món ăn kèm",width:"120px"},{key:"no_update_quantity_toping",label:"Không cập nhật số lượng món ăn kèm",width:"220px"},{key:"item_type_id",label:"Nhóm",width:"120px"},{key:"item_class_id",label:"Loại món",width:"120px"},{key:"description",label:"Mô tả",width:"200px"},{key:"sku",label:"SKU",width:"100px"},{key:"unit_id",label:"Đơn vị",width:"100px"},{key:"ots_tax",label:"VAT (%)",width:"80px"},{key:"time_cooking",label:"Thời gian chế biến (phút)",width:"180px"},{key:"price_change",label:"Cho phép sửa giá khi bán",width:"180px"},{key:"is_virtual_item",label:"Cấu hình món ảo",width:"150px"},{key:"is_item_service",label:"Cấu hình món dịch vụ",width:"180px"},{key:"is_buffet_item",label:"Cấu hình món ăn là vé buffet",width:"200px"},{key:"time_sale_date_week",label:"Ngày",width:"80px"},{key:"time_sale_hour_day",label:"Giờ",width:"80px"},{key:"image_path",label:"Hình ảnh",width:"120px"},{key:"inqr_formula",label:"Công thức inQR cho máy pha trà",width:"220px"}];return e.jsx($,{open:i,onOpenChange:t,children:e.jsxs(U,{className:"max-h-[90vh] max-w-7xl sm:max-w-4xl",children:[e.jsx(W,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(Q,{className:"text-xl font-semibold",children:"Thêm mới"})}),e.jsxs("div",{className:"space-y-4 overflow-hidden",children:[e.jsxs(ae,{className:"h-[60vh] w-full rounded-md border",children:[e.jsxs(je,{children:[e.jsx(ve,{className:"sticky top-0 z-10 bg-white",children:e.jsxs(X,{children:[e.jsx(re,{className:"w-16 text-center",children:"Thứ tự"}),D.map(T=>e.jsx(re,{style:{width:T.width},children:T.label},T.key))]})}),e.jsx(Ne,{children:l.map((T,u)=>e.jsxs(X,{children:[e.jsx(J,{className:"text-center font-medium",children:u+1}),D.map(b=>{var d;return e.jsx(J,{style:{width:b.width},children:b.key==="ots_price"?e.jsxs("span",{className:"text-right",children:[(d=T[b.key])==null?void 0:d.toLocaleString("vi-VN")," ₫"]}):b.key==="active"?e.jsx("span",{children:T[b.key]}):b.key==="item_id"||b.key==="item_id_barcode"?e.jsx("span",{className:"font-mono text-sm",children:T[b.key]}):b.key==="item_name"?e.jsx("span",{className:"font-medium",children:T[b.key]}):b.key==="is_eat_with"||b.key==="no_update_quantity_toping"||b.key==="price_change"||b.key==="is_virtual_item"||b.key==="is_item_service"||b.key==="is_buffet_item"?e.jsx("span",{className:"text-center",children:T[b.key]}):e.jsx("span",{children:T[b.key]||""})},b.key)})]},u))})]}),e.jsx(q,{orientation:"horizontal"}),e.jsx(q,{orientation:"vertical"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx(E,{variant:"outline",onClick:()=>t(!1),children:"Đóng"}),e.jsx(E,{onClick:I,disabled:m||M||!n,children:m||M?"Đang lưu...":"Lưu"})]})]})]})})}function ln({open:i,onOpenChange:t,storeUid:s}){const[n,l]=N.useState(!1),[h,m]=N.useState([]),{data:x}=ce(),{data:o}=we(),{data:_}=ke(),w=async()=>{try{await Us({itemTypes:x||[],itemClasses:o||[],units:_||[]}),B.success("Đã tải xuống file mẫu thành công")}catch(y){console.error("Error creating Excel file:",y),B.error("Có lỗi xảy ra khi tạo file mẫu")}},M=()=>{const y=document.createElement("input");y.type="file",y.accept=".xlsx,.xls",y.onchange=async f=>{var p;const k=(p=f.target.files)==null?void 0:p[0];if(k)try{const I=await k.arrayBuffer(),D=Oe(I,{type:"array"});let T;D.SheetNames.includes("Menu")?T=D.Sheets.Menu:T=D.Sheets[D.SheetNames[0]];const u=he.sheet_to_json(T),b=new Set,d=u.map(r=>{const c=(r["Mã món"]??"").toString().trim();c&&b.add(c);let a=c;if(!a){let g=He();for(;b.has(g);)g=He();b.add(g),a=g}return{id:r.ID||"",item_id:a,city_name:r["Thành phố"]||"",store_name:r["Cửa hàng"]||"",item_name:r.Tên||"",ots_price:r.Giá||0,active:Ue(r["Trạng thái"]),item_id_barcode:r["Mã barcode"]||"",is_eat_with:z(r["Món ăn kèm"]),no_update_quantity_toping:z(r["Không cập nhật số lượng món ăn kèm"]),unit_id:r["Đơn vị"]||"",item_type_id:r.Nhóm||"",item_type_name:r["Tên nhóm"]||"",item_class_id:r["Loại món"]||"",item_class_name:r["Tên loại"]||"",description:r["Mô tả"]||"",sku:r.SKU||"",ots_tax:(r["VAT (%)"]||0)/100,time_cooking:(r["Thời gian chế biến (phút)"]||0)*6e4,price_change:z(r["Cho phép sửa giá khi bán"]),is_virtual_item:z(r["Cấu hình món ảo"]),is_item_service:z(r["Cấu hình món dịch vụ"]),is_buffet_item:z(r["Cấu hình món ăn là vé buffet"]),time_sale_hour_day:r.Giờ||0,time_sale_date_week:r.Ngày||0,list_order:r["Thứ tự"]||0,image_path:r["Hình ảnh"]||"",inqr_formula:r["Công thức inQR cho máy pha trà"]||"",originalData:r}});m(d),l(!0),t(!1)}catch{B.error("Có lỗi xảy ra khi đọc file Excel")}},y.click()};return e.jsxs(e.Fragment,{children:[e.jsx($,{open:i,onOpenChange:t,children:e.jsxs(U,{className:"max-w-2xl",children:[e.jsx(W,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(Q,{className:"text-xl font-semibold",children:"Thêm món"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Tải file mẫu"}),e.jsxs(E,{variant:"outline",onClick:w,className:"flex items-center gap-2",children:[e.jsx(Pe,{className:"h-4 w-4"}),"Tải xuống"]})]})}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Thêm món vào file"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsx("p",{children:e.jsx("strong",{children:"Các món được thêm vào chỉ được áp dụng tại: Tutimi-Bình Lợi"})}),e.jsxs("p",{children:["Không được để trống các cột ",e.jsx("span",{className:"font-medium text-blue-600",children:"Tên"}),"."]}),e.jsxs("p",{children:["Các cột còn lại có thể để trống, để gán nhóm, loại, đơn vị cho món: Nhập mã nhóm, mã loại, mã đơn vị đã có vào cột ",e.jsx("span",{className:"font-medium text-blue-600",children:"Nhóm"}),","," ",e.jsx("span",{className:"font-medium text-blue-600",children:"Loại món"}),"."]}),e.jsxs("p",{children:["Mã đơn vị món có thể xem trong sheet ",e.jsx("span",{className:"font-medium text-blue-600",children:"template"})," của file mẫu để tham khảo."]})]})]})}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Tải file thực đơn lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Sau khi đã điền đầy đủ thực đơn bạn có thể tải file lên"}),e.jsxs(E,{onClick:M,className:"flex items-center gap-2",children:[e.jsx(Be,{className:"h-4 w-4"}),"Tải file lên"]})]})]})})]})]})}),e.jsx(an,{open:n,onOpenChange:l,data:h,storeUid:s})]})}function rn({open:i,onOpenChange:t,onConfirm:s,title:n,isLoading:l=!1}){return e.jsx($,{open:i,onOpenChange:t,children:e.jsxs(U,{className:"max-w-md",children:[e.jsx(W,{children:e.jsx(Q,{className:"text-center",children:n})}),e.jsxs(Ee,{className:"flex justify-between gap-4",children:[e.jsx(E,{variant:"outline",onClick:()=>t(!1),disabled:l,className:"flex-1",children:"Hủy"}),e.jsx(E,{onClick:s,disabled:l,className:"flex-1 bg-green-600 hover:bg-green-700",children:l?"Đang đồng bộ...":"Đồng bộ"})]})]})})}function cn({open:i,onOpenChange:t,sourceStoreUid:s,selectedItems:n,onItemsChange:l,onSave:h}){const[m,x]=N.useState(""),[o,_]=N.useState("all"),[w,M]=N.useState(!1),{data:y=[],isLoading:f}=Fe({params:{store_uid:s},enabled:i&&!!s}),k=y.filter(r=>{var g;const c=(g=r.item_name)==null?void 0:g.toLowerCase().includes(m.toLowerCase()),a=o==="all"||r.item_type_uid===o;return c&&a}),p=Array.from(new Set(y.map(r=>r.item_type_uid))).map(r=>{const c=y.find(a=>a.item_type_uid===r);return{uid:r,name:(c==null?void 0:c.item_type_uid)||r}}),I=()=>{const r=k.filter(c=>c.active===1);n.length===r.length?l([]):l(r.map(c=>c.item_id))},D=r=>{n.includes(r)?l(n.filter(c=>c!==r)):l([...n,r])},T=()=>{h(),t(!1)},u=y.filter(r=>n.includes(r.item_id)),b=k.filter(r=>r.active===1),d=b.length>0&&n.length===b.length;return e.jsx($,{open:i,onOpenChange:t,children:e.jsxs(U,{className:"flex h-[90vh] max-w-4xl flex-col",children:[e.jsx(W,{className:"flex-shrink-0",children:e.jsx(Q,{children:"Chọn món ăn"})}),e.jsxs("div",{className:"flex min-h-0 flex-1 flex-col space-y-4",children:[e.jsxs("div",{className:"flex flex-shrink-0 gap-4",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(at,{className:"text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform"}),e.jsx(Me,{placeholder:"Tìm kiếm",value:m,onChange:r=>x(r.target.value),className:"pl-10"})]}),e.jsxs(Z,{value:o,onValueChange:_,children:[e.jsx(ee,{className:"w-48",children:e.jsx(te,{placeholder:"Tất cả nhóm món"})}),e.jsxs(se,{children:[e.jsx(G,{value:"all",children:"Tất cả nhóm món"}),p.map(r=>e.jsx(G,{value:r.uid,children:r.name},r.uid))]})]})]}),e.jsx("div",{className:"flex-shrink-0",children:e.jsxs(os,{open:w,onOpenChange:M,children:[e.jsxs("div",{className:"bg-muted/50 flex items-center space-x-2 rounded p-2",children:[e.jsx(xe,{id:"select-all",checked:d,onCheckedChange:I}),e.jsx(ds,{asChild:!0,children:e.jsxs("button",{className:"hover:text-primary flex items-center space-x-2 text-sm font-medium",children:[e.jsxs("span",{children:["Đã chọn ",n.length]}),n.length>0&&e.jsx(cs,{className:`h-4 w-4 transition-transform ${w?"rotate-180":""}`})]})})]}),n.length>0&&e.jsx(hs,{className:"mt-2",children:e.jsx("div",{className:"bg-muted/30 max-h-32 overflow-y-auto rounded-md p-2",children:e.jsx("div",{className:"space-y-1",children:u.map(r=>e.jsxs("div",{className:"flex items-center space-x-2 text-sm",children:[e.jsx(xe,{checked:!0,onCheckedChange:()=>D(r.item_id),className:"h-3 w-3"}),e.jsx("span",{className:"flex-1 truncate",children:r.item_name})]},r.id))})})})]})}),e.jsx("div",{className:"min-h-0 flex-1",children:e.jsx(ae,{className:"h-full",children:e.jsx("div",{className:"space-y-2 pr-4",children:f?e.jsx("div",{className:"py-8 text-center",children:"Đang tải..."}):k.length===0?e.jsx("div",{className:"text-muted-foreground py-8 text-center",children:"Không tìm thấy món ăn nào"}):k.map(r=>{const c=r.active===1;return e.jsxs("div",{className:`flex items-center space-x-3 rounded border p-3 ${c?"hover:bg-muted/50":"bg-muted/20"}`,children:[e.jsx(xe,{id:r.id,checked:n.includes(r.item_id),onCheckedChange:()=>D(r.item_id),disabled:!c,className:c?"":"opacity-50"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("label",{htmlFor:r.id,className:`block truncate text-sm font-medium ${c?"cursor-pointer":"text-muted-foreground cursor-not-allowed"}`,children:r.item_name}),e.jsx("p",{className:"text-muted-foreground mt-1 truncate text-xs",children:r.item_type_uid})]}),!c&&e.jsx("div",{className:"rounded-full bg-red-500 px-2 py-1 text-xs font-medium text-white",children:"Deactive"})]},r.id)})})})})]}),e.jsxs(Ee,{className:"flex-shrink-0",children:[e.jsx(E,{variant:"outline",onClick:()=>t(!1),children:"Hủy"}),e.jsx(E,{onClick:T,className:"bg-blue-600 hover:bg-blue-700",children:"Xong"})]})]})})}function on({open:i,onOpenChange:t}){const[s,n]=N.useState(""),[l,h]=N.useState([]),[m,x]=N.useState([]),[o,_]=N.useState(!1),[w,M]=N.useState(!1),{data:y=[],isLoading:f}=ge({enabled:i}),k=St(),{company:p}=ne(a=>a.auth),{selectedBrand:I}=pe();N.useEffect(()=>{i||(n(""),h([]),x([]),_(!1),M(!1))},[i]);const D=y.map(a=>({label:a.name,value:a.id})),T=D.filter(a=>a.value!==s),u=()=>{if(!s){B.error("Vui lòng chọn cửa hàng nguồn trước");return}_(!0)},b=()=>{if(!s){B.error("Vui lòng chọn cửa hàng nguồn");return}if(l.length===0){B.error("Vui lòng chọn ít nhất một cửa hàng đích");return}if(m.length===0){B.error("Vui lòng chọn ít nhất một món ăn");return}M(!0)},d=async()=>{try{if(!(I!=null&&I.id)){B.error("Thiếu thông tin công ty hoặc thương hiệu");return}for(const a of l)await k.mutateAsync({company_uid:p==null?void 0:p.id,brand_uid:I.id,store_uid_root:s,store_uid_target:a,list_item_id:m,menu_type:"store"});B.success("Đồng bộ thực đơn thành công!"),M(!1),t(!1)}catch(a){console.error("Error syncing menu:",a)}},r=()=>{const a=y.find(g=>g.id===s);return a?a.name:""},c=()=>l.map(a=>{const g=y.find(j=>j.id===a);return g?g.name:""}).filter(Boolean).join(", ");return e.jsxs(e.Fragment,{children:[e.jsx($,{open:i,onOpenChange:t,children:e.jsxs(U,{className:"max-w-2xl",children:[e.jsx(W,{children:e.jsx(Q,{children:"Đồng bộ thực đơn"})}),e.jsxs("div",{className:"mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4",children:[e.jsx("p",{className:"text-sm text-yellow-800",children:"Nếu không chọn món, thực đơn tại cửa hàng đích sẽ bị xoá để đồng bộ dữ liệu!"}),e.jsxs("p",{className:"mt-1 text-sm text-yellow-800",children:["Trước khi đồng bộ món, để đảm bảo dữ liệu tại cửa hàng"," ",e.jsx("span",{className:"font-semibold text-red-600",children:"A Hưng"}),", hãy đồng bộ món tại thành phố"," ",e.jsx("span",{className:"font-semibold text-red-600",children:"Hà Nội"}),"!"]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Cửa hàng nguồn"}),e.jsxs(Z,{value:s,onValueChange:n,children:[e.jsx(ee,{children:e.jsx(te,{placeholder:"Chọn cửa hàng"})}),e.jsx(se,{children:f?e.jsx(G,{disabled:!0,value:"loading",children:"Đang tải..."}):D.map(a=>e.jsx(G,{value:a.value,children:a.label},a.value))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Cửa hàng đích"}),e.jsx(tt,{options:T,value:l,onValueChange:h,placeholder:"Tìm kiếm",variant:"default",animation:2,maxCount:3})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Chọn món"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(E,{variant:"outline",onClick:u,className:"flex-1 justify-start text-left",disabled:!s,children:m.length>0?`Đã chọn ${m.length} món`:"Tất cả các món"}),e.jsx(E,{onClick:b,className:"bg-green-600 hover:bg-green-700",disabled:k.isPending,children:k.isPending?"Đang đồng bộ...":"Đồng bộ"})]})]})]})]})}),e.jsx(cn,{open:o,onOpenChange:_,sourceStoreUid:s,selectedItems:m,onItemsChange:x,onSave:()=>_(!1)}),e.jsx(rn,{open:w,onOpenChange:M,onConfirm:d,title:`Bạn có muốn đồng bộ ${m.length>0?`${m.length} món`:"bộ thực đơn"} của cửa hàng ${r()} tới ${c()}?`,isLoading:k.isPending})]})}function dn({open:i,onOpenChange:t,data:s,originalItems:n,sources:l}){const[h,m]=N.useState(s),{bulkUpdatePriceBySource:x,isUpdating:o}=Cs();N.useEffect(()=>{m(s)},[s]);const _=f=>{m(k=>k.filter((p,I)=>I!==f))},w=async()=>{if(h.length===0){B.error("Không có dữ liệu để lưu");return}try{await x({previewItems:h,originalItems:n,sources:l}),t(!1)}catch(f){console.error("Error saving price by source configuration:",f)}},M=[{key:"item_uid",label:"item_uid",width:"280px"},{key:"item_id",label:"item_id",width:"120px"},{key:"item_name",label:"item_name",width:"200px"},{key:"ots_price",label:"Giá gốc",width:"100px"},{key:"ots_tax",label:"Vat",width:"80px"},{key:"ZALO [10000045]",label:"ZALO [10000045]",width:"120px"},{key:"FACEBOOK [10000049]",label:"FACEBOOK [10000049]",width:"140px"},{key:"SO [10000134]",label:"SO [10000134]",width:"120px"},{key:"CRM [10000162]",label:"CRM [10000162]",width:"120px"},{key:"VNPAY [10000165]",label:"VNPAY [10000165]",width:"120px"},{key:"GOJEK (GOVIET) [10000168]",label:"GOJEK (GOVIET) [10000168]",width:"180px"},{key:"ShopeeFood [10000169]",label:"ShopeeFood [10000169]",width:"160px"},{key:"MANG VỀ [10000171]",label:"MANG VỀ [10000171]",width:"140px"},{key:"TẠI CHỖ [10000172]",label:"TẠI CHỖ [10000172]",width:"140px"},{key:"CALL CENTER [10000176]",label:"CALL CENTER [10000176]",width:"160px"},{key:"O2O [10000216]",label:"O2O [10000216]",width:"120px"},{key:"BEFOOD [10000253]",label:"BEFOOD [10000253]",width:"140px"}],y=f=>{if(f==null||f==="")return"";const k=typeof f=="string"?parseFloat(f):f;return isNaN(k)?"":k.toLocaleString("vi-VN")};return e.jsx($,{open:i,onOpenChange:t,children:e.jsxs(U,{className:"max-h-[90vh] max-w-7xl sm:max-w-4xl",children:[e.jsx(W,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(Q,{className:"text-xl font-semibold",children:"Thêm cấu hình giá món theo nguồn đơn"})}),e.jsxs("div",{className:"flex h-full flex-col space-y-4 overflow-hidden",children:[e.jsxs("div",{className:"flex-shrink-0 text-sm text-gray-600",children:["Tổng số món: ",e.jsx("span",{className:"font-medium",children:h.length})]}),e.jsx("div",{className:"min-h-0 flex-1",children:e.jsxs(ae,{className:"h-[60vh] w-full rounded-md border",children:[e.jsxs(je,{children:[e.jsx(ve,{className:"sticky top-0 z-10 bg-white",children:e.jsxs(X,{children:[e.jsx(re,{className:"w-12"}),M.map(f=>e.jsx(re,{style:{width:f.width},children:f.label},f.key))]})}),e.jsx(Ne,{children:h.map((f,k)=>e.jsxs(X,{children:[e.jsx(J,{children:e.jsx(E,{variant:"ghost",size:"icon",onClick:()=>_(k),className:"h-8 w-8 text-red-500 hover:text-red-700",children:e.jsx(De,{className:"h-4 w-4"})})}),M.map(p=>e.jsxs(J,{style:{width:p.width},children:[p.key==="item_uid"&&e.jsx("span",{className:"font-mono text-xs text-gray-600",children:f[p.key]}),p.key==="item_id"&&e.jsx("span",{className:"font-mono text-sm",children:f[p.key]}),p.key==="item_name"&&e.jsx("span",{className:"font-medium",children:f[p.key]}),p.key==="item_type_name"&&e.jsx("span",{className:"text-sm",children:f[p.key]}),p.key==="ots_price"&&e.jsx("span",{className:"text-right font-medium",children:y(f[p.key])}),p.key==="ots_tax"&&e.jsx("span",{className:"text-center",children:f[p.key]}),p.key.includes("[")&&e.jsx("span",{className:"text-right font-medium text-blue-600",children:f[p.key]?`${y(f[p.key])}`:""}),!p.key.includes("[")&&p.key!=="item_uid"&&p.key!=="item_id"&&p.key!=="item_name"&&p.key!=="item_type_name"&&p.key!=="ots_price"&&p.key!=="ots_tax"&&e.jsx("span",{children:f[p.key]||""})]},p.key))]},k))})]}),e.jsx(q,{orientation:"horizontal"}),e.jsx(q,{orientation:"vertical"})]})}),e.jsxs("div",{className:"flex flex-shrink-0 items-center justify-between border-t pt-4",children:[e.jsx(E,{variant:"outline",onClick:()=>t(!1),children:"Đóng"}),e.jsx(E,{onClick:w,disabled:o||h.length===0,children:o?"Đang lưu...":"Lưu"})]})]})]})})}function hn({open:i,onOpenChange:t,stores:s}){var d;const[n,l]=N.useState(""),[h,m]=N.useState(null),[x,o]=N.useState(!1),[_,w]=N.useState(!1),[M,y]=N.useState([]),[f,k]=N.useState(!1),{items:p,sources:I,itemTypes:D,isLoading:T}=ws(n,!!n),u=async()=>{if(!n){B.error("Vui lòng chọn cửa hàng trước");return}if(T){B.error("Đang tải dữ liệu, vui lòng chờ...");return}if(!p.length){B.error("Không có món nào trong cửa hàng này");return}try{o(!0);const r=s.find(a=>a.id===n),c=(r==null?void 0:r.name)||"Unknown";await tn(p,D,c),B.success("Đã tải xuống file template thành công")}catch(r){console.error("Error downloading template:",r),B.error("Có lỗi xảy ra khi tải xuống file template")}finally{o(!1)}},b=()=>{if(!n){B.error("Vui lòng chọn cửa hàng trước");return}const r=document.createElement("input");r.type="file",r.accept=".xlsx,.xls",r.onchange=async c=>{var j;const a=(j=c.target.files)==null?void 0:j[0];if(!a)return;const g=Ys(a);if(!g.isValid){B.error(g.error);return}try{w(!0),m(a);const S=await Qs(a);if(S.length===0){B.error("File không có dữ liệu hợp lệ");return}y(S),k(!0),t(!1),B.success(`Đã xử lý file thành công: ${S.length} món`)}catch(S){console.error("Error processing file:",S),B.error("Có lỗi xảy ra khi xử lý file")}finally{w(!1)}},r.click()};return e.jsxs($,{open:i,onOpenChange:t,children:[e.jsxs(U,{className:"max-w-2xl",children:[e.jsx(W,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(Q,{className:"text-xl font-semibold",children:"Thêm cấu hình giá món theo nguồn đơn"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 text-lg font-medium",children:"Bước 1. Chọn cửa hàng"}),e.jsxs(Z,{value:n,onValueChange:l,children:[e.jsx(ee,{className:"w-full",children:e.jsx(te,{placeholder:"Chọn cửa hàng"})}),e.jsx(se,{children:s.map(r=>e.jsx(G,{value:r.id,children:r.name},r.id))})]})]}),e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 text-lg font-medium",children:"Bước 2. Tải file dữ liệu để lấy món và nguồn đã có"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex flex-col",children:e.jsx("span",{className:"text-sm text-gray-600",children:"Tải xuống"})}),e.jsxs(E,{variant:"outline",onClick:u,disabled:x||!n||T,className:"flex items-center gap-2",children:[e.jsx(Pe,{className:"h-4 w-4"}),x?"Đang tải...":T?"Đang tải dữ liệu...":"Tải xuống"]})]})]}),e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 text-lg font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("p",{children:["Nhập giá theo nguồn tương ứng với từng món vào cột ",e.jsx("strong",{children:"price"}),"."]}),e.jsx("p",{children:"Bỏ trống hoặc xoá dòng với những nguồn không có cấu hình giá."}),e.jsx("p",{children:e.jsx("strong",{className:"text-red-600",children:"Không sửa các cột item_uid, item_name."})})]})]}),e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 text-lg font-medium",children:"Bước 4. Tải file lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),h&&e.jsxs("span",{className:"mt-1 text-xs text-gray-500",children:["File đã chọn: ",h.name]})]}),e.jsxs(E,{onClick:b,disabled:!n||_,className:"flex items-center gap-2",children:[e.jsx(Be,{className:"h-4 w-4"}),_?"Đang xử lý...":"Tải file lên"]})]})]})]})]}),e.jsx(dn,{open:f,onOpenChange:k,data:M,originalItems:p,sources:I,storeName:(d=s.find(r=>r.id===n))==null?void 0:d.name})]})}function mn({itemTypes:i,selectedStoreUid:t,selectedItemTypeUid:s,onSelect:n,className:l,onOrderChange:h,onOrderChangeDetailed:m}){const x=lt(be(ct,{activationConstraint:{distance:6}}),be(rt,{coordinateGetter:We})),[o,_]=N.useState([]);N.useEffect(()=>{i&&i.length>0?_(i.map(y=>({id:y.id,item_type_name:y.item_type_name,store_uid:y.store_uid??null}))):_([])},[i]);const w=N.useMemo(()=>o.map(y=>y.id),[o]),M=y=>{const{active:f,over:k}=y;!k||f.id===k.id||_(p=>{const I=p.findIndex(j=>j.id===String(f.id)),D=p.findIndex(j=>j.id===String(k.id));if(I===-1||D===-1)return p;const T=j=>{var S;return((S=p[j])==null?void 0:S.store_uid)===t};if(!T(I))return p;const u=p.map((j,S)=>S).filter(j=>T(j));if(u.length<=1)return p;const b=u.indexOf(I);if(b===-1)return p;let d=u.findIndex(j=>j>=D);if(d===-1&&(d=u.length-1),!T(D)){let j=-1;for(let v=u.length-1;v>=0;v--)if(u[v]<D){j=v;break}const S=u.findIndex(v=>v>D);if(j===-1&&S===-1)return p;if(j===-1)d=S;else if(S===-1)d=j;else{const v=Math.abs(D-u[j]),V=Math.abs(u[S]-D);d=v<=V?j:S}}const r=u.map(j=>p[j]),c=Ye(r,b,d),a=p.slice();let g=0;for(let j=0;j<a.length;j++)T(j)&&(a[j]=c[g++]);return h&&h(a.map(j=>j.id)),m&&m(a),a})};return e.jsx("div",{className:l,children:e.jsxs(ae,{className:"h-[50vh] w-full",children:[e.jsx(ot,{sensors:x,collisionDetection:dt,onDragEnd:M,children:e.jsx(Qe,{items:w,strategy:Tt,children:e.jsx("div",{className:"space-y-1 p-2",children:o.map(y=>e.jsx(un,{itemType:y,selectedStoreUid:t,isSelected:s===y.id,onSelect:()=>n(y.id)},y.id))})})}),e.jsx(q,{orientation:"vertical"})]})})}function un({itemType:i,selectedStoreUid:t,isSelected:s,onSelect:n}){const l=i.store_uid===t,{attributes:h,listeners:m,setNodeRef:x,transform:o,transition:_,isDragging:w}=Xe({id:i.id}),M={transform:ht.Transform.toString(o),transition:_,opacity:w?.6:1,zIndex:w?1e3:1,cursor:l?"move":"default"};return e.jsx("div",{ref:x,style:M,...h,...l?m:{},children:e.jsx("div",{onClick:n,className:`w-full rounded p-2 text-left text-sm transition-colors ${s?"bg-blue-100 text-blue-900":"hover:bg-gray-100"}`,children:e.jsxs("div",{className:"flex items-center gap-2",children:[i.store_uid===null?e.jsx(ys,{size:16,className:"text-gray-600"}):i.store_uid===t?e.jsx(_s,{size:16,className:"cursor-move text-gray-600"}):null,e.jsx("span",{children:i.item_type_name})]})})})}function xn({item:i}){var o;const{attributes:t,listeners:s,setNodeRef:n,transform:l,transition:h,isDragging:m}=Xe({id:i.id}),x={transform:ht.Transform.toString(l),transition:h,opacity:m?.5:1,zIndex:m?1e3:1};return e.jsx("div",{ref:n,style:x,...t,...s,children:e.jsx(gs,{className:"cursor-move transition-shadow hover:shadow-md",children:e.jsxs(fs,{className:"p-3",children:[e.jsx("div",{className:"mb-2 flex aspect-square items-center justify-center rounded-md bg-gray-200",children:i.image_path?e.jsx("img",{src:i.image_path,alt:i.item_name,className:"h-full w-full rounded-md object-cover"}):e.jsx("span",{className:"text-2xl text-gray-400",children:"🍽️"})}),e.jsx("h4",{className:"mb-1 overflow-hidden text-sm font-medium text-ellipsis",style:{display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical"},children:i.item_name}),e.jsxs("p",{className:"text-sm text-gray-600",children:[(o=i.ots_price)==null?void 0:o.toLocaleString("vi-VN")," đ"]})]})})})}function pn({selectedItemTypeUid:i,itemTypes:t,sortedItems:s,sensors:n,onDragEnd:l}){var m;const h=i&&((m=t.find(x=>x.id===i))==null?void 0:m.item_type_name)||"Uncategory";return e.jsxs("div",{className:"flex h-full flex-col rounded-lg border",children:[e.jsx("div",{className:"flex-shrink-0 border-b bg-gray-50 p-3",children:e.jsx("h3",{className:"text-sm font-medium",children:h})}),e.jsx("div",{className:"min-h-0 flex-1",children:e.jsxs(ae,{className:"h-[50vh] w-full",children:[e.jsx("div",{className:"p-4",children:i?s.length===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:e.jsx("p",{children:"Không có món ăn nào trong nhóm này"})}):e.jsx(ot,{sensors:n,collisionDetection:dt,onDragEnd:l,children:e.jsx(Qe,{items:s.map(x=>x.id),strategy:It,children:e.jsx("div",{className:"grid grid-cols-4 gap-4",children:s.map(x=>e.jsx(xn,{item:x},x.id))})})}):e.jsx("div",{className:"py-8 text-center text-gray-500",children:e.jsx("p",{children:"Vui lòng chọn nhóm món từ danh sách bên trái"})})}),e.jsx(q,{orientation:"vertical"}),e.jsx(q,{orientation:"horizontal"})]})})]})}function gn({open:i,onOpenChange:t}){const[s,n]=N.useState(""),[l,h]=N.useState(""),[m,x]=N.useState([]),[o,_]=N.useState(!1),[w,M]=N.useState(!1),{data:y=[]}=ge({enabled:i}),{data:f=[]}=ce({...s&&s!=="all"?{store_uid:s}:{},enabled:i&&!!s}),{data:k=[]}=Ze({params:{...s&&{store_uid:s},active:1,skip_limit:!0},enabled:i&&!!s}),p=Ie(),I=bs(),D=lt(be(ct),be(rt,{coordinateGetter:We})),[T,u]=N.useState([]);N.useEffect(()=>{if(!l){u([]),M(!1);return}const a=(k||[]).filter(j=>j.item_type_uid===l);if(a.length===0){u([]),M(!1);return}const g=[...a].sort((j,S)=>j.sort!==S.sort?j.sort-S.sort:j.id.localeCompare(S.id));u(g),M(!1)},[k.length,l]),N.useEffect(()=>{i||(n(""),h(""),u([]),x([]),_(!1),M(!1))},[i]),N.useEffect(()=>{h(""),u([])},[s]);const b=a=>{const{active:g,over:j}=a;j&&g.id!==j.id&&u(S=>{const v=S.findIndex(H=>H.id===g.id),V=S.findIndex(H=>H.id===j.id),R=Ye(S,v,V);return w||M(!0),R})},d=()=>T.map((a,g)=>({id:a.id,item_id:a.item_id,item_name:a.item_name,description:a.description,ots_price:a.ots_price,ots_tax:a.ots_tax,ta_price:a.ta_price,ta_tax:a.ta_tax,time_sale_hour_day:a.time_sale_hour_day,time_sale_date_week:a.time_sale_date_week,allow_take_away:a.allow_take_away,is_eat_with:a.is_eat_with,image_path:a.image_path,image_path_thumb:a.image_path_thumb,item_color:a.item_color,list_order:g+1,is_service:a.is_service,is_material:a.is_material,active:a.active,user_id:a.user_id,is_foreign:a.is_foreign,quantity_default:a.quantity_default,price_change:a.price_change,currency_type_id:a.currency_type_id,point:a.point,is_gift:a.is_gift,is_fc:a.is_fc,show_on_web:a.show_on_web,show_price_on_web:a.show_price_on_web,cost_price:a.cost_price,is_print_label:a.is_print_label,quantity_limit:a.quantity_limit,is_kit:a.is_kit,time_cooking:a.time_cooking,item_id_barcode:a.item_id_barcode,process_index:a.process_index,is_allow_discount:a.is_allow_discount,quantity_per_day:a.quantity_per_day,item_id_eat_with:a.item_id_eat_with,is_parent:a.is_parent,is_sub:a.is_sub,item_id_mapping:a.item_id_mapping,effective_date:a.effective_date,expire_date:a.expire_date,sort:g+1,extra_data:{...a.extra_data,formula_qrcode:""},revision:a.revision,unit_uid:a.unit_uid,unit_secondary_uid:a.unit_secondary_uid,item_type_uid:a.item_type_uid,item_class_uid:a.item_class_uid,source_uid:a.source_uid,brand_uid:a.brand_uid,company_uid:a.company_uid,customization_uid:a.customization_uid,is_fabi:a.is_fabi,deleted:a.deleted,created_by:a.created_by,updated_by:a.updated_by,deleted_by:a.deleted_by,created_at:a.created_at,updated_at:a.updated_at,deleted_at:a.deleted_at,apply_with_store:a.apply_with_store,store_uid:a.store_uid,city_uid:a.city_uid})),r=()=>{if(!s){t(!1);return}const a=o,g=w&&T.length>0&&!!l;if(!a&&!g){t(!1);return}let j=0;const S=Number(a)+Number(g),v=()=>{j+=1,j>=S&&t(!1)};if(a){const R=((m.length>0?m:f)||[]).map((H,O)=>({id:H.id,sort:O+1}));I.mutate({store_uid:s,data:R},{onSuccess:()=>v()})}if(g){const V=d();p.mutate(V,{onSuccess:()=>v()})}},c=()=>{t(!1)};return e.jsx($,{open:i,onOpenChange:t,children:e.jsxs(U,{className:"flex h-[90vh] max-w-7xl flex-col sm:max-w-4xl",children:[e.jsx(W,{className:"flex-shrink-0",children:e.jsx(Q,{className:"text-center text-lg font-medium",children:"Sắp xếp thực đơn bán hàng"})}),e.jsx("div",{className:"mb-4 flex-shrink-0 rounded-md border border-yellow-200 bg-yellow-50 p-3",children:e.jsx("p",{className:"text-sm text-yellow-800",children:"Các món tại thành phố bị thay đổi vị trí sẽ chuyển thành món tại cửa hàng"})}),e.jsxs("div",{className:"mb-4 flex flex-shrink-0 items-center gap-4",children:[e.jsx("span",{className:"text-sm font-medium whitespace-nowrap",children:"Thứ tự hiển thị sẽ được áp dụng trên thực đơn của thiết bị bán hàng"}),e.jsx("div",{className:"flex-1"}),e.jsx("div",{className:"w-64",children:e.jsxs(Z,{value:s,onValueChange:n,children:[e.jsx(ee,{children:e.jsx(te,{placeholder:"Chọn điểm áp dụng"})}),e.jsx(se,{children:y.map(a=>e.jsx(G,{value:a.id,children:a.name},a.id))})]})})]}),e.jsx("div",{className:"min-h-0 flex-1 overflow-hidden",children:s?e.jsxs("div",{className:"grid h-full grid-cols-12 gap-4",children:[e.jsx("div",{className:"col-span-3 h-full",children:e.jsxs("div",{className:"flex h-full flex-col rounded-lg border",children:[e.jsx("div",{className:"flex-shrink-0 border-b bg-gray-50 p-3",children:e.jsx("h3",{className:"text-sm font-medium",children:"Tên nhóm món"})}),e.jsx("div",{className:"min-h-0 flex-1",children:e.jsx(mn,{itemTypes:f,selectedStoreUid:s,selectedItemTypeUid:l,onSelect:a=>h(a),onOrderChangeDetailed:a=>{x(a);try{const g=f.map(v=>v.id),j=a.map(v=>v.id),S=g.length!==j.length||g.some((v,V)=>v!==j[V]);_(S)}catch{}}})})]})}),e.jsx("div",{className:"col-span-9 h-full",children:e.jsx(pn,{selectedItemTypeUid:l,itemTypes:f.map(a=>({id:a.id,item_type_name:a.item_type_name})),sortedItems:T,sensors:D,onDragEnd:b})})]}):e.jsx("div",{className:"flex h-full items-center justify-center text-gray-500",children:e.jsx("p",{children:"Chưa chọn cửa hàng"})})}),e.jsxs("div",{className:"flex flex-shrink-0 justify-end gap-2 border-t pt-4",children:[e.jsx(E,{variant:"outline",onClick:c,disabled:p.isPending,children:"Hủy"}),e.jsx(E,{onClick:r,disabled:!s||!o&&!(w&&T.length>0&&l),children:I.isPending||p.isPending?"Đang lưu...":"Lưu"})]})]})})}function fn({open:i,onOpenChange:t}){const[s,n]=N.useState(""),[l,h]=N.useState("all"),[m,x]=N.useState(""),[o,_]=N.useState(0),[w,M]=N.useState(0),[y,f]=N.useState({}),{data:k=[]}=ge({enabled:i}),{data:p=[]}=ce({...m&&m!=="all"?{store_uid:m}:{},enabled:i&&!!m}),{data:I=[],isLoading:D}=Ze({params:{...m?{store_uid:m}:{},...l!=="all"&&l?{item_type_uid:l}:{},active:1},enabled:i&&!!m}),T=N.useMemo(()=>{const c=s.trim().toLowerCase();return c?I.filter(a=>a.active===1).filter(a=>{var g;return((g=a.item_name)==null?void 0:g.toLowerCase().includes(c))||(a.item_id||"").toLowerCase().includes(c)}):I},[I,s]),u=c=>{t(c),c||setTimeout(()=>{n(""),h("all"),x(""),_(0),M(0),f({})},300)},b=(c,a)=>(c&a)!==0?c&~a:c|a,d=Ie(),r=()=>{if(!m||Object.keys(y).length===0){t(!1);return}const c=I.filter(a=>y[a.id]).map(a=>y[a.id]==="selected"?{...a,time_sale_date_week:Number(o||0),time_sale_hour_day:Number(w||0)}:{...a,time_sale_date_week:0,time_sale_hour_day:0});if(c.length===0){t(!1);return}d.mutate(c,{onSuccess:()=>{f({}),t(!1)}})};return e.jsx($,{open:i,onOpenChange:u,children:e.jsxs(U,{className:"flex h-[90vh] max-w-7xl flex-col sm:max-w-5xl",children:[e.jsx(W,{className:"flex-shrink-0",children:e.jsx(Q,{className:"text-center",children:"Cấu hình khung giờ bán hàng"})}),e.jsx("div",{className:"mb-2 flex-shrink-0 rounded-md border border-yellow-200 bg-yellow-50 p-3",children:e.jsx("p",{className:"text-sm text-yellow-800",children:"Các món tại thành phố bị thay đổi vị trí sẽ chuyển thành món tại cửa hàng"})}),e.jsxs("div",{className:"flex flex-shrink-0 items-center gap-3",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(Me,{placeholder:"Nhập tên hoặc mã món",value:s,onChange:c=>n(c.target.value),className:"pl-9"}),e.jsx(at,{className:"text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2"})]}),e.jsx("div",{className:"w-56",children:e.jsxs(Z,{value:l,onValueChange:h,children:[e.jsx(ee,{children:e.jsx(te,{placeholder:"Chọn nhóm món"})}),e.jsxs(se,{children:[e.jsx(G,{value:"all",children:"Tất cả nhóm món"}),p.map(c=>e.jsx(G,{value:c.id,children:c.item_type_name},c.id))]})]})}),e.jsx("div",{className:"w-56",children:e.jsxs(Z,{value:m,onValueChange:x,children:[e.jsx(ee,{children:e.jsx(te,{placeholder:"Chọn điểm áp dụng"})}),e.jsx(se,{children:k.map(c=>e.jsx(G,{value:c.id,children:c.name},c.id))})]})})]}),e.jsxs("div",{className:"mt-3 flex-shrink-0 space-y-2",children:[e.jsx("div",{className:"grid grid-cols-7 gap-2",children:[{name:"Thứ 2",bit:4},{name:"Thứ 3",bit:8},{name:"Thứ 4",bit:16},{name:"Thứ 5",bit:32},{name:"Thứ 6",bit:64},{name:"Thứ 7",bit:128},{name:"Chủ nhật",bit:2}].map(({name:c,bit:a})=>{const g=(o&a)!==0;return e.jsx(E,{type:"button",variant:g?"default":"outline",size:"sm",className:`text-xs ${g?"bg-blue-600 text-white":"border-blue-600 text-blue-600"}`,onClick:()=>_(j=>b(j,a)),children:c},c)})}),e.jsx("div",{className:"grid grid-cols-10 gap-2",children:Array.from({length:24},(c,a)=>({hour:a,label:`${a}h`})).map(({hour:c,label:a})=>{const g=1<<c,j=(w&g)!==0;return e.jsx(E,{type:"button",variant:j?"default":"outline",size:"sm",className:`text-xs ${j?"bg-blue-600 text-white":"border-blue-600 text-blue-600"}`,onClick:()=>M(S=>b(S,g)),children:a},c)})})]}),e.jsx("div",{className:"mt-3 min-h-0 flex-1",children:m?e.jsx("div",{className:"h-full rounded-md border",children:e.jsxs(ae,{className:"h-full w-full",children:[e.jsx("div",{className:"grid grid-cols-1 gap-3 p-3 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4",children:D?e.jsx("div",{className:"text-muted-foreground col-span-full py-10 text-center",children:"Đang tải..."}):T.length===0?e.jsx("div",{className:"text-muted-foreground col-span-full py-10 text-center",children:"Không có món ăn"}):T.map(c=>{const a=Number(c.time_sale_date_week||0)===Number(o||0)&&Number(c.time_sale_hour_day||0)===Number(w||0),g=y[c.id],j=()=>{f(S=>{const v=S[c.id];if(a){if(!v)return{...S,[c.id]:"zero"};const{[c.id]:V,...R}=S;return R}else{if(!v)return{...S,[c.id]:"selected"};if(v==="selected")return{...S,[c.id]:"zero"};const{[c.id]:V,...R}=S;return R}})};return e.jsxs("button",{type:"button",onClick:j,className:"relative flex h-28 flex-col justify-center rounded-md border bg-neutral-800 p-3 text-left text-white",children:[a&&!g&&e.jsx("div",{className:"absolute top-2 right-2 flex h-6 w-6 items-center justify-center rounded-full bg-green-500",children:e.jsx(Ce,{className:"h-4 w-4 text-white"})}),g==="selected"&&e.jsx("div",{className:"absolute top-2 right-2 flex h-6 w-6 items-center justify-center rounded-full bg-green-500",children:e.jsx(Ce,{className:"h-4 w-4 text-white"})}),g==="zero"&&e.jsx("div",{className:"absolute top-2 right-2 flex h-6 w-6 items-center justify-center rounded-full bg-yellow-500",children:e.jsx(Ce,{className:"h-4 w-4 text-white"})}),e.jsx("div",{className:"truncate text-sm font-medium",title:c.item_name,children:c.item_name}),e.jsx("div",{className:"mt-2 truncate text-xs opacity-80",children:c.item_id}),e.jsxs("div",{className:"mt-1 text-sm font-semibold",children:[(c.ots_price||0).toLocaleString("vi-VN")," đ"]})]},c.id)})}),e.jsx(q,{orientation:"vertical"})]})}):e.jsx("div",{className:"flex h-full items-center justify-center text-gray-500",children:e.jsx("p",{children:"Chọn cửa hàng để cấu hình thời gian bán món ăn"})})}),e.jsxs(Ee,{className:"flex-shrink-0",children:[e.jsx(E,{variant:"outline",onClick:()=>u(!1),children:"Hủy"}),e.jsx(E,{onClick:r,disabled:!m||d.isPending||Object.keys(y).length===0,children:d.isPending?"Đang lưu...":"Lưu"})]})]})})}function _n(){const{open:i,setOpen:t,currentRow:s,setCurrentRow:n,selectedStoreUid:l}=Te(),{deleteItemAsync:h}=Ft(),{data:m=[]}=ge(),{data:x=[]}=Fe({enabled:i==="buffet-config"&&!!s});return e.jsxs(e.Fragment,{children:[e.jsx(nn,{open:i==="export",onOpenChange:()=>t(null)}),e.jsx(ln,{open:i==="import",onOpenChange:()=>t(null),storeUid:l||(s==null?void 0:s.store_uid)}),e.jsx(gn,{open:i==="sort-menu",onOpenChange:()=>t(null)}),e.jsx(on,{open:i==="copy-menu",onOpenChange:()=>t(null)}),e.jsx(fn,{open:i==="config-time-frame",onOpenChange:()=>t(null)}),e.jsx(hn,{open:i==="config-price-by-source",onOpenChange:()=>t(null),stores:m}),e.jsx(Je,{open:i==="buffet-config",onOpenChange:o=>{t(null),o||setTimeout(()=>{n(null)},500)},itemsBuffet:[],onItemsChange:()=>{},items:x}),s&&e.jsx(e.Fragment,{children:e.jsx(et,{destructive:!0,open:i==="delete",onOpenChange:o=>{o||(t(null),setTimeout(()=>{n(null)},500))},handleConfirm:async()=>{t(null),setTimeout(()=>{n(null)},500),await h(s.id||"")},className:"max-w-md",title:"Bạn có muốn xoá ?",desc:e.jsx(e.Fragment,{children:"Hành động không thể hoàn tác."}),confirmText:"Xoá"},"quantity-day-delete")})]})}function yn({selectedStoreUid:i,setSelectedStoreUid:t}){const[s,n]=N.useState(1),l=$e(),{setOpen:h,setCurrentRow:m}=Te(),{updateStatusAsync:x}=Dt(),{updateItemAsync:o}=Et(),{isCustomizationDialogOpen:_,isBuffetItem:w,isBuffetConfigModalOpen:M,setIsCustomizationDialogOpen:y,setIsBuffetItem:f,selectedMenuItem:k,setSelectedMenuItem:p,setIsBuffetConfigModalOpen:I,selectedBuffetMenuItem:D,setSelectedBuffetMenuItem:T,selectedItemTypeUid:u,setSelectedItemTypeUid:b,selectedDaysOfWeek:d,setSelectedDaysOfWeek:r,selectedStatus:c,setSelectedStatus:a,selectedApplyWithStore:g,setSelectedApplyWithStore:j}=ut(),S=N.useMemo(()=>({...u!=="all"&&{item_type_uid:u},...i!=="all"&&{store_uid:i},...d.length>0&&{time_sale_date_week:d.join(",")},...c!=="all"&&{active:parseInt(c,10)},...g==="-1"&&{apply_with_store:parseInt(g,10)},page:s}),[u,i,d,c,g,s]),{data:v=[],isLoading:V,error:R,hasNextPage:H}=Fe({params:S}),{data:O=[]}=yt({skip_limit:!0,store_uid:i});N.useEffect(()=>{n(1)},[u,i,d,c,g]);const ue=A=>{p(A),y(!0)},oe=A=>{var le,Ae;p(A),T(((le=A==null?void 0:A.extra_data)==null?void 0:le.exclude_items_buffet)||[]),f(((Ae=A==null?void 0:A.extra_data)==null?void 0:Ae.is_buffet_item)===1),I(!0)},Y=A=>{l({to:"/menu/items/items-in-store/detail",search:{id:A.id||""}})},ie=A=>{m(A),h("delete")},C=A=>{l({to:"/menu/items/items-in-store/detail/$id",params:{id:A.id||""}})},F=async A=>{const le=A.active?0:1;await x({id:A.id||"",active:le})},K=Is({onBuffetConfigClick:oe});return R?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-muted-foreground mb-2 text-sm",children:"Có lỗi xảy ra khi tải dữ liệu"}),e.jsx("p",{className:"text-muted-foreground text-xs",children:R&&`Món ăn: ${(R==null?void 0:R.message)||"Lỗi không xác định"}`})]})}):e.jsxs(e.Fragment,{children:[e.jsx(bt,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Nt,{}),e.jsx(wt,{}),e.jsx(vt,{})]})}),e.jsxs(jt,{children:[e.jsxs("div",{className:"mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Món ăn tại cửa hàng"})}),e.jsx(Ss,{})]}),e.jsxs("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12",children:[V&&e.jsx(Ps,{}),!V&&e.jsx(Os,{columns:K,data:v,onCustomizationClick:ue,onCopyClick:Y,onToggleStatus:F,onRowClick:C,onDeleteClick:ie,customizations:O,selectedItemTypeUid:u,onItemTypeChange:b,selectedStoreUid:i,onStoreChange:t,selectedDaysOfWeek:d,onDaysOfWeekChange:r,selectedStatus:c,onStatusChange:a,selectedApplyWithStore:g,onApplyWithStoreChange:j,hasNextPageOverride:H,currentPage:s,onPageChange:n})]})]}),e.jsx(_n,{}),_&&k&&e.jsx(Ot,{open:_,onOpenChange:y,item:k,customizations:O}),M&&D&&e.jsx(Je,{itemsBuffet:D,open:M,onOpenChange:I,onItemsChange:async A=>{await o({...k,extra_data:{is_buffet_item:w?1:0,exclude_items_buffet:A}})},items:v,hide:!1,enable:w,onEnableChange:f})]})}function bn(){const{selectedStoreUid:i,setSelectedStoreUid:t}=ut();return e.jsx(Mt,{selectedStoreUid:i,setSelectedStoreUid:t,children:e.jsx(yn,{selectedStoreUid:i,setSelectedStoreUid:t})})}const Ya=bn;export{Ya as component};
