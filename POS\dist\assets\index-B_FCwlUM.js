import{r as U}from"./index-C21OP4ex.js";/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function x(e,o){return typeof e=="function"?e(o):e}function $(e,o){return t=>{o.setState(n=>({...n,[e]:x(t,n[e])}))}}function L(e){return e instanceof Function}function Ce(e){return Array.isArray(e)&&e.every(o=>typeof o=="number")}function Re(e,o){const t=[],n=i=>{i.forEach(r=>{t.push(r);const l=o(r);l!=null&&l.length&&n(l)})};return n(e),t}function m(e,o,t){let n=[],i;return r=>{let l;t.key&&t.debug&&(l=Date.now());const s=e(r);if(!(s.length!==n.length||s.some((f,S)=>n[S]!==f)))return i;n=s;let g;if(t.key&&t.debug&&(g=Date.now()),i=o(...s),t==null||t.onChange==null||t.onChange(i),t.key&&t.debug&&t!=null&&t.debug()){const f=Math.round((Date.now()-l)*100)/100,S=Math.round((Date.now()-g)*100)/100,d=S/16,u=(c,p)=>{for(c=String(c);c.length<p;)c=" "+c;return c};console.info(`%c⏱ ${u(S,5)} /${u(f,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*d,120))}deg 100% 31%);`,t==null?void 0:t.key)}return i}}function C(e,o,t,n){return{debug:()=>{var i;return(i=e==null?void 0:e.debugAll)!=null?i:e[o]},key:!1,onChange:n}}function we(e,o,t,n){const i=()=>{var l;return(l=r.getValue())!=null?l:e.options.renderFallbackValue},r={id:`${o.id}_${t.id}`,row:o,column:t,getValue:()=>o.getValue(n),renderValue:i,getContext:m(()=>[e,t,o,r],(l,s,a,g)=>({table:l,column:s,row:a,cell:g,getValue:g.getValue,renderValue:g.renderValue}),C(e.options,"debugCells"))};return e._features.forEach(l=>{l.createCell==null||l.createCell(r,t,o,e)},{}),r}function ve(e,o,t,n){var i,r;const s={...e._getDefaultColumnDef(),...o},a=s.accessorKey;let g=(i=(r=s.id)!=null?r:a?typeof String.prototype.replaceAll=="function"?a.replaceAll(".","_"):a.replace(/\./g,"_"):void 0)!=null?i:typeof s.header=="string"?s.header:void 0,f;if(s.accessorFn?f=s.accessorFn:a&&(a.includes(".")?f=d=>{let u=d;for(const p of a.split(".")){var c;u=(c=u)==null?void 0:c[p]}return u}:f=d=>d[s.accessorKey]),!g)throw new Error;let S={id:`${String(g)}`,accessorFn:f,parent:n,depth:t,columnDef:s,columns:[],getFlatColumns:m(()=>[!0],()=>{var d;return[S,...(d=S.columns)==null?void 0:d.flatMap(u=>u.getFlatColumns())]},C(e.options,"debugColumns")),getLeafColumns:m(()=>[e._getOrderColumnsFn()],d=>{var u;if((u=S.columns)!=null&&u.length){let c=S.columns.flatMap(p=>p.getLeafColumns());return d(c)}return[S]},C(e.options,"debugColumns"))};for(const d of e._features)d.createColumn==null||d.createColumn(S,e);return S}const _="debugHeaders";function oe(e,o,t){var n;let r={id:(n=t.id)!=null?n:o.id,column:o,index:t.index,isPlaceholder:!!t.isPlaceholder,placeholderId:t.placeholderId,depth:t.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const l=[],s=a=>{a.subHeaders&&a.subHeaders.length&&a.subHeaders.map(s),l.push(a)};return s(r),l},getContext:()=>({table:e,header:r,column:o})};return e._features.forEach(l=>{l.createHeader==null||l.createHeader(r,e)}),r}const he={createTable:e=>{e.getHeaderGroups=m(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n,i)=>{var r,l;const s=(r=n==null?void 0:n.map(S=>t.find(d=>d.id===S)).filter(Boolean))!=null?r:[],a=(l=i==null?void 0:i.map(S=>t.find(d=>d.id===S)).filter(Boolean))!=null?l:[],g=t.filter(S=>!(n!=null&&n.includes(S.id))&&!(i!=null&&i.includes(S.id)));return G(o,[...s,...g,...a],e)},C(e.options,_)),e.getCenterHeaderGroups=m(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n,i)=>(t=t.filter(r=>!(n!=null&&n.includes(r.id))&&!(i!=null&&i.includes(r.id))),G(o,t,e,"center")),C(e.options,_)),e.getLeftHeaderGroups=m(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(o,t,n)=>{var i;const r=(i=n==null?void 0:n.map(l=>t.find(s=>s.id===l)).filter(Boolean))!=null?i:[];return G(o,r,e,"left")},C(e.options,_)),e.getRightHeaderGroups=m(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(o,t,n)=>{var i;const r=(i=n==null?void 0:n.map(l=>t.find(s=>s.id===l)).filter(Boolean))!=null?i:[];return G(o,r,e,"right")},C(e.options,_)),e.getFooterGroups=m(()=>[e.getHeaderGroups()],o=>[...o].reverse(),C(e.options,_)),e.getLeftFooterGroups=m(()=>[e.getLeftHeaderGroups()],o=>[...o].reverse(),C(e.options,_)),e.getCenterFooterGroups=m(()=>[e.getCenterHeaderGroups()],o=>[...o].reverse(),C(e.options,_)),e.getRightFooterGroups=m(()=>[e.getRightHeaderGroups()],o=>[...o].reverse(),C(e.options,_)),e.getFlatHeaders=m(()=>[e.getHeaderGroups()],o=>o.map(t=>t.headers).flat(),C(e.options,_)),e.getLeftFlatHeaders=m(()=>[e.getLeftHeaderGroups()],o=>o.map(t=>t.headers).flat(),C(e.options,_)),e.getCenterFlatHeaders=m(()=>[e.getCenterHeaderGroups()],o=>o.map(t=>t.headers).flat(),C(e.options,_)),e.getRightFlatHeaders=m(()=>[e.getRightHeaderGroups()],o=>o.map(t=>t.headers).flat(),C(e.options,_)),e.getCenterLeafHeaders=m(()=>[e.getCenterFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),C(e.options,_)),e.getLeftLeafHeaders=m(()=>[e.getLeftFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),C(e.options,_)),e.getRightLeafHeaders=m(()=>[e.getRightFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),C(e.options,_)),e.getLeafHeaders=m(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(o,t,n)=>{var i,r,l,s,a,g;return[...(i=(r=o[0])==null?void 0:r.headers)!=null?i:[],...(l=(s=t[0])==null?void 0:s.headers)!=null?l:[],...(a=(g=n[0])==null?void 0:g.headers)!=null?a:[]].map(f=>f.getLeafHeaders()).flat()},C(e.options,_))}};function G(e,o,t,n){var i,r;let l=0;const s=function(d,u){u===void 0&&(u=1),l=Math.max(l,u),d.filter(c=>c.getIsVisible()).forEach(c=>{var p;(p=c.columns)!=null&&p.length&&s(c.columns,u+1)},0)};s(e);let a=[];const g=(d,u)=>{const c={depth:u,id:[n,`${u}`].filter(Boolean).join("_"),headers:[]},p=[];d.forEach(R=>{const w=[...p].reverse()[0],v=R.column.depth===c.depth;let h,M=!1;if(v&&R.column.parent?h=R.column.parent:(h=R.column,M=!0),w&&(w==null?void 0:w.column)===h)w.subHeaders.push(R);else{const F=oe(t,h,{id:[n,u,h.id,R==null?void 0:R.id].filter(Boolean).join("_"),isPlaceholder:M,placeholderId:M?`${p.filter(y=>y.column===h).length}`:void 0,depth:u,index:p.length});F.subHeaders.push(R),p.push(F)}c.headers.push(R),R.headerGroup=c}),a.push(c),u>0&&g(p,u-1)},f=o.map((d,u)=>oe(t,d,{depth:l,index:u}));g(f,l-1),a.reverse();const S=d=>d.filter(c=>c.column.getIsVisible()).map(c=>{let p=0,R=0,w=[0];c.subHeaders&&c.subHeaders.length?(w=[],S(c.subHeaders).forEach(h=>{let{colSpan:M,rowSpan:F}=h;p+=M,w.push(F)})):p=1;const v=Math.min(...w);return R=R+v,c.colSpan=p,c.rowSpan=R,{colSpan:p,rowSpan:R}});return S((i=(r=a[0])==null?void 0:r.headers)!=null?i:[]),a}const W=(e,o,t,n,i,r,l)=>{let s={id:o,index:n,original:t,depth:i,parentId:l,_valuesCache:{},_uniqueValuesCache:{},getValue:a=>{if(s._valuesCache.hasOwnProperty(a))return s._valuesCache[a];const g=e.getColumn(a);if(g!=null&&g.accessorFn)return s._valuesCache[a]=g.accessorFn(s.original,n),s._valuesCache[a]},getUniqueValues:a=>{if(s._uniqueValuesCache.hasOwnProperty(a))return s._uniqueValuesCache[a];const g=e.getColumn(a);if(g!=null&&g.accessorFn)return g.columnDef.getUniqueValues?(s._uniqueValuesCache[a]=g.columnDef.getUniqueValues(s.original,n),s._uniqueValuesCache[a]):(s._uniqueValuesCache[a]=[s.getValue(a)],s._uniqueValuesCache[a])},renderValue:a=>{var g;return(g=s.getValue(a))!=null?g:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>Re(s.subRows,a=>a.subRows),getParentRow:()=>s.parentId?e.getRow(s.parentId,!0):void 0,getParentRows:()=>{let a=[],g=s;for(;;){const f=g.getParentRow();if(!f)break;a.push(f),g=f}return a.reverse()},getAllCells:m(()=>[e.getAllLeafColumns()],a=>a.map(g=>we(e,s,g,g.id)),C(e.options,"debugRows")),_getAllCellsByColumnId:m(()=>[s.getAllCells()],a=>a.reduce((g,f)=>(g[f.column.id]=f,g),{}),C(e.options,"debugRows"))};for(let a=0;a<e._features.length;a++){const g=e._features[a];g==null||g.createRow==null||g.createRow(s,e)}return s},_e={createColumn:(e,o)=>{e._getFacetedRowModel=o.options.getFacetedRowModel&&o.options.getFacetedRowModel(o,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():o.getPreFilteredRowModel(),e._getFacetedUniqueValues=o.options.getFacetedUniqueValues&&o.options.getFacetedUniqueValues(o,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=o.options.getFacetedMinMaxValues&&o.options.getFacetedMinMaxValues(o,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},re=(e,o,t)=>{var n,i;const r=t==null||(n=t.toString())==null?void 0:n.toLowerCase();return!!(!((i=e.getValue(o))==null||(i=i.toString())==null||(i=i.toLowerCase())==null)&&i.includes(r))};re.autoRemove=e=>V(e);const le=(e,o,t)=>{var n;return!!(!((n=e.getValue(o))==null||(n=n.toString())==null)&&n.includes(t))};le.autoRemove=e=>V(e);const se=(e,o,t)=>{var n;return((n=e.getValue(o))==null||(n=n.toString())==null?void 0:n.toLowerCase())===(t==null?void 0:t.toLowerCase())};se.autoRemove=e=>V(e);const ue=(e,o,t)=>{var n;return(n=e.getValue(o))==null?void 0:n.includes(t)};ue.autoRemove=e=>V(e);const ae=(e,o,t)=>!t.some(n=>{var i;return!((i=e.getValue(o))!=null&&i.includes(n))});ae.autoRemove=e=>V(e)||!(e!=null&&e.length);const ge=(e,o,t)=>t.some(n=>{var i;return(i=e.getValue(o))==null?void 0:i.includes(n)});ge.autoRemove=e=>V(e)||!(e!=null&&e.length);const de=(e,o,t)=>e.getValue(o)===t;de.autoRemove=e=>V(e);const fe=(e,o,t)=>e.getValue(o)==t;fe.autoRemove=e=>V(e);const Y=(e,o,t)=>{let[n,i]=t;const r=e.getValue(o);return r>=n&&r<=i};Y.resolveFilterValue=e=>{let[o,t]=e,n=typeof o!="number"?parseFloat(o):o,i=typeof t!="number"?parseFloat(t):t,r=o===null||Number.isNaN(n)?-1/0:n,l=t===null||Number.isNaN(i)?1/0:i;if(r>l){const s=r;r=l,l=s}return[r,l]};Y.autoRemove=e=>V(e)||V(e[0])&&V(e[1]);const P={includesString:re,includesStringSensitive:le,equalsString:se,arrIncludes:ue,arrIncludesAll:ae,arrIncludesSome:ge,equals:de,weakEquals:fe,inNumberRange:Y};function V(e){return e==null||e===""}const Fe={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:$("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,o)=>{e.getAutoFilterFn=()=>{const t=o.getCoreRowModel().flatRows[0],n=t==null?void 0:t.getValue(e.id);return typeof n=="string"?P.includesString:typeof n=="number"?P.inNumberRange:typeof n=="boolean"||n!==null&&typeof n=="object"?P.equals:Array.isArray(n)?P.arrIncludes:P.weakEquals},e.getFilterFn=()=>{var t,n;return L(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(t=(n=o.options.filterFns)==null?void 0:n[e.columnDef.filterFn])!=null?t:P[e.columnDef.filterFn]},e.getCanFilter=()=>{var t,n,i;return((t=e.columnDef.enableColumnFilter)!=null?t:!0)&&((n=o.options.enableColumnFilters)!=null?n:!0)&&((i=o.options.enableFilters)!=null?i:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var t;return(t=o.getState().columnFilters)==null||(t=t.find(n=>n.id===e.id))==null?void 0:t.value},e.getFilterIndex=()=>{var t,n;return(t=(n=o.getState().columnFilters)==null?void 0:n.findIndex(i=>i.id===e.id))!=null?t:-1},e.setFilterValue=t=>{o.setColumnFilters(n=>{const i=e.getFilterFn(),r=n==null?void 0:n.find(f=>f.id===e.id),l=x(t,r?r.value:void 0);if(ie(i,l,e)){var s;return(s=n==null?void 0:n.filter(f=>f.id!==e.id))!=null?s:[]}const a={id:e.id,value:l};if(r){var g;return(g=n==null?void 0:n.map(f=>f.id===e.id?a:f))!=null?g:[]}return n!=null&&n.length?[...n,a]:[a]})}},createRow:(e,o)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=o=>{const t=e.getAllLeafColumns(),n=i=>{var r;return(r=x(o,i))==null?void 0:r.filter(l=>{const s=t.find(a=>a.id===l.id);if(s){const a=s.getFilterFn();if(ie(a,l.value,s))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(n)},e.resetColumnFilters=o=>{var t,n;e.setColumnFilters(o?[]:(t=(n=e.initialState)==null?void 0:n.columnFilters)!=null?t:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function ie(e,o,t){return(e&&e.autoRemove?e.autoRemove(o,t):!1)||typeof o>"u"||typeof o=="string"&&!o}const $e=(e,o,t)=>t.reduce((n,i)=>{const r=i.getValue(e);return n+(typeof r=="number"?r:0)},0),Ve=(e,o,t)=>{let n;return t.forEach(i=>{const r=i.getValue(e);r!=null&&(n>r||n===void 0&&r>=r)&&(n=r)}),n},Me=(e,o,t)=>{let n;return t.forEach(i=>{const r=i.getValue(e);r!=null&&(n<r||n===void 0&&r>=r)&&(n=r)}),n},Pe=(e,o,t)=>{let n,i;return t.forEach(r=>{const l=r.getValue(e);l!=null&&(n===void 0?l>=l&&(n=i=l):(n>l&&(n=l),i<l&&(i=l)))}),[n,i]},xe=(e,o)=>{let t=0,n=0;if(o.forEach(i=>{let r=i.getValue(e);r!=null&&(r=+r)>=r&&(++t,n+=r)}),t)return n/t},Ie=(e,o)=>{if(!o.length)return;const t=o.map(r=>r.getValue(e));if(!Ce(t))return;if(t.length===1)return t[0];const n=Math.floor(t.length/2),i=t.sort((r,l)=>r-l);return t.length%2!==0?i[n]:(i[n-1]+i[n])/2},ye=(e,o)=>Array.from(new Set(o.map(t=>t.getValue(e))).values()),Ee=(e,o)=>new Set(o.map(t=>t.getValue(e))).size,De=(e,o)=>o.length,z={sum:$e,min:Ve,max:Me,extent:Pe,mean:xe,median:Ie,unique:ye,uniqueCount:Ee,count:De},Ge={getDefaultColumnDef:()=>({aggregatedCell:e=>{var o,t;return(o=(t=e.getValue())==null||t.toString==null?void 0:t.toString())!=null?o:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:$("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,o)=>{e.toggleGrouping=()=>{o.setGrouping(t=>t!=null&&t.includes(e.id)?t.filter(n=>n!==e.id):[...t??[],e.id])},e.getCanGroup=()=>{var t,n;return((t=e.columnDef.enableGrouping)!=null?t:!0)&&((n=o.options.enableGrouping)!=null?n:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var t;return(t=o.getState().grouping)==null?void 0:t.includes(e.id)},e.getGroupedIndex=()=>{var t;return(t=o.getState().grouping)==null?void 0:t.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const t=o.getCoreRowModel().flatRows[0],n=t==null?void 0:t.getValue(e.id);if(typeof n=="number")return z.sum;if(Object.prototype.toString.call(n)==="[object Date]")return z.extent},e.getAggregationFn=()=>{var t,n;if(!e)throw new Error;return L(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(t=(n=o.options.aggregationFns)==null?void 0:n[e.columnDef.aggregationFn])!=null?t:z[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=o=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(o),e.resetGrouping=o=>{var t,n;e.setGrouping(o?[]:(t=(n=e.initialState)==null?void 0:n.grouping)!=null?t:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,o)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=t=>{if(e._groupingValuesCache.hasOwnProperty(t))return e._groupingValuesCache[t];const n=o.getColumn(t);return n!=null&&n.columnDef.getGroupingValue?(e._groupingValuesCache[t]=n.columnDef.getGroupingValue(e.original),e._groupingValuesCache[t]):e.getValue(t)},e._groupingValuesCache={}},createCell:(e,o,t,n)=>{e.getIsGrouped=()=>o.getIsGrouped()&&o.id===t.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&o.getIsGrouped(),e.getIsAggregated=()=>{var i;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((i=t.subRows)!=null&&i.length)}}};function He(e,o,t){if(!(o!=null&&o.length)||!t)return e;const n=e.filter(r=>!o.includes(r.id));return t==="remove"?n:[...o.map(r=>e.find(l=>l.id===r)).filter(Boolean),...n]}const Ae={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:$("columnOrder",e)}),createColumn:(e,o)=>{e.getIndex=m(t=>[D(o,t)],t=>t.findIndex(n=>n.id===e.id),C(o.options,"debugColumns")),e.getIsFirstColumn=t=>{var n;return((n=D(o,t)[0])==null?void 0:n.id)===e.id},e.getIsLastColumn=t=>{var n;const i=D(o,t);return((n=i[i.length-1])==null?void 0:n.id)===e.id}},createTable:e=>{e.setColumnOrder=o=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(o),e.resetColumnOrder=o=>{var t;e.setColumnOrder(o?[]:(t=e.initialState.columnOrder)!=null?t:[])},e._getOrderColumnsFn=m(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(o,t,n)=>i=>{let r=[];if(!(o!=null&&o.length))r=i;else{const l=[...o],s=[...i];for(;s.length&&l.length;){const a=l.shift(),g=s.findIndex(f=>f.id===a);g>-1&&r.push(s.splice(g,1)[0])}r=[...r,...s]}return He(r,t,n)},C(e.options,"debugTable"))}},O=()=>({left:[],right:[]}),Le={getInitialState:e=>({columnPinning:O(),...e}),getDefaultOptions:e=>({onColumnPinningChange:$("columnPinning",e)}),createColumn:(e,o)=>{e.pin=t=>{const n=e.getLeafColumns().map(i=>i.id).filter(Boolean);o.setColumnPinning(i=>{var r,l;if(t==="right"){var s,a;return{left:((s=i==null?void 0:i.left)!=null?s:[]).filter(S=>!(n!=null&&n.includes(S))),right:[...((a=i==null?void 0:i.right)!=null?a:[]).filter(S=>!(n!=null&&n.includes(S))),...n]}}if(t==="left"){var g,f;return{left:[...((g=i==null?void 0:i.left)!=null?g:[]).filter(S=>!(n!=null&&n.includes(S))),...n],right:((f=i==null?void 0:i.right)!=null?f:[]).filter(S=>!(n!=null&&n.includes(S)))}}return{left:((r=i==null?void 0:i.left)!=null?r:[]).filter(S=>!(n!=null&&n.includes(S))),right:((l=i==null?void 0:i.right)!=null?l:[]).filter(S=>!(n!=null&&n.includes(S)))}})},e.getCanPin=()=>e.getLeafColumns().some(n=>{var i,r,l;return((i=n.columnDef.enablePinning)!=null?i:!0)&&((r=(l=o.options.enableColumnPinning)!=null?l:o.options.enablePinning)!=null?r:!0)}),e.getIsPinned=()=>{const t=e.getLeafColumns().map(s=>s.id),{left:n,right:i}=o.getState().columnPinning,r=t.some(s=>n==null?void 0:n.includes(s)),l=t.some(s=>i==null?void 0:i.includes(s));return r?"left":l?"right":!1},e.getPinnedIndex=()=>{var t,n;const i=e.getIsPinned();return i?(t=(n=o.getState().columnPinning)==null||(n=n[i])==null?void 0:n.indexOf(e.id))!=null?t:-1:0}},createRow:(e,o)=>{e.getCenterVisibleCells=m(()=>[e._getAllVisibleCells(),o.getState().columnPinning.left,o.getState().columnPinning.right],(t,n,i)=>{const r=[...n??[],...i??[]];return t.filter(l=>!r.includes(l.column.id))},C(o.options,"debugRows")),e.getLeftVisibleCells=m(()=>[e._getAllVisibleCells(),o.getState().columnPinning.left],(t,n)=>(n??[]).map(r=>t.find(l=>l.column.id===r)).filter(Boolean).map(r=>({...r,position:"left"})),C(o.options,"debugRows")),e.getRightVisibleCells=m(()=>[e._getAllVisibleCells(),o.getState().columnPinning.right],(t,n)=>(n??[]).map(r=>t.find(l=>l.column.id===r)).filter(Boolean).map(r=>({...r,position:"right"})),C(o.options,"debugRows"))},createTable:e=>{e.setColumnPinning=o=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(o),e.resetColumnPinning=o=>{var t,n;return e.setColumnPinning(o?O():(t=(n=e.initialState)==null?void 0:n.columnPinning)!=null?t:O())},e.getIsSomeColumnsPinned=o=>{var t;const n=e.getState().columnPinning;if(!o){var i,r;return!!((i=n.left)!=null&&i.length||(r=n.right)!=null&&r.length)}return!!((t=n[o])!=null&&t.length)},e.getLeftLeafColumns=m(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(o,t)=>(t??[]).map(n=>o.find(i=>i.id===n)).filter(Boolean),C(e.options,"debugColumns")),e.getRightLeafColumns=m(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(o,t)=>(t??[]).map(n=>o.find(i=>i.id===n)).filter(Boolean),C(e.options,"debugColumns")),e.getCenterLeafColumns=m(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n)=>{const i=[...t??[],...n??[]];return o.filter(r=>!i.includes(r.id))},C(e.options,"debugColumns"))}};function ze(e){return e||(typeof document<"u"?document:null)}const H={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},B=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),Oe={getDefaultColumnDef:()=>H,getInitialState:e=>({columnSizing:{},columnSizingInfo:B(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:$("columnSizing",e),onColumnSizingInfoChange:$("columnSizingInfo",e)}),createColumn:(e,o)=>{e.getSize=()=>{var t,n,i;const r=o.getState().columnSizing[e.id];return Math.min(Math.max((t=e.columnDef.minSize)!=null?t:H.minSize,(n=r??e.columnDef.size)!=null?n:H.size),(i=e.columnDef.maxSize)!=null?i:H.maxSize)},e.getStart=m(t=>[t,D(o,t),o.getState().columnSizing],(t,n)=>n.slice(0,e.getIndex(t)).reduce((i,r)=>i+r.getSize(),0),C(o.options,"debugColumns")),e.getAfter=m(t=>[t,D(o,t),o.getState().columnSizing],(t,n)=>n.slice(e.getIndex(t)+1).reduce((i,r)=>i+r.getSize(),0),C(o.options,"debugColumns")),e.resetSize=()=>{o.setColumnSizing(t=>{let{[e.id]:n,...i}=t;return i})},e.getCanResize=()=>{var t,n;return((t=e.columnDef.enableResizing)!=null?t:!0)&&((n=o.options.enableColumnResizing)!=null?n:!0)},e.getIsResizing=()=>o.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,o)=>{e.getSize=()=>{let t=0;const n=i=>{if(i.subHeaders.length)i.subHeaders.forEach(n);else{var r;t+=(r=i.column.getSize())!=null?r:0}};return n(e),t},e.getStart=()=>{if(e.index>0){const t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=t=>{const n=o.getColumn(e.column.id),i=n==null?void 0:n.getCanResize();return r=>{if(!n||!i||(r.persist==null||r.persist(),T(r)&&r.touches&&r.touches.length>1))return;const l=e.getSize(),s=e?e.getLeafHeaders().map(w=>[w.column.id,w.column.getSize()]):[[n.id,n.getSize()]],a=T(r)?Math.round(r.touches[0].clientX):r.clientX,g={},f=(w,v)=>{typeof v=="number"&&(o.setColumnSizingInfo(h=>{var M,F;const y=o.options.columnResizeDirection==="rtl"?-1:1,ee=(v-((M=h==null?void 0:h.startOffset)!=null?M:0))*y,te=Math.max(ee/((F=h==null?void 0:h.startSize)!=null?F:0),-.999999);return h.columnSizingStart.forEach(Se=>{let[me,ne]=Se;g[me]=Math.round(Math.max(ne+ne*te,0)*100)/100}),{...h,deltaOffset:ee,deltaPercentage:te}}),(o.options.columnResizeMode==="onChange"||w==="end")&&o.setColumnSizing(h=>({...h,...g})))},S=w=>f("move",w),d=w=>{f("end",w),o.setColumnSizingInfo(v=>({...v,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},u=ze(t),c={moveHandler:w=>S(w.clientX),upHandler:w=>{u==null||u.removeEventListener("mousemove",c.moveHandler),u==null||u.removeEventListener("mouseup",c.upHandler),d(w.clientX)}},p={moveHandler:w=>(w.cancelable&&(w.preventDefault(),w.stopPropagation()),S(w.touches[0].clientX),!1),upHandler:w=>{var v;u==null||u.removeEventListener("touchmove",p.moveHandler),u==null||u.removeEventListener("touchend",p.upHandler),w.cancelable&&(w.preventDefault(),w.stopPropagation()),d((v=w.touches[0])==null?void 0:v.clientX)}},R=Be()?{passive:!1}:!1;T(r)?(u==null||u.addEventListener("touchmove",p.moveHandler,R),u==null||u.addEventListener("touchend",p.upHandler,R)):(u==null||u.addEventListener("mousemove",c.moveHandler,R),u==null||u.addEventListener("mouseup",c.upHandler,R)),o.setColumnSizingInfo(w=>({...w,startOffset:a,startSize:l,deltaOffset:0,deltaPercentage:0,columnSizingStart:s,isResizingColumn:n.id}))}}},createTable:e=>{e.setColumnSizing=o=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(o),e.setColumnSizingInfo=o=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(o),e.resetColumnSizing=o=>{var t;e.setColumnSizing(o?{}:(t=e.initialState.columnSizing)!=null?t:{})},e.resetHeaderSizeInfo=o=>{var t;e.setColumnSizingInfo(o?B():(t=e.initialState.columnSizingInfo)!=null?t:B())},e.getTotalSize=()=>{var o,t;return(o=(t=e.getHeaderGroups()[0])==null?void 0:t.headers.reduce((n,i)=>n+i.getSize(),0))!=null?o:0},e.getLeftTotalSize=()=>{var o,t;return(o=(t=e.getLeftHeaderGroups()[0])==null?void 0:t.headers.reduce((n,i)=>n+i.getSize(),0))!=null?o:0},e.getCenterTotalSize=()=>{var o,t;return(o=(t=e.getCenterHeaderGroups()[0])==null?void 0:t.headers.reduce((n,i)=>n+i.getSize(),0))!=null?o:0},e.getRightTotalSize=()=>{var o,t;return(o=(t=e.getRightHeaderGroups()[0])==null?void 0:t.headers.reduce((n,i)=>n+i.getSize(),0))!=null?o:0}}};let A=null;function Be(){if(typeof A=="boolean")return A;let e=!1;try{const o={get passive(){return e=!0,!1}},t=()=>{};window.addEventListener("test",t,o),window.removeEventListener("test",t)}catch{e=!1}return A=e,A}function T(e){return e.type==="touchstart"}const Te={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:$("columnVisibility",e)}),createColumn:(e,o)=>{e.toggleVisibility=t=>{e.getCanHide()&&o.setColumnVisibility(n=>({...n,[e.id]:t??!e.getIsVisible()}))},e.getIsVisible=()=>{var t,n;const i=e.columns;return(t=i.length?i.some(r=>r.getIsVisible()):(n=o.getState().columnVisibility)==null?void 0:n[e.id])!=null?t:!0},e.getCanHide=()=>{var t,n;return((t=e.columnDef.enableHiding)!=null?t:!0)&&((n=o.options.enableHiding)!=null?n:!0)},e.getToggleVisibilityHandler=()=>t=>{e.toggleVisibility==null||e.toggleVisibility(t.target.checked)}},createRow:(e,o)=>{e._getAllVisibleCells=m(()=>[e.getAllCells(),o.getState().columnVisibility],t=>t.filter(n=>n.column.getIsVisible()),C(o.options,"debugRows")),e.getVisibleCells=m(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(t,n,i)=>[...t,...n,...i],C(o.options,"debugRows"))},createTable:e=>{const o=(t,n)=>m(()=>[n(),n().filter(i=>i.getIsVisible()).map(i=>i.id).join("_")],i=>i.filter(r=>r.getIsVisible==null?void 0:r.getIsVisible()),C(e.options,"debugColumns"));e.getVisibleFlatColumns=o("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=o("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=o("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=o("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=o("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var n;e.setColumnVisibility(t?{}:(n=e.initialState.columnVisibility)!=null?n:{})},e.toggleAllColumnsVisible=t=>{var n;t=(n=t)!=null?n:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((i,r)=>({...i,[r.id]:t||!(r.getCanHide!=null&&r.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(t=>!(t.getIsVisible!=null&&t.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(t=>t.getIsVisible==null?void 0:t.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var n;e.toggleAllColumnsVisible((n=t.target)==null?void 0:n.checked)}}};function D(e,o){return o?o==="center"?e.getCenterVisibleLeafColumns():o==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const qe={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},ke={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:$("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:o=>{var t;const n=(t=e.getCoreRowModel().flatRows[0])==null||(t=t._getAllCellsByColumnId()[o.id])==null?void 0:t.getValue();return typeof n=="string"||typeof n=="number"}}),createColumn:(e,o)=>{e.getCanGlobalFilter=()=>{var t,n,i,r;return((t=e.columnDef.enableGlobalFilter)!=null?t:!0)&&((n=o.options.enableGlobalFilter)!=null?n:!0)&&((i=o.options.enableFilters)!=null?i:!0)&&((r=o.options.getColumnCanGlobalFilter==null?void 0:o.options.getColumnCanGlobalFilter(e))!=null?r:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>P.includesString,e.getGlobalFilterFn=()=>{var o,t;const{globalFilterFn:n}=e.options;return L(n)?n:n==="auto"?e.getGlobalAutoFilterFn():(o=(t=e.options.filterFns)==null?void 0:t[n])!=null?o:P[n]},e.setGlobalFilter=o=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(o)},e.resetGlobalFilter=o=>{e.setGlobalFilter(o?void 0:e.initialState.globalFilter)}}},je={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:$("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let o=!1,t=!1;e._autoResetExpanded=()=>{var n,i;if(!o){e._queue(()=>{o=!0});return}if((n=(i=e.options.autoResetAll)!=null?i:e.options.autoResetExpanded)!=null?n:!e.options.manualExpanding){if(t)return;t=!0,e._queue(()=>{e.resetExpanded(),t=!1})}},e.setExpanded=n=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(n),e.toggleAllRowsExpanded=n=>{n??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=n=>{var i,r;e.setExpanded(n?{}:(i=(r=e.initialState)==null?void 0:r.expanded)!=null?i:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(n=>n.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>n=>{n.persist==null||n.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const n=e.getState().expanded;return n===!0||Object.values(n).some(Boolean)},e.getIsAllRowsExpanded=()=>{const n=e.getState().expanded;return typeof n=="boolean"?n===!0:!(!Object.keys(n).length||e.getRowModel().flatRows.some(i=>!i.getIsExpanded()))},e.getExpandedDepth=()=>{let n=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(r=>{const l=r.split(".");n=Math.max(n,l.length)}),n},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,o)=>{e.toggleExpanded=t=>{o.setExpanded(n=>{var i;const r=n===!0?!0:!!(n!=null&&n[e.id]);let l={};if(n===!0?Object.keys(o.getRowModel().rowsById).forEach(s=>{l[s]=!0}):l=n,t=(i=t)!=null?i:!r,!r&&t)return{...l,[e.id]:!0};if(r&&!t){const{[e.id]:s,...a}=l;return a}return n})},e.getIsExpanded=()=>{var t;const n=o.getState().expanded;return!!((t=o.options.getIsRowExpanded==null?void 0:o.options.getIsRowExpanded(e))!=null?t:n===!0||n!=null&&n[e.id])},e.getCanExpand=()=>{var t,n,i;return(t=o.options.getRowCanExpand==null?void 0:o.options.getRowCanExpand(e))!=null?t:((n=o.options.enableExpanding)!=null?n:!0)&&!!((i=e.subRows)!=null&&i.length)},e.getIsAllParentsExpanded=()=>{let t=!0,n=e;for(;t&&n.parentId;)n=o.getRow(n.parentId,!0),t=n.getIsExpanded();return t},e.getToggleExpandedHandler=()=>{const t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},N=0,X=10,q=()=>({pageIndex:N,pageSize:X}),Ue={getInitialState:e=>({...e,pagination:{...q(),...e==null?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:$("pagination",e)}),createTable:e=>{let o=!1,t=!1;e._autoResetPageIndex=()=>{var n,i;if(!o){e._queue(()=>{o=!0});return}if((n=(i=e.options.autoResetAll)!=null?i:e.options.autoResetPageIndex)!=null?n:!e.options.manualPagination){if(t)return;t=!0,e._queue(()=>{e.resetPageIndex(),t=!1})}},e.setPagination=n=>{const i=r=>x(n,r);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(i)},e.resetPagination=n=>{var i;e.setPagination(n?q():(i=e.initialState.pagination)!=null?i:q())},e.setPageIndex=n=>{e.setPagination(i=>{let r=x(n,i.pageIndex);const l=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return r=Math.max(0,Math.min(r,l)),{...i,pageIndex:r}})},e.resetPageIndex=n=>{var i,r;e.setPageIndex(n?N:(i=(r=e.initialState)==null||(r=r.pagination)==null?void 0:r.pageIndex)!=null?i:N)},e.resetPageSize=n=>{var i,r;e.setPageSize(n?X:(i=(r=e.initialState)==null||(r=r.pagination)==null?void 0:r.pageSize)!=null?i:X)},e.setPageSize=n=>{e.setPagination(i=>{const r=Math.max(1,x(n,i.pageSize)),l=i.pageSize*i.pageIndex,s=Math.floor(l/r);return{...i,pageIndex:s,pageSize:r}})},e.setPageCount=n=>e.setPagination(i=>{var r;let l=x(n,(r=e.options.pageCount)!=null?r:-1);return typeof l=="number"&&(l=Math.max(-1,l)),{...i,pageCount:l}}),e.getPageOptions=m(()=>[e.getPageCount()],n=>{let i=[];return n&&n>0&&(i=[...new Array(n)].fill(null).map((r,l)=>l)),i},C(e.options,"debugTable")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:n}=e.getState().pagination,i=e.getPageCount();return i===-1?!0:i===0?!1:n<i-1},e.previousPage=()=>e.setPageIndex(n=>n-1),e.nextPage=()=>e.setPageIndex(n=>n+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var n;return(n=e.options.pageCount)!=null?n:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var n;return(n=e.options.rowCount)!=null?n:e.getPrePaginationRowModel().rows.length}}},k=()=>({top:[],bottom:[]}),Ne={getInitialState:e=>({rowPinning:k(),...e}),getDefaultOptions:e=>({onRowPinningChange:$("rowPinning",e)}),createRow:(e,o)=>{e.pin=(t,n,i)=>{const r=n?e.getLeafRows().map(a=>{let{id:g}=a;return g}):[],l=i?e.getParentRows().map(a=>{let{id:g}=a;return g}):[],s=new Set([...l,e.id,...r]);o.setRowPinning(a=>{var g,f;if(t==="bottom"){var S,d;return{top:((S=a==null?void 0:a.top)!=null?S:[]).filter(p=>!(s!=null&&s.has(p))),bottom:[...((d=a==null?void 0:a.bottom)!=null?d:[]).filter(p=>!(s!=null&&s.has(p))),...Array.from(s)]}}if(t==="top"){var u,c;return{top:[...((u=a==null?void 0:a.top)!=null?u:[]).filter(p=>!(s!=null&&s.has(p))),...Array.from(s)],bottom:((c=a==null?void 0:a.bottom)!=null?c:[]).filter(p=>!(s!=null&&s.has(p)))}}return{top:((g=a==null?void 0:a.top)!=null?g:[]).filter(p=>!(s!=null&&s.has(p))),bottom:((f=a==null?void 0:a.bottom)!=null?f:[]).filter(p=>!(s!=null&&s.has(p)))}})},e.getCanPin=()=>{var t;const{enableRowPinning:n,enablePinning:i}=o.options;return typeof n=="function"?n(e):(t=n??i)!=null?t:!0},e.getIsPinned=()=>{const t=[e.id],{top:n,bottom:i}=o.getState().rowPinning,r=t.some(s=>n==null?void 0:n.includes(s)),l=t.some(s=>i==null?void 0:i.includes(s));return r?"top":l?"bottom":!1},e.getPinnedIndex=()=>{var t,n;const i=e.getIsPinned();if(!i)return-1;const r=(t=i==="top"?o.getTopRows():o.getBottomRows())==null?void 0:t.map(l=>{let{id:s}=l;return s});return(n=r==null?void 0:r.indexOf(e.id))!=null?n:-1}},createTable:e=>{e.setRowPinning=o=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(o),e.resetRowPinning=o=>{var t,n;return e.setRowPinning(o?k():(t=(n=e.initialState)==null?void 0:n.rowPinning)!=null?t:k())},e.getIsSomeRowsPinned=o=>{var t;const n=e.getState().rowPinning;if(!o){var i,r;return!!((i=n.top)!=null&&i.length||(r=n.bottom)!=null&&r.length)}return!!((t=n[o])!=null&&t.length)},e._getPinnedRows=(o,t,n)=>{var i;return((i=e.options.keepPinnedRows)==null||i?(t??[]).map(l=>{const s=e.getRow(l,!0);return s.getIsAllParentsExpanded()?s:null}):(t??[]).map(l=>o.find(s=>s.id===l))).filter(Boolean).map(l=>({...l,position:n}))},e.getTopRows=m(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(o,t)=>e._getPinnedRows(o,t,"top"),C(e.options,"debugRows")),e.getBottomRows=m(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(o,t)=>e._getPinnedRows(o,t,"bottom"),C(e.options,"debugRows")),e.getCenterRows=m(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(o,t,n)=>{const i=new Set([...t??[],...n??[]]);return o.filter(r=>!i.has(r.id))},C(e.options,"debugRows"))}},Xe={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:$("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=o=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(o),e.resetRowSelection=o=>{var t;return e.setRowSelection(o?{}:(t=e.initialState.rowSelection)!=null?t:{})},e.toggleAllRowsSelected=o=>{e.setRowSelection(t=>{o=typeof o<"u"?o:!e.getIsAllRowsSelected();const n={...t},i=e.getPreGroupedRowModel().flatRows;return o?i.forEach(r=>{r.getCanSelect()&&(n[r.id]=!0)}):i.forEach(r=>{delete n[r.id]}),n})},e.toggleAllPageRowsSelected=o=>e.setRowSelection(t=>{const n=typeof o<"u"?o:!e.getIsAllPageRowsSelected(),i={...t};return e.getRowModel().rows.forEach(r=>{K(i,r.id,n,!0,e)}),i}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=m(()=>[e.getState().rowSelection,e.getCoreRowModel()],(o,t)=>Object.keys(o).length?j(e,t):{rows:[],flatRows:[],rowsById:{}},C(e.options,"debugTable")),e.getFilteredSelectedRowModel=m(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(o,t)=>Object.keys(o).length?j(e,t):{rows:[],flatRows:[],rowsById:{}},C(e.options,"debugTable")),e.getGroupedSelectedRowModel=m(()=>[e.getState().rowSelection,e.getSortedRowModel()],(o,t)=>Object.keys(o).length?j(e,t):{rows:[],flatRows:[],rowsById:{}},C(e.options,"debugTable")),e.getIsAllRowsSelected=()=>{const o=e.getFilteredRowModel().flatRows,{rowSelection:t}=e.getState();let n=!!(o.length&&Object.keys(t).length);return n&&o.some(i=>i.getCanSelect()&&!t[i.id])&&(n=!1),n},e.getIsAllPageRowsSelected=()=>{const o=e.getPaginationRowModel().flatRows.filter(i=>i.getCanSelect()),{rowSelection:t}=e.getState();let n=!!o.length;return n&&o.some(i=>!t[i.id])&&(n=!1),n},e.getIsSomeRowsSelected=()=>{var o;const t=Object.keys((o=e.getState().rowSelection)!=null?o:{}).length;return t>0&&t<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const o=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:o.filter(t=>t.getCanSelect()).some(t=>t.getIsSelected()||t.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>o=>{e.toggleAllRowsSelected(o.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>o=>{e.toggleAllPageRowsSelected(o.target.checked)}},createRow:(e,o)=>{e.toggleSelected=(t,n)=>{const i=e.getIsSelected();o.setRowSelection(r=>{var l;if(t=typeof t<"u"?t:!i,e.getCanSelect()&&i===t)return r;const s={...r};return K(s,e.id,t,(l=n==null?void 0:n.selectChildren)!=null?l:!0,o),s})},e.getIsSelected=()=>{const{rowSelection:t}=o.getState();return Z(e,t)},e.getIsSomeSelected=()=>{const{rowSelection:t}=o.getState();return J(e,t)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:t}=o.getState();return J(e,t)==="all"},e.getCanSelect=()=>{var t;return typeof o.options.enableRowSelection=="function"?o.options.enableRowSelection(e):(t=o.options.enableRowSelection)!=null?t:!0},e.getCanSelectSubRows=()=>{var t;return typeof o.options.enableSubRowSelection=="function"?o.options.enableSubRowSelection(e):(t=o.options.enableSubRowSelection)!=null?t:!0},e.getCanMultiSelect=()=>{var t;return typeof o.options.enableMultiRowSelection=="function"?o.options.enableMultiRowSelection(e):(t=o.options.enableMultiRowSelection)!=null?t:!0},e.getToggleSelectedHandler=()=>{const t=e.getCanSelect();return n=>{var i;t&&e.toggleSelected((i=n.target)==null?void 0:i.checked)}}}},K=(e,o,t,n,i)=>{var r;const l=i.getRow(o,!0);t?(l.getCanMultiSelect()||Object.keys(e).forEach(s=>delete e[s]),l.getCanSelect()&&(e[o]=!0)):delete e[o],n&&(r=l.subRows)!=null&&r.length&&l.getCanSelectSubRows()&&l.subRows.forEach(s=>K(e,s.id,t,n,i))};function j(e,o){const t=e.getState().rowSelection,n=[],i={},r=function(l,s){return l.map(a=>{var g;const f=Z(a,t);if(f&&(n.push(a),i[a.id]=a),(g=a.subRows)!=null&&g.length&&(a={...a,subRows:r(a.subRows)}),f)return a}).filter(Boolean)};return{rows:r(o.rows),flatRows:n,rowsById:i}}function Z(e,o){var t;return(t=o[e.id])!=null?t:!1}function J(e,o,t){var n;if(!((n=e.subRows)!=null&&n.length))return!1;let i=!0,r=!1;return e.subRows.forEach(l=>{if(!(r&&!i)&&(l.getCanSelect()&&(Z(l,o)?r=!0:i=!1),l.subRows&&l.subRows.length)){const s=J(l,o);s==="all"?r=!0:(s==="some"&&(r=!0),i=!1)}}),i?"all":r?"some":!1}const Q=/([0-9]+)/gm,Ke=(e,o,t)=>ce(I(e.getValue(t)).toLowerCase(),I(o.getValue(t)).toLowerCase()),Je=(e,o,t)=>ce(I(e.getValue(t)),I(o.getValue(t))),Qe=(e,o,t)=>b(I(e.getValue(t)).toLowerCase(),I(o.getValue(t)).toLowerCase()),We=(e,o,t)=>b(I(e.getValue(t)),I(o.getValue(t))),Ye=(e,o,t)=>{const n=e.getValue(t),i=o.getValue(t);return n>i?1:n<i?-1:0},Ze=(e,o,t)=>b(e.getValue(t),o.getValue(t));function b(e,o){return e===o?0:e>o?1:-1}function I(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function ce(e,o){const t=e.split(Q).filter(Boolean),n=o.split(Q).filter(Boolean);for(;t.length&&n.length;){const i=t.shift(),r=n.shift(),l=parseInt(i,10),s=parseInt(r,10),a=[l,s].sort();if(isNaN(a[0])){if(i>r)return 1;if(r>i)return-1;continue}if(isNaN(a[1]))return isNaN(l)?-1:1;if(l>s)return 1;if(s>l)return-1}return t.length-n.length}const E={alphanumeric:Ke,alphanumericCaseSensitive:Je,text:Qe,textCaseSensitive:We,datetime:Ye,basic:Ze},be={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:$("sorting",e),isMultiSortEvent:o=>o.shiftKey}),createColumn:(e,o)=>{e.getAutoSortingFn=()=>{const t=o.getFilteredRowModel().flatRows.slice(10);let n=!1;for(const i of t){const r=i==null?void 0:i.getValue(e.id);if(Object.prototype.toString.call(r)==="[object Date]")return E.datetime;if(typeof r=="string"&&(n=!0,r.split(Q).length>1))return E.alphanumeric}return n?E.text:E.basic},e.getAutoSortDir=()=>{const t=o.getFilteredRowModel().flatRows[0];return typeof(t==null?void 0:t.getValue(e.id))=="string"?"asc":"desc"},e.getSortingFn=()=>{var t,n;if(!e)throw new Error;return L(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(t=(n=o.options.sortingFns)==null?void 0:n[e.columnDef.sortingFn])!=null?t:E[e.columnDef.sortingFn]},e.toggleSorting=(t,n)=>{const i=e.getNextSortingOrder(),r=typeof t<"u"&&t!==null;o.setSorting(l=>{const s=l==null?void 0:l.find(u=>u.id===e.id),a=l==null?void 0:l.findIndex(u=>u.id===e.id);let g=[],f,S=r?t:i==="desc";if(l!=null&&l.length&&e.getCanMultiSort()&&n?s?f="toggle":f="add":l!=null&&l.length&&a!==l.length-1?f="replace":s?f="toggle":f="replace",f==="toggle"&&(r||i||(f="remove")),f==="add"){var d;g=[...l,{id:e.id,desc:S}],g.splice(0,g.length-((d=o.options.maxMultiSortColCount)!=null?d:Number.MAX_SAFE_INTEGER))}else f==="toggle"?g=l.map(u=>u.id===e.id?{...u,desc:S}:u):f==="remove"?g=l.filter(u=>u.id!==e.id):g=[{id:e.id,desc:S}];return g})},e.getFirstSortDir=()=>{var t,n;return((t=(n=e.columnDef.sortDescFirst)!=null?n:o.options.sortDescFirst)!=null?t:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=t=>{var n,i;const r=e.getFirstSortDir(),l=e.getIsSorted();return l?l!==r&&((n=o.options.enableSortingRemoval)==null||n)&&(!(t&&(i=o.options.enableMultiRemove)!=null)||i)?!1:l==="desc"?"asc":"desc":r},e.getCanSort=()=>{var t,n;return((t=e.columnDef.enableSorting)!=null?t:!0)&&((n=o.options.enableSorting)!=null?n:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var t,n;return(t=(n=e.columnDef.enableMultiSort)!=null?n:o.options.enableMultiSort)!=null?t:!!e.accessorFn},e.getIsSorted=()=>{var t;const n=(t=o.getState().sorting)==null?void 0:t.find(i=>i.id===e.id);return n?n.desc?"desc":"asc":!1},e.getSortIndex=()=>{var t,n;return(t=(n=o.getState().sorting)==null?void 0:n.findIndex(i=>i.id===e.id))!=null?t:-1},e.clearSorting=()=>{o.setSorting(t=>t!=null&&t.length?t.filter(n=>n.id!==e.id):[])},e.getToggleSortingHandler=()=>{const t=e.getCanSort();return n=>{t&&(n.persist==null||n.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?o.options.isMultiSortEvent==null?void 0:o.options.isMultiSortEvent(n):!1))}}},createTable:e=>{e.setSorting=o=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(o),e.resetSorting=o=>{var t,n;e.setSorting(o?[]:(t=(n=e.initialState)==null?void 0:n.sorting)!=null?t:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},et=[he,Te,Ae,Le,_e,Fe,qe,ke,be,Ge,je,Ue,Ne,Xe,Oe];function tt(e){var o,t;const n=[...et,...(o=e._features)!=null?o:[]];let i={_features:n};const r=i._features.reduce((d,u)=>Object.assign(d,u.getDefaultOptions==null?void 0:u.getDefaultOptions(i)),{}),l=d=>i.options.mergeOptions?i.options.mergeOptions(r,d):{...r,...d};let a={...{},...(t=e.initialState)!=null?t:{}};i._features.forEach(d=>{var u;a=(u=d.getInitialState==null?void 0:d.getInitialState(a))!=null?u:a});const g=[];let f=!1;const S={_features:n,options:{...r,...e},initialState:a,_queue:d=>{g.push(d),f||(f=!0,Promise.resolve().then(()=>{for(;g.length;)g.shift()();f=!1}).catch(u=>setTimeout(()=>{throw u})))},reset:()=>{i.setState(i.initialState)},setOptions:d=>{const u=x(d,i.options);i.options=l(u)},getState:()=>i.options.state,setState:d=>{i.options.onStateChange==null||i.options.onStateChange(d)},_getRowId:(d,u,c)=>{var p;return(p=i.options.getRowId==null?void 0:i.options.getRowId(d,u,c))!=null?p:`${c?[c.id,u].join("."):u}`},getCoreRowModel:()=>(i._getCoreRowModel||(i._getCoreRowModel=i.options.getCoreRowModel(i)),i._getCoreRowModel()),getRowModel:()=>i.getPaginationRowModel(),getRow:(d,u)=>{let c=(u?i.getPrePaginationRowModel():i.getRowModel()).rowsById[d];if(!c&&(c=i.getCoreRowModel().rowsById[d],!c))throw new Error;return c},_getDefaultColumnDef:m(()=>[i.options.defaultColumn],d=>{var u;return d=(u=d)!=null?u:{},{header:c=>{const p=c.header.column.columnDef;return p.accessorKey?p.accessorKey:p.accessorFn?p.id:null},cell:c=>{var p,R;return(p=(R=c.renderValue())==null||R.toString==null?void 0:R.toString())!=null?p:null},...i._features.reduce((c,p)=>Object.assign(c,p.getDefaultColumnDef==null?void 0:p.getDefaultColumnDef()),{}),...d}},C(e,"debugColumns")),_getColumnDefs:()=>i.options.columns,getAllColumns:m(()=>[i._getColumnDefs()],d=>{const u=function(c,p,R){return R===void 0&&(R=0),c.map(w=>{const v=ve(i,w,R,p),h=w;return v.columns=h.columns?u(h.columns,v,R+1):[],v})};return u(d)},C(e,"debugColumns")),getAllFlatColumns:m(()=>[i.getAllColumns()],d=>d.flatMap(u=>u.getFlatColumns()),C(e,"debugColumns")),_getAllFlatColumnsById:m(()=>[i.getAllFlatColumns()],d=>d.reduce((u,c)=>(u[c.id]=c,u),{}),C(e,"debugColumns")),getAllLeafColumns:m(()=>[i.getAllColumns(),i._getOrderColumnsFn()],(d,u)=>{let c=d.flatMap(p=>p.getLeafColumns());return u(c)},C(e,"debugColumns")),getColumn:d=>i._getAllFlatColumnsById()[d]};Object.assign(i,S);for(let d=0;d<i._features.length;d++){const u=i._features[d];u==null||u.createTable==null||u.createTable(i)}return i}function at(){return e=>m(()=>[e.options.data],o=>{const t={rows:[],flatRows:[],rowsById:{}},n=function(i,r,l){r===void 0&&(r=0);const s=[];for(let g=0;g<i.length;g++){const f=W(e,e._getRowId(i[g],g,l),i[g],g,r,void 0,l==null?void 0:l.id);if(t.flatRows.push(f),t.rowsById[f.id]=f,s.push(f),e.options.getSubRows){var a;f.originalSubRows=e.options.getSubRows(i[g],g),(a=f.originalSubRows)!=null&&a.length&&(f.subRows=n(f.originalSubRows,r+1,f))}}return s};return t.rows=n(o),t},C(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function nt(e){const o=[],t=n=>{var i;o.push(n),(i=n.subRows)!=null&&i.length&&n.getIsExpanded()&&n.subRows.forEach(t)};return e.rows.forEach(t),{rows:o,flatRows:e.flatRows,rowsById:e.rowsById}}function pe(e,o,t){return t.options.filterFromLeafRows?ot(e,o,t):it(e,o,t)}function ot(e,o,t){var n;const i=[],r={},l=(n=t.options.maxLeafRowFilterDepth)!=null?n:100,s=function(a,g){g===void 0&&(g=0);const f=[];for(let d=0;d<a.length;d++){var S;let u=a[d];const c=W(t,u.id,u.original,u.index,u.depth,void 0,u.parentId);if(c.columnFilters=u.columnFilters,(S=u.subRows)!=null&&S.length&&g<l){if(c.subRows=s(u.subRows,g+1),u=c,o(u)&&!c.subRows.length){f.push(u),r[u.id]=u,i.push(u);continue}if(o(u)||c.subRows.length){f.push(u),r[u.id]=u,i.push(u);continue}}else u=c,o(u)&&(f.push(u),r[u.id]=u,i.push(u))}return f};return{rows:s(e),flatRows:i,rowsById:r}}function it(e,o,t){var n;const i=[],r={},l=(n=t.options.maxLeafRowFilterDepth)!=null?n:100,s=function(a,g){g===void 0&&(g=0);const f=[];for(let d=0;d<a.length;d++){let u=a[d];if(o(u)){var S;if((S=u.subRows)!=null&&S.length&&g<l){const p=W(t,u.id,u.original,u.index,u.depth,void 0,u.parentId);p.subRows=s(u.subRows,g+1),u=p}f.push(u),i.push(u),r[u.id]=u}}return f};return{rows:s(e),flatRows:i,rowsById:r}}function gt(){return(e,o)=>m(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter,e.getFilteredRowModel()],(t,n,i)=>{if(!t.rows.length||!(n!=null&&n.length)&&!i)return t;const r=[...n.map(s=>s.id).filter(s=>s!==o),i?"__global__":void 0].filter(Boolean),l=s=>{for(let a=0;a<r.length;a++)if(s.columnFilters[r[a]]===!1)return!1;return!0};return pe(t.rows,l,e)},C(e.options,"debugTable"))}function dt(){return(e,o)=>m(()=>{var t;return[(t=e.getColumn(o))==null?void 0:t.getFacetedRowModel()]},t=>{if(!t)return new Map;let n=new Map;for(let r=0;r<t.flatRows.length;r++){const l=t.flatRows[r].getUniqueValues(o);for(let s=0;s<l.length;s++){const a=l[s];if(n.has(a)){var i;n.set(a,((i=n.get(a))!=null?i:0)+1)}else n.set(a,1)}}return n},C(e.options,"debugTable"))}function ft(){return e=>m(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(o,t,n)=>{if(!o.rows.length||!(t!=null&&t.length)&&!n){for(let d=0;d<o.flatRows.length;d++)o.flatRows[d].columnFilters={},o.flatRows[d].columnFiltersMeta={};return o}const i=[],r=[];(t??[]).forEach(d=>{var u;const c=e.getColumn(d.id);if(!c)return;const p=c.getFilterFn();p&&i.push({id:d.id,filterFn:p,resolvedValue:(u=p.resolveFilterValue==null?void 0:p.resolveFilterValue(d.value))!=null?u:d.value})});const l=(t??[]).map(d=>d.id),s=e.getGlobalFilterFn(),a=e.getAllLeafColumns().filter(d=>d.getCanGlobalFilter());n&&s&&a.length&&(l.push("__global__"),a.forEach(d=>{var u;r.push({id:d.id,filterFn:s,resolvedValue:(u=s.resolveFilterValue==null?void 0:s.resolveFilterValue(n))!=null?u:n})}));let g,f;for(let d=0;d<o.flatRows.length;d++){const u=o.flatRows[d];if(u.columnFilters={},i.length)for(let c=0;c<i.length;c++){g=i[c];const p=g.id;u.columnFilters[p]=g.filterFn(u,p,g.resolvedValue,R=>{u.columnFiltersMeta[p]=R})}if(r.length){for(let c=0;c<r.length;c++){f=r[c];const p=f.id;if(f.filterFn(u,p,f.resolvedValue,R=>{u.columnFiltersMeta[p]=R})){u.columnFilters.__global__=!0;break}}u.columnFilters.__global__!==!0&&(u.columnFilters.__global__=!1)}}const S=d=>{for(let u=0;u<l.length;u++)if(d.columnFilters[l[u]]===!1)return!1;return!0};return pe(o.rows,S,e)},C(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex()))}function ct(e){return o=>m(()=>[o.getState().pagination,o.getPrePaginationRowModel(),o.options.paginateExpandedRows?void 0:o.getState().expanded],(t,n)=>{if(!n.rows.length)return n;const{pageSize:i,pageIndex:r}=t;let{rows:l,flatRows:s,rowsById:a}=n;const g=i*r,f=g+i;l=l.slice(g,f);let S;o.options.paginateExpandedRows?S={rows:l,flatRows:s,rowsById:a}:S=nt({rows:l,flatRows:s,rowsById:a}),S.flatRows=[];const d=u=>{S.flatRows.push(u),u.subRows.length&&u.subRows.forEach(d)};return S.rows.forEach(d),S},C(o.options,"debugTable"))}function pt(){return e=>m(()=>[e.getState().sorting,e.getPreSortedRowModel()],(o,t)=>{if(!t.rows.length||!(o!=null&&o.length))return t;const n=e.getState().sorting,i=[],r=n.filter(a=>{var g;return(g=e.getColumn(a.id))==null?void 0:g.getCanSort()}),l={};r.forEach(a=>{const g=e.getColumn(a.id);g&&(l[a.id]={sortUndefined:g.columnDef.sortUndefined,invertSorting:g.columnDef.invertSorting,sortingFn:g.getSortingFn()})});const s=a=>{const g=a.map(f=>({...f}));return g.sort((f,S)=>{for(let u=0;u<r.length;u+=1){var d;const c=r[u],p=l[c.id],R=p.sortUndefined,w=(d=c==null?void 0:c.desc)!=null?d:!1;let v=0;if(R){const h=f.getValue(c.id),M=S.getValue(c.id),F=h===void 0,y=M===void 0;if(F||y){if(R==="first")return F?-1:1;if(R==="last")return F?1:-1;v=F&&y?0:F?R:-R}}if(v===0&&(v=p.sortingFn(f,S,c.id)),v!==0)return w&&(v*=-1),p.invertSorting&&(v*=-1),v}return f.index-S.index}),g.forEach(f=>{var S;i.push(f),(S=f.subRows)!=null&&S.length&&(f.subRows=s(f.subRows))}),g};return{rows:s(t.rows),flatRows:i,rowsById:t.rowsById}},C(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function St(e,o){return e?rt(e)?U.createElement(e,o):e:null}function rt(e){return lt(e)||typeof e=="function"||st(e)}function lt(e){return typeof e=="function"&&(()=>{const o=Object.getPrototypeOf(e);return o.prototype&&o.prototype.isReactComponent})()}function st(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function mt(e){const o={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[t]=U.useState(()=>({current:tt(o)})),[n,i]=U.useState(()=>t.current.initialState);return t.current.setOptions(r=>({...r,...e,state:{...n,...e.state},onStateChange:l=>{i(l),e.onStateChange==null||e.onStateChange(l)}})),t.current}export{gt as a,pt as b,ct as c,ft as d,at as e,St as f,dt as g,mt as u};
