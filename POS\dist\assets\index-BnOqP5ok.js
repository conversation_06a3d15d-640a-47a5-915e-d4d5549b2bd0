import{z as o,j as e,L as f,c as u,B as h}from"./index-Bnt3OGV2.js";import{C as b}from"./content-section-Cz2fnFL4.js";import{u as y,f as v,F,a,b as n,c as l,d as t,g as i,e as m}from"./form-wT1R35uI.js";import{s as S}from"./zod-CNC8A7Cl.js";import{I as p}from"./input-CiKEYbig.js";import{S as C,a as U,b as I,c as L,d}from"./select-Czd7KcZQ.js";import{T}from"./textarea-Da8YG9AB.js";import"./separator-CClVRZ9M.js";import"./index-Bl1CGAiZ.js";import"./index-BT7Z3RDV.js";import"./index-C2T2k_Lh.js";import"./createLucideIcon-CNa_hh6B.js";import"./check-apx2eTVC.js";const w=o.object({username:o.string().min(2,{message:"Username must be at least 2 characters."}).max(30,{message:"Username must not be longer than 30 characters."}),email:o.string({required_error:"Please select an email to display."}).email(),bio:o.string().max(160).min(4),urls:o.array(o.object({value:o.string().url({message:"Please enter a valid URL."})})).optional()}),N={bio:"I own a computer.",urls:[{value:"https://shadcn.com"},{value:"http://twitter.com/shadcn"}]};function P(){const r=y({resolver:S(w),defaultValues:N,mode:"onChange"}),{fields:x,append:j}=v({name:"urls",control:r.control});return e.jsx(F,{...r,children:e.jsxs("form",{onSubmit:r.handleSubmit(s=>{}),className:"space-y-8",children:[e.jsx(a,{control:r.control,name:"username",render:({field:s})=>e.jsxs(n,{children:[e.jsx(l,{children:"Username"}),e.jsx(t,{children:e.jsx(p,{placeholder:"shadcn",...s})}),e.jsx(i,{children:"This is your public display name. It can be your real name or a pseudonym. You can only change this once every 30 days."}),e.jsx(m,{})]})}),e.jsx(a,{control:r.control,name:"email",render:({field:s})=>e.jsxs(n,{children:[e.jsx(l,{children:"Email"}),e.jsxs(C,{onValueChange:s.onChange,defaultValue:s.value,children:[e.jsx(t,{children:e.jsx(U,{children:e.jsx(I,{placeholder:"Select a verified email to display"})})}),e.jsxs(L,{children:[e.jsx(d,{value:"<EMAIL>",children:"<EMAIL>"}),e.jsx(d,{value:"<EMAIL>",children:"<EMAIL>"}),e.jsx(d,{value:"<EMAIL>",children:"<EMAIL>"})]})]}),e.jsxs(i,{children:["You can manage verified email addresses in your ",e.jsx(f,{to:"/",children:"email settings"}),"."]}),e.jsx(m,{})]})}),e.jsx(a,{control:r.control,name:"bio",render:({field:s})=>e.jsxs(n,{children:[e.jsx(l,{children:"Bio"}),e.jsx(t,{children:e.jsx(T,{placeholder:"Tell us a little bit about yourself",className:"resize-none",...s})}),e.jsxs(i,{children:["You can ",e.jsx("span",{children:"@mention"})," other users and organizations to link to them."]}),e.jsx(m,{})]})}),e.jsxs("div",{children:[x.map((s,c)=>e.jsx(a,{control:r.control,name:`urls.${c}.value`,render:({field:g})=>e.jsxs(n,{children:[e.jsx(l,{className:u(c!==0&&"sr-only"),children:"URLs"}),e.jsx(i,{className:u(c!==0&&"sr-only"),children:"Add links to your website, blog, or social media profiles."}),e.jsx(t,{children:e.jsx(p,{...g})}),e.jsx(m,{})]})},s.id)),e.jsx(h,{type:"button",variant:"outline",size:"sm",className:"mt-2",onClick:()=>j({value:""}),children:"Add URL"})]}),e.jsx(h,{type:"submit",children:"Update profile"})]})})}function k(){return e.jsx(b,{title:"Profile",desc:"This is how others will see you on the site.",children:e.jsx(P,{})})}const H=k;export{H as component};
