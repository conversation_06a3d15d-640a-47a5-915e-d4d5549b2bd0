import{j as e,B as z,r as C,a as G,l as O,b as B,h as K}from"./index-Bnt3OGV2.js";import"./pos-api-BwpRFGce.js";import"./vietqr-api-DAENYiJ_.js";import{u as Q,a as Z,b as ee,c as te}from"./use-membership-discounts-DeAGlCi2.js";import"./user-oq7iQk7S.js";import"./crm-api-Dd9UhSCJ.js";import{H as se}from"./header-stuEr_6l.js";import{M as ne}from"./main-Dj7NWzIf.js";import{P as ae}from"./profile-dropdown-BQhzNXaW.js";import{S as re,T as ie}from"./search-BwtQTbOQ.js";import"./date-range-picker-CVvofQC0.js";import"./form-wT1R35uI.js";import{C as oe,S as b,a as S,b as y,c as N,d as v}from"./select-Czd7KcZQ.js";import{D as le,a as ce,b as de,c as me}from"./dropdown-menu-BjJ0HZAV.js";import{P as he}from"./modal-B0J8RkN-.js";import{C as M}from"./checkbox-BiVztVsP.js";import{T as k,d as L,b as P,e as x,a as pe,c as xe}from"./table-C602nYEy.js";import{B as ue}from"./badge-u6qfWPSj.js";import"./discount-form-context-D6Jd4mst.js";import{u as ge}from"./use-combos-B4mR1i3R.js";import"./useQuery-DSrD7NAp.js";import"./utils-km2FGkQ4.js";import"./useMutation-d67-fNFq.js";import"./discounts-DCn5I4UW.js";import"./query-keys-3lmd-xp6.js";import"./separator-CClVRZ9M.js";import"./avatar-D33aZz95.js";import"./search-context-DLufo9i0.js";import"./command-ByfqjQDn.js";import"./calendar-CzR6WBaB.js";import"./createLucideIcon-CNa_hh6B.js";import"./index-BT7Z3RDV.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-hQ-PVOWr.js";import"./search-BVQVKwPC.js";import"./createReactComponent-BD5R5KSl.js";import"./scroll-area-BeVbW7LP.js";import"./index-Bl1CGAiZ.js";import"./IconChevronRight-BM-o6vT_.js";import"./IconSearch-DCdKv7Cy.js";import"./chevron-right-sZt3EK3r.js";import"./react-icons.esm-B7rNr9e-.js";import"./popover-C2mvzdeD.js";import"./index-C2T2k_Lh.js";import"./check-apx2eTVC.js";import"./index-UiaF_xtq.js";const je=[{value:"all",label:"Tất cả trạng thái"},{value:"1",label:"Active"},{value:"0",label:"Deactive"}],ve=[{value:"all",label:"Tất cả ngày áp dụng"},{value:"expired",label:"Hết hạn"},{value:"unexpired",label:"Chưa hết hạn"}];function fe({selectedStoreId:t,onStoreChange:r,stores:s}){return e.jsx("div",{className:"min-w-[160px]",children:e.jsxs(b,{value:t,onValueChange:r,children:[e.jsx(S,{children:e.jsx(y,{placeholder:"Tất cả các điểm"})}),e.jsxs(N,{children:[e.jsx(v,{value:"all",children:"Tất cả các điểm"}),s.map(i=>e.jsx(v,{value:i.id,children:i.store_name},i.id))]})]})})}function Ce({selectedPromotion:t,onPromotionChange:r,promotions:s}){return e.jsx("div",{className:"min-w-[160px]",children:e.jsxs(b,{value:t,onValueChange:r,children:[e.jsx(S,{children:e.jsx(y,{placeholder:"Tất cả các CKTM"})}),e.jsxs(N,{children:[e.jsx(v,{value:"all",children:"Tất cả các CKTM"}),s.map(i=>e.jsx(v,{value:i.promotion_id,children:i.promotion_name},i.promotion_id))]})]})})}function be({selectedStatus:t,onStatusChange:r}){return e.jsx("div",{className:"min-w-[130px]",children:e.jsxs(b,{value:t,onValueChange:r,children:[e.jsx(S,{children:e.jsx(y,{placeholder:"Tất cả trạng thái"})}),e.jsx(N,{children:je.map(s=>e.jsx(v,{value:s.value,children:s.label},s.value))})]})})}function Se({selectedExpiry:t,onExpiryChange:r}){return e.jsx("div",{className:"min-w-[150px]",children:e.jsxs(b,{value:t,onValueChange:r,children:[e.jsx(S,{children:e.jsx(y,{placeholder:"Tất cả ngày áp dụng"})}),e.jsx(N,{children:ve.map(s=>e.jsx(v,{value:s.value,children:s.label},s.value))})]})})}function ye({selectedStoreId:t,onStoreChange:r,selectedPromotion:s,onPromotionChange:i,selectedStatus:l,onStatusChange:a,selectedExpiry:o,onExpiryChange:c,stores:g,promotions:m,onCopyModalOpen:h,onCreateDiscount:j}){return e.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[e.jsx("h2",{className:"text-xl font-semibold whitespace-nowrap",children:"Thông tin giảm giá *"}),e.jsx(fe,{selectedStoreId:t,onStoreChange:r,stores:g}),e.jsx(Ce,{selectedPromotion:s,onPromotionChange:i,promotions:m}),e.jsx(be,{selectedStatus:l,onStatusChange:a}),e.jsx(Se,{selectedExpiry:o,onExpiryChange:c}),e.jsxs("div",{className:"ml-auto flex items-center gap-2",children:[e.jsxs(le,{children:[e.jsx(ce,{asChild:!0,children:e.jsxs(z,{variant:"outline",size:"sm",children:["Tiện ích ",e.jsx(oe,{className:"ml-2 h-4 w-4"})]})}),e.jsx(de,{children:e.jsx(me,{onClick:h,children:"Sao chép CTGG"})})]}),e.jsx(z,{size:"sm",onClick:j,children:"Tạo giảm giá"})]})]})}function Ne({isOpen:t,onClose:r}){const[s,i]=C.useState(""),[l,a]=C.useState(new Set),[o,c]=C.useState(new Set),{currentBrandStores:g}=G(),{selectedBrand:m}=O(),{company:h}=B(),j=Q(),{data:p=[],isLoading:T}=Z({companyUid:h==null?void 0:h.id,brandUid:m==null?void 0:m.id,storeUid:s,enabled:!!s}),u=g.filter(n=>n.id!==s),_=(n,d)=>{const f=new Set(n);return f.has(d)?f.delete(d):f.add(d),f},A=n=>{c(d=>_(d,n))},I=n=>{a(d=>_(d,n))},W=l.size===u.length,F=()=>{a(W?new Set:new Set(u.map(n=>n.id)))},q=(h==null?void 0:h.id)&&(m==null?void 0:m.id)&&s,Y=l.size>0&&o.size>0,X=q&&Y,J=async()=>{if(X)try{const n=Array.from(o),d=Array.from(l);for(const f of d)await j.mutateAsync({companyUid:h.id,brandUid:m.id,listDiscountUid:n,storeUidRoot:s,storeUidTarget:f});U()}catch(n){console.error("Error copying discount programs:",n)}},U=()=>{i(""),a(new Set),c(new Set),r()},E=p.filter(n=>o.has(n.id));return e.jsx(he,{title:"Sao chép CTGG",open:t,onOpenChange:U,onCancel:U,onConfirm:J,confirmText:"Sao chép",confirmDisabled:!s||l.size===0||o.size===0||j.isPending,maxWidth:"sm:max-w-6xl",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium",children:"Cửa hàng nguồn"}),e.jsxs(b,{value:s,onValueChange:i,children:[e.jsx(S,{className:"mt-1",children:e.jsx(y,{placeholder:"Chọn cửa hàng nguồn"})}),e.jsx(N,{children:g.map(n=>e.jsx(v,{value:n.id,children:n.store_name},n.id))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Chương trình giảm giá"}),e.jsxs("div",{className:"max-h-64 overflow-y-auto rounded-md border p-3",children:[T&&e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:"Đang tải..."}),!T&&p.length===0&&e.jsxs("div",{className:"text-muted-foreground py-4 text-center text-sm",children:[s&&"Không có chương trình giảm giá",!s&&"Chọn cửa hàng nguồn"]}),!T&&p.length>0&&e.jsx("div",{className:"space-y-3",children:p.map(n=>{var d;return e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(M,{id:n.id,checked:o.has(n.id),onCheckedChange:()=>A(n.id)}),e.jsx("div",{className:"min-w-0 flex-1",children:e.jsxs("div",{className:"text-sm font-medium",children:[((d=n.promotion)==null?void 0:d.promotion_name)||"N/A"," - ",n.membership_type_name||"N/A"]})})]},n.id)})})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium",children:"Cửa hàng đích"}),e.jsxs(b,{children:[e.jsx(S,{className:"mt-1",children:e.jsx(y,{placeholder:l.size===0?"Chọn cửa hàng đích":`Đã chọn ${l.size} cửa hàng`})}),e.jsxs(N,{children:[u.length===0&&e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:"Chọn cửa hàng nguồn trước"}),u.length>0&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center space-x-3 border-b px-2 py-1.5",children:[e.jsx(M,{id:"select-all-targets",checked:l.size===u.length&&u.length>0,onCheckedChange:F,onClick:n=>n.stopPropagation()}),e.jsx("label",{htmlFor:"select-all-targets",className:"flex-1 cursor-pointer text-sm font-medium",onClick:n=>{n.preventDefault(),F()},children:"Chọn tất cả"})]}),u.map(n=>e.jsxs("div",{className:"flex items-center space-x-3 px-2 py-1.5",children:[e.jsx(M,{id:`target-${n.id}`,checked:l.has(n.id),onCheckedChange:()=>I(n.id),onClick:d=>d.stopPropagation()}),e.jsx("label",{htmlFor:`target-${n.id}`,className:"flex-1 cursor-pointer text-sm font-medium",onClick:d=>{d.preventDefault(),I(n.id)},children:n.store_name})]},n.id))]})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Chương trình được chọn"}),e.jsxs("div",{className:"max-h-64 overflow-y-auto rounded-md border p-3",children:[E.length===0&&e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:"Chưa chọn chương trình nào"}),E.length>0&&e.jsx("div",{className:"space-y-2",children:E.map(n=>{var d;return e.jsx("div",{className:"bg-muted rounded-md p-2",children:e.jsxs("div",{className:"text-sm font-medium",children:[((d=n.promotion)==null?void 0:d.promotion_name)||"N/A"," - ",n.membership_type_name||"N/A"]})},n.id)})})]})]})]})]})})}const R=["#","Tên CT","Cửa hàng","Hạng thành viên","Tuỳ chỉnh","GG ngày thường","GG sinh nhật","Thao tác"],H={ACTIVE:"cursor-pointer bg-green-100 text-green-800 hover:bg-green-200",INACTIVE:"cursor-pointer bg-red-100 text-red-800 hover:bg-red-200"},$=(t,r)=>{const s=t[r];return t.discountType==="AMOUNT"?`${s.toLocaleString("vi-VN")}đ`:`${s*100}%`},Te=(t,r)=>{const s=new Date(t).toLocaleDateString("vi-VN"),i=new Date(r).toLocaleDateString("vi-VN");return`Từ ${s} đến ${i}`},De=t=>t.isAll===1?"(Áp dụng cho tất cả)":t.typeId?`(Áp dụng cho ${t.typeId.split(",").length} nhóm)`:"";function V(){return e.jsx(pe,{children:e.jsx(P,{children:R.map(t=>e.jsx(xe,{children:t},t))})})}function _e({discount:t,onToggleActive:r}){const s=t.active===1,i=s?"Activate":"Deactivate",l=s?H.ACTIVE:H.INACTIVE,a=s?"default":"destructive";return e.jsx(ue,{variant:a,className:l,onClick:o=>{o.stopPropagation(),r(t)},children:i})}function Ae({discounts:t,isLoading:r,onToggleActive:s}){const i=K(),l=(a,o)=>{const c=o.target;c.closest("button")||c.closest('[role="button"]')||c.closest(".badge")||c.tagName==="BUTTON"||i({to:"/sale/discount/membership/detail/$id",params:{id:a.id},search:{store_uid:a.storeUid}})};return r?e.jsx("div",{className:"rounded-md border",children:e.jsxs(k,{children:[e.jsx(V,{}),e.jsx(L,{children:e.jsx(P,{children:e.jsx(x,{colSpan:R.length,className:"h-24 text-center",children:"Đang tải dữ liệu..."})})})]})}):t.length===0?e.jsx("div",{className:"rounded-md border",children:e.jsxs(k,{children:[e.jsx(V,{}),e.jsx(L,{children:e.jsx(P,{children:e.jsx(x,{colSpan:R.length,className:"text-muted-foreground h-24 text-center",children:"Không có dữ liệu"})})})]})}):e.jsx("div",{className:"rounded-md border",children:e.jsxs(k,{children:[e.jsx(V,{}),e.jsx(L,{children:t.map((a,o)=>e.jsxs(P,{className:"hover:bg-muted/50 cursor-pointer",onClick:c=>l(a,c),children:[e.jsx(x,{children:o+1}),e.jsx(x,{children:e.jsx("div",{className:"font-medium",children:a.promotionName||"N/A"})}),e.jsx(x,{children:a.storeName}),e.jsx(x,{children:e.jsx("div",{className:"font-medium",children:a.membershipTypeName})}),e.jsx(x,{children:e.jsxs("div",{className:"space-y-1",children:[a.toDate<Date.now()&&e.jsx("div",{className:"font-medium text-red-600",children:"Hết hạn"}),a.toDate>=Date.now()&&e.jsx("div",{className:"text-sm",children:Te(a.fromDate,a.toDate)}),e.jsxs("div",{className:"text-xs text-gray-600",children:["Áp dụng trước và sau sinh nhật ",a.birthTimeRangeApply," ngày"]}),e.jsx("div",{className:"text-muted-foreground text-xs",children:De(a)})]})}),e.jsx(x,{children:$(a,"taDiscount")}),e.jsx(x,{children:$(a,"birthTaDiscount")}),e.jsx(x,{children:e.jsx(_e,{discount:a,onToggleActive:s})})]},a.id))})]})})}const D="all";function Ie(){const[t,r]=C.useState({storeId:D,promotion:D,status:D,expiry:D});return C.useEffect(()=>{r(o=>({...o,promotion:D}))},[t.storeId]),{filters:t,handlers:{handleStoreChange:o=>{r(c=>({...c,storeId:o}))},handlePromotionChange:o=>{r(c=>({...c,promotion:o}))},handleStatusChange:o=>{r(c=>({...c,status:o}))},handleExpiryChange:o=>{r(c=>({...c,expiry:o}))}}}}function we(t){return{id:t.id,created_at:t.createdAt,created_by:t.createdBy,updated_at:t.updatedAt,updated_by:t.updatedBy,deleted:t.deleted||!1,deleted_at:t.deletedAt||null,deleted_by:t.deletedBy||null,membership_type_id:t.membershipTypeId,membership_type_name:t.membershipTypeName,birth_ta_discount:t.birthTaDiscount,birth_ots_discount:t.birthOtsDiscount,birth_time_range_apply:t.birthTimeRangeApply,ta_discount:t.taDiscount,ots_discount:t.otsDiscount,is_all:t.isAll,is_type:t.isType,is_item:t.isItem,type_id:t.typeId,item_id:t.itemId,discount_type:t.discountType,from_date:t.fromDate,to_date:t.toDate,time_sale_hour_day:t.timeSaleHourDay,time_sale_date_week:t.timeSaleDateWeek,description:t.description,extra_data:t.extraData,active:t.active===1?0:1,revision:t.revision,sort:t.sort,promotion_uid:t.promotionUid,store_uid:t.storeUid,brand_uid:t.brandUid,company_uid:t.companyUid,discount_clone_id:t.discountCloneId,promotion:t.promotion}}function Pe(t,r){return t.map(s=>{const i=r.find(l=>l.id===s.storeUid);return{...s,storeName:(i==null?void 0:i.store_name)||"Không xác định"}})}const w="all";function Ue({filters:t}){const{currentBrandStores:r}=G(),{selectedBrand:s}=O(),{company:i}=B(),l=t.storeId===w,a=l?r.map(p=>p.id):[t.storeId],{data:o=[]}=ge({storeUid:l?void 0:t.storeId}),{data:c=[],isLoading:g}=ee({companyUid:i==null?void 0:i.id,brandUid:s==null?void 0:s.id,page:1,listStoreUid:a,status:t.expiry===w?void 0:t.expiry,active:t.status===w?void 0:parseInt(t.status)}),m=Pe(c,r);return{discounts:t.promotion===w?m:m.filter(p=>p.promotionId===t.promotion),promotions:o,isLoading:g,stores:r}}function Ee(){const[t,r]=C.useState(!1),s=K(),{selectedBrand:i}=O(),{company:l}=B(),{filters:a,handlers:o}=Ie(),{discounts:c,promotions:g,isLoading:m,stores:h}=Ue({filters:a}),j=te(),p=A=>{const I=we(A);j.mutate(I)},T=()=>{l!=null&&l.id&&(i!=null&&i.id)&&s({to:"/sale/discount/membership/detail"})},u=()=>r(!0),_=()=>r(!1);return e.jsxs(e.Fragment,{children:[e.jsx(se,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(re,{}),e.jsx(ie,{}),e.jsx(ae,{})]})}),e.jsx(ne,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx(ye,{selectedStoreId:a.storeId,onStoreChange:o.handleStoreChange,selectedPromotion:a.promotion,onPromotionChange:o.handlePromotionChange,selectedStatus:a.status,onStatusChange:o.handleStatusChange,selectedExpiry:a.expiry,onExpiryChange:o.handleExpiryChange,stores:h,promotions:g,onCopyModalOpen:u,onCreateDiscount:T}),e.jsx(Ae,{discounts:c,isLoading:m,onToggleActive:p})]}),e.jsx(Ne,{isOpen:t,onClose:_})]})})]})}const Dt=Ee;export{Dt as component};
