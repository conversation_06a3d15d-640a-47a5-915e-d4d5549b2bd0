import{b as te,l as ne,aA as re,j as e,B as f,r as y,a as de,c as Y,T as oe,o as W,p as G,q as U}from"./index-Bnt3OGV2.js";import{f as me,g as le,h as A,i as he,d as ue,j as xe,S as pe}from"./use-service-charge-form-C0oRrKP4.js";import{D as ge}from"./discount-toggle-button-ByRMDsJu.js";import{v as P}from"./date-range-picker-CVvofQC0.js";import{L as v}from"./form-wT1R35uI.js";import{X as fe,C as X}from"./calendar-CzR6WBaB.js";import{I as M}from"./input-CiKEYbig.js";import{C as S,S as je,a as ve,b as Ne,c as ye,d as Ce}from"./select-Czd7KcZQ.js";import{T as _e,a as be,c as J}from"./tabs-DqOllQAY.js";import{T as Se}from"./textarea-Da8YG9AB.js";import{C as _}from"./checkbox-BiVztVsP.js";import{u as z}from"./useQuery-DSrD7NAp.js";import{b as H}from"./pos-api-BwpRFGce.js";import{Q}from"./query-keys-3lmd-xp6.js";import{P as Te}from"./modal-B0J8RkN-.js";import{C as T,a as w,b as D}from"./collapsible-CfXqqCTe.js";import{C as k}from"./chevron-right-sZt3EK3r.js";import{P as Z,a as B,b as ee}from"./popover-C2mvzdeD.js";import{C as se}from"./calendar-BgsBunwq.js";import{f as I}from"./isSameMonth-C8JQo-AN.js";import{C as ae}from"./circle-help-vaGE5apo.js";import{S as we}from"./switch-B9wGuLU2.js";function De(){const{handleBack:n,handleSave:d}=me(),{isEditMode:r,isLoading:m,isFormValid:c}=le(),{updateFormData:o}=A(),{company:u}=te(),{selectedBrand:a}=ne(),j=re({strict:!1}).id,p=!!j,{data:t}=he({company_uid:u==null?void 0:u.id,brand_uid:a==null?void 0:a.id,id:j,enabled:p}),g=ue(),s=r&&t,i=m||g.isPending,h=async()=>{if(!s)return;const E=t.active===1?0:1,$={...t,active:E,is_update_same_service_charges:!1};try{await g.mutateAsync($),o({active:E})}catch(R){console.error("Failed to toggle service charge status:",R)}},x=()=>r?"Chi tiết phí dịch vụ":"Tạo phí dịch vụ",N=()=>m?r?"Đang cập nhật...":"Đang tạo...":r?"Cập nhật":"Lưu",F=m||!c,K=(t==null?void 0:t.active)===1;return e.jsxs("div",{className:"mb-8 flex items-center justify-between",children:[e.jsx(f,{variant:"ghost",size:"sm",onClick:n,className:"flex items-center",children:e.jsx(fe,{className:"h-4 w-4"})}),e.jsx("h1",{className:"text-3xl font-bold",children:x()}),e.jsxs("div",{className:"flex items-center gap-2",children:[s&&e.jsx(ge,{isActive:K,onToggle:h,disabled:i}),e.jsx(f,{type:"button",disabled:F,className:"min-w-[100px]",onClick:d,children:N()})]})]})}function ke({itemTypes:n,selectedItems:d,searchTerm:r,onItemToggle:m}){const[c,o]=y.useState(!1),[u,a]=y.useState(!1),l=(Array.isArray(n)?n:[]).filter(t=>{const g=t.source_name||t.item_type_name||t.name||"";return g.toLowerCase()!=="uncategory"&&g.toLowerCase().includes(r.toLowerCase())}),j=l.filter(t=>d.includes(t.source_id||t.item_type_id||t.id)),p=l.filter(t=>!d.includes(t.source_id||t.item_type_id||t.id));return e.jsxs("div",{className:"space-y-4",children:[e.jsxs(T,{open:!c,onOpenChange:t=>o(!t),children:[e.jsx(w,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",j.length,")"]}),c&&e.jsx(k,{className:"h-4 w-4"}),!c&&e.jsx(S,{className:"h-4 w-4"})]})}),e.jsxs(D,{className:"max-h-40 space-y-2 overflow-y-auto",children:[j.map(t=>e.jsxs("div",{className:`flex items-center space-x-2 p-2 ${t.active===0?"opacity-50":""}`,children:[e.jsx(_,{checked:!0,onCheckedChange:()=>m(t.source_id||t.item_type_id||t.id),disabled:t.active===0}),e.jsx("span",{className:"text-sm",children:t.source_name||t.item_type_name||t.name||"Không có tên"})]},t.id)),j.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn nhóm nào"})]})]}),e.jsxs(T,{open:!u,onOpenChange:t=>a(!t),children:[e.jsx(w,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",p.length,")"]}),u&&e.jsx(k,{className:"h-4 w-4"}),!u&&e.jsx(S,{className:"h-4 w-4"})]})}),e.jsxs(D,{className:"max-h-40 space-y-2 overflow-y-auto",children:[p.map(t=>e.jsxs("div",{className:`flex items-center space-x-2 p-2 ${t.active===0?"opacity-50":""}`,children:[e.jsx(_,{checked:!1,onCheckedChange:()=>m(t.source_id||t.item_type_id||t.id),disabled:t.active===0}),e.jsx("span",{className:"text-sm",children:t.source_name||t.item_type_name||t.name||"Không có tên"})]},t.id)),p.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có nhóm nào"})]})]})]})}function Ae({items:n,selectedItems:d,searchTerm:r,isLoading:m,onItemToggle:c}){const[o,u]=y.useState(!1),[a,l]=y.useState(!1),p=(Array.isArray(n)?n:[]).filter(s=>((s==null?void 0:s.area_name)||(s==null?void 0:s.item_name)||(s==null?void 0:s.name)||"").toLowerCase().includes(r.toLowerCase())).map(s=>({...s,name:s.area_name||s.item_name||s.name||"Không có tên"})),t=p.filter(s=>d.includes(s.area_id||s.item_id||s.id)),g=p.filter(s=>!d.includes(s.area_id||s.item_id||s.id));return m?e.jsx("div",{className:"py-4 text-center text-sm text-gray-500",children:"Đang tải..."}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs(T,{open:!o,onOpenChange:s=>u(!s),children:[e.jsx(w,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",t.length,")"]}),o&&e.jsx(k,{className:"h-4 w-4"}),!o&&e.jsx(S,{className:"h-4 w-4"})]})}),e.jsxs(D,{className:"max-h-40 space-y-2 overflow-y-auto",children:[t.map(s=>e.jsxs("div",{className:`flex items-center space-x-2 p-2 ${s.active===0?"opacity-50":""}`,children:[e.jsx(_,{checked:!0,onCheckedChange:()=>c(s.area_id||s.item_id||s.id),disabled:s.active===0}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:s.name||"Không có tên"})})]},s.id)),t.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn món ăn nào"})]})]}),e.jsxs(T,{open:!a,onOpenChange:s=>l(!s),children:[e.jsx(w,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",g.length,")"]}),a&&e.jsx(k,{className:"h-4 w-4"}),!a&&e.jsx(S,{className:"h-4 w-4"})]})}),e.jsxs(D,{className:"max-h-40 space-y-2 overflow-y-auto",children:[g.map(s=>e.jsxs("div",{className:`flex items-center space-x-2 p-2 ${s.active===0?"opacity-50":""}`,children:[e.jsx(_,{checked:!1,onCheckedChange:()=>c(s.area_id||s.item_id||s.id),disabled:s.active===0}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:s.name||"Không có tên"})})]},s.id)),g.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có món ăn nào"})]})]})]})}function Fe({packages:n,selectedItems:d,searchTerm:r,isLoading:m,onItemToggle:c}){const[o,u]=y.useState(!1),[a,l]=y.useState(!1),p=(Array.isArray(n)?n:[]).filter(s=>{const h=((s==null?void 0:s.table_name)||(s==null?void 0:s.package_name)||(s==null?void 0:s.name)||"").toLowerCase().includes(r.toLowerCase());return s.active!==0&&h}).map(s=>({...s,name:s.table_name||s.package_name||s.name||"Không có tên"})),t=p.filter(s=>d.includes(s.table_id||s.package_id||s.id)),g=p.filter(s=>!d.includes(s.table_id||s.package_id||s.id));return m?e.jsx("div",{className:"py-4 text-center text-sm text-gray-500",children:"Đang tải..."}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs(T,{open:!o,onOpenChange:s=>u(!s),children:[e.jsx(w,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",t.length,")"]}),o&&e.jsx(k,{className:"h-4 w-4"}),!o&&e.jsx(S,{className:"h-4 w-4"})]})}),e.jsxs(D,{className:"max-h-40 space-y-2 overflow-y-auto",children:[t.map(s=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(_,{checked:!0,onCheckedChange:()=>c(s.table_id||s.package_id||s.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:s.name||"Không có tên"})})]},s.id)),t.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn combo nào"})]})]}),e.jsxs(T,{open:!a,onOpenChange:s=>l(!s),children:[e.jsx(w,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",g.length,")"]}),a&&e.jsx(k,{className:"h-4 w-4"}),!a&&e.jsx(S,{className:"h-4 w-4"})]})}),e.jsxs(D,{className:"max-h-40 space-y-2 overflow-y-auto",children:[g.map(s=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(_,{checked:!1,onCheckedChange:()=>c(s.table_id||s.package_id||s.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:s.name||"Không có tên"})})]},s.id)),g.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có combo nào"})]})]})]})}function Ee({open:n,onOpenChange:d,storeUid:r,onSave:m,initialApplyToAll:c=!1,initialActiveFilter:o=null,initialSelectedItems:u=[]}){const{selectedBrand:a}=ne(),{company:l}=te(),[j,p]=y.useState(""),[t,g]=y.useState(!1),[s,i]=y.useState(null),[h,x]=y.useState([]),{data:N=[],isLoading:F}=z({queryKey:[Q.SOURCES,l==null?void 0:l.id,a==null?void 0:a.id,r],queryFn:async()=>!(l!=null&&l.id)||!(a!=null&&a.id)||!r?[]:(await H.get(`/mdata/v1/sources?skip_limit=true&company_uid=${l.id}&brand_uid=${a.id}&store_uid=${r}`)).data.data||[],enabled:n&&!!(l!=null&&l.id)&&!!(a!=null&&a.id)&&!!r}),{data:K=[],isLoading:E}=z({queryKey:[Q.AREAS,l==null?void 0:l.id,a==null?void 0:a.id,r],queryFn:async()=>!(l!=null&&l.id)||!(a!=null&&a.id)||!r?[]:(await H.get(`/pos/v1/area?skip_limit=true&company_uid=${l.id}&brand_uid=${a.id}&store_uid=${r}`)).data.data||[],enabled:n&&!!(l!=null&&l.id)&&!!(a!=null&&a.id)&&!!r}),{data:$=[],isLoading:R}=z({queryKey:[Q.TABLES,l==null?void 0:l.id,a==null?void 0:a.id,r],queryFn:async()=>!(l!=null&&l.id)||!(a!=null&&a.id)||!r?[]:(await H.get(`/pos/v1/table?skip_limit=true&company_uid=${l.id}&brand_uid=${a.id}&store_uid=${r}`)).data.data||[],enabled:n&&!!(l!=null&&l.id)&&!!(a!=null&&a.id)&&!!r}),O=F||E||R;y.useEffect(()=>{n&&(p(""),g(c),i(o||"groups"),x(u))},[n,c,o,u]);const q=C=>{i(b=>{const L=b===C?null:C;return L!==b&&x([]),L})},V=C=>{x(b=>b.includes(C)?b.filter(L=>L!==C):[...b,C])},ie=()=>{d(!1)},ce=()=>{t?m(["all"],!0,null):m(h,!1,s),d(!1)};return e.jsx(Te,{title:"Áp dụng điều kiện mở rộng",open:n,onOpenChange:d,onCancel:ie,onConfirm:ce,cancelText:"Hủy",confirmText:"Lưu",maxWidth:"sm:max-w-2xl",isLoading:O,confirmDisabled:h.length===0,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center gap-4",children:e.jsx(M,{placeholder:"Tìm kiếm",value:j,onChange:C=>p(C.target.value),className:"flex-1"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(v,{className:"text-sm font-medium",children:"Áp dụng cho"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(f,{type:"button",variant:s==="groups"?"default":"outline",size:"sm",onClick:()=>q("groups"),children:"Nguồn"}),e.jsx(f,{type:"button",variant:s==="items"?"default":"outline",size:"sm",onClick:()=>q("items"),children:"Khu vực"}),e.jsx(f,{type:"button",variant:s==="packages"?"default":"outline",size:"sm",onClick:()=>q("packages"),children:"Bàn"})]})]}),e.jsxs("div",{className:"space-y-2",children:[s==="groups"&&e.jsx(ke,{itemTypes:N,selectedItems:h,searchTerm:j,onItemToggle:V}),s==="items"&&e.jsx(Ae,{items:K,selectedItems:h,searchTerm:j,isLoading:O,onItemToggle:V}),s==="packages"&&e.jsx(Fe,{packages:$,selectedItems:h,searchTerm:j,isLoading:O,onItemToggle:V})]})]})})}const Le={groups:{flag:"is_source",field:"source_ids"},items:{flag:"is_area",field:"area_ids"},packages:{flag:"is_table",field:"table_ids"}};function Pe(){const{formData:n,updateFormData:d}=A(),{isEditMode:r}=le(),{currentBrandStores:m}=de(),[c,o]=y.useState(!1),u=()=>{o(!0)},a=i=>({...n.filterState,is_all:0,is_type:0,is_item:0,is_combo:0,type_id:"",item_id:"",combo_id:"",is_source:0,is_area:0,is_table:0,source_ids:[],area_ids:[],table_ids:[]}),l=(i,h,x)=>{const N=Le[h];N&&(i[N.flag]=1,i[N.field]=x)},j=(i,h,x)=>{const N=a();i.length>0&&x&&l(N,x,i),d({filterState:N})},p={groups:{flag:"is_source",field:"source_ids",label:"nguồn"},items:{flag:"is_area",field:"area_ids",label:"khu vực"},packages:{flag:"is_table",field:"table_ids",label:"bàn"}},t=()=>{var i;for(const[h,x]of Object.entries(p))if(((i=n.filterState)==null?void 0:i[x.flag])===1)return h;return null},g=()=>{var N;const i=t();if(!i)return[];const h=p[i],x=(N=n.filterState)==null?void 0:N[h.field];return Array.isArray(x)?x:typeof x=="string"?x?x.split(",").filter(Boolean):[]:[]},s=()=>{const i=g();if(i.length>0){const h=t(),x=h?p[h].label:"";return`${i.length} ${x}`}return"Thêm"};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Cấu hình"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(v,{className:"min-w-[200px] text-sm font-medium",children:["Áp dụng với hoá đơn từ ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx(M,{type:"number",min:"0",value:n.fromAmount||"",onChange:i=>d({fromAmount:Number(i.target.value)}),placeholder:"0",className:"flex-1"}),e.jsxs(v,{className:"text-sm font-medium",children:["Đến ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(M,{type:"number",min:"0",value:n.toAmount||"",onChange:i=>d({toAmount:Number(i.target.value)}),placeholder:"0",className:"flex-1"})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(v,{className:"min-w-[200px] text-sm font-medium",children:[n.chargeType==="PERCENT"?"Phần trăm":"Số tiền"," ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"flex flex-1 gap-2",children:[e.jsx(M,{type:"number",min:"0",max:n.chargeType==="PERCENT"?"100":void 0,value:n.chargeValue||"",onChange:i=>{const h=Number(i.target.value);if(n.chargeType==="PERCENT"){const x=h>100?100:h;d({chargeValue:x})}else d({chargeValue:h})},placeholder:"0",className:"flex-1"}),e.jsx(_e,{value:n.chargeType,onValueChange:i=>d({chargeType:i}),className:"w-auto",children:e.jsxs(be,{className:"grid w-fit grid-cols-2",children:[e.jsx(J,{value:"PERCENT",children:"%"}),e.jsx(J,{value:"AMOUNT",children:"đ"})]})})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(v,{className:"min-w-[200px] text-sm font-medium",children:["Cửa hàng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(je,{value:n.storeUid,onValueChange:i=>{d({storeUid:i})},disabled:r,children:[e.jsx(ve,{className:"flex-1",children:e.jsx(Ne,{placeholder:"Chọn cửa hàng"})}),e.jsx(ye,{children:m.map(i=>e.jsx(Ce,{value:i.id,children:i.store_name},i.id))})]})]}),e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx(v,{className:"min-w-[200px] text-sm font-medium",children:"Mô tả"}),e.jsx(Se,{placeholder:"Nhập mô tả...",value:n.description,onChange:i=>d({description:i.target.value}),className:"flex-1",rows:3})]}),n.isServiceChargeVoucher&&!r&&e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(v,{className:"min-w-[200px] text-sm font-medium",children:"Phiếu phí dịch vụ yêu cầu nhập số lượng"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(_,{id:"requiresQuantityInput",checked:n.requiresQuantityInput,onCheckedChange:i=>d({requiresQuantityInput:i})}),e.jsx(v,{htmlFor:"requiresQuantityInput",className:"cursor-pointer text-sm font-normal",children:"Yêu cầu nhập số lượng"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Điều kiện áp dụng khác"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Nếu các điều kiện dưới đây được cấu hình, phí dịch vụ được áp dụng khi hoá đơn có giá trị trong khoảng tiền đã cấu hình và thoả mãn 1 trong 3 điều kiện"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(v,{className:"min-w-[200px] text-sm font-medium",children:["Điều kiện mở rộng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(f,{type:"button",variant:"outline",onClick:u,disabled:!n.storeUid,className:"flex-1 justify-start",children:s()})]})]}),e.jsx(Ee,{open:c,onOpenChange:o,onSave:j,storeUid:n.storeUid,initialApplyToAll:!1,initialActiveFilter:t(),initialSelectedItems:g()})]})}function Ie(){const{formData:n,updateFormData:d}=A(),r=new Date;r.setHours(0,0,0,0);const m=n.startDate?new Date(n.startDate):void 0,c=n.endDate?new Date(n.endDate):void 0,o=a=>{if(a){const l=I(a,"yyyy-MM-dd");d({startDate:l})}},u=a=>{if(a){const l=I(a,"yyyy-MM-dd");d({endDate:l})}};return e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Ngày áp dụng"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex flex-1 items-center gap-4",children:[e.jsx(v,{className:"min-w-[120px] text-sm font-medium",children:"Ngày bắt đầu"}),e.jsxs(Z,{children:[e.jsx(B,{asChild:!0,children:e.jsxs(f,{variant:"outline",className:Y("flex-1 justify-start text-left font-normal",!m&&"text-muted-foreground"),children:[e.jsx(se,{className:"mr-2 h-4 w-4"}),m?I(m,"dd/MM/yyyy",{locale:P}):"Chọn ngày bắt đầu"]})}),e.jsx(ee,{className:"w-auto p-0",align:"start",children:e.jsx(X,{mode:"single",selected:m,onSelect:o,disabled:a=>a>r,initialFocus:!0,locale:P})})]})]}),e.jsxs("div",{className:"flex flex-1 items-center gap-4",children:[e.jsx(v,{className:"min-w-[120px] text-sm font-medium",children:"Ngày kết thúc"}),e.jsxs(Z,{children:[e.jsx(B,{asChild:!0,children:e.jsxs(f,{variant:"outline",className:Y("flex-1 justify-start text-left font-normal",!c&&"text-muted-foreground"),children:[e.jsx(se,{className:"mr-2 h-4 w-4"}),c?I(c,"dd/MM/yyyy",{locale:P}):"Chọn ngày kết thúc"]})}),e.jsx(ee,{className:"w-auto p-0",align:"start",children:e.jsx(X,{mode:"single",selected:c,onSelect:u,disabled:a=>a<r,initialFocus:!0,locale:P})})]})]})]})]})}const Me=[{label:"T2",value:"0"},{label:"T3",value:"1"},{label:"T4",value:"2"},{label:"T5",value:"3"},{label:"T6",value:"4"},{label:"T7",value:"5"},{label:"CN",value:"6"}],Ke=[{value:"0"},{value:"1"},{value:"2"},{value:"3"},{value:"4"},{value:"5"},{value:"6"},{value:"7"},{value:"8"},{value:"9"},{value:"10"},{value:"11"},{value:"12"},{value:"13"},{value:"14"},{value:"15"},{value:"16"},{value:"17"},{value:"18"},{value:"19"},{value:"20"},{value:"21"},{value:"22"},{value:"23"}];function $e(){const{formData:n,updateFormData:d}=A(),r=c=>{const o=n.marketingDays||[],u=o.includes(c)?o.filter(a=>a!==c):[...o,c];d({marketingDays:u})},m=c=>{const o=n.marketingHours||[],u=o.includes(c)?o.filter(a=>a!==c):[...o,c];d({marketingHours:u})};return e.jsx(oe,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Khung thời gian áp dụng"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(v,{className:"text-sm font-medium text-gray-500",children:"Chọn ngày"}),e.jsxs(W,{children:[e.jsx(G,{asChild:!0,children:e.jsx(ae,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(U,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu để trống mặc định sẽ áp dụng tất cả các ngày trong tuần"})})]})]}),e.jsx("div",{className:"flex gap-2",children:Me.map(c=>{var o;return e.jsx(f,{type:"button",variant:(o=n.marketingDays)!=null&&o.includes(c.value)?"default":"outline",size:"sm",onClick:()=>r(c.value),className:"flex-1",children:c.label},c.value)})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(v,{className:"text-sm font-medium text-gray-500",children:"Chọn giờ"}),e.jsxs(W,{children:[e.jsx(G,{asChild:!0,children:e.jsx(ae,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(U,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu để trống mặc định sẽ áp dụng tất cả các giờ trong ngày"})})]})]}),e.jsx("div",{className:"grid grid-cols-6 gap-2",children:Ke.map(c=>{var o;return e.jsxs(f,{type:"button",variant:(o=n.marketingHours)!=null&&o.includes(c.value)?"default":"outline",size:"sm",onClick:()=>m(c.value),className:"text-xs",children:[c.value,":00"]},c.value)})})]})]})})}function Re(){const{formData:n,updateFormData:d}=A(),r=m=>{d({isUpdateSameServiceCharges:m})};return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(v,{htmlFor:"sync-service-charges",className:"text-sm font-medium",children:"Đồng bộ với phí dịch vụ tương tự của các cửa hàng khác"}),e.jsx(we,{id:"sync-service-charges",checked:n.isUpdateSameServiceCharges,onCheckedChange:r})]})}function rs({serviceChargeId:n,storeUid:d}={}){const r=xe({serviceChargeId:n,initialStoreUid:d});return e.jsx(pe,{value:r,children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(De,{}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx(Re,{}),e.jsx(Pe,{}),e.jsx(Ie,{}),e.jsx($e,{})]})})})]})})}export{rs as S};
