import{r as d,j as e,B as j,h as M,aA as R}from"./index-Bnt3OGV2.js";import{d as A,e as T,f as G}from"./use-order-sources-Yciryonx.js";import{a as B}from"./use-sources-ByVM6ea-.js";import"./date-range-picker-CVvofQC0.js";import{L as _}from"./form-wT1R35uI.js";import{I as b}from"./input-CiKEYbig.js";import{C as D}from"./checkbox-BiVztVsP.js";import{C as k,a as L,b as O}from"./collapsible-CfXqqCTe.js";import{D as $,a as K,e as U}from"./dialog-hQ-PVOWr.js";import{C as I}from"./chevron-right-sZt3EK3r.js";import{C as E}from"./select-Czd7KcZQ.js";import{X as P}from"./calendar-CzR6WBaB.js";function Q({value:r,onValueChange:a,onSelectItem:l,placeholder:S="Nhập tên nguồn đơn hàng",className:g,disabled:C=!1}){const[i,c]=d.useState(!1),h=d.useRef(null),t=d.useRef(null),{data:m=[],isLoading:x}=B(),u=m.filter(s=>s.source_name.toLowerCase().includes((r||"").toLowerCase())),f=s=>{const o=s.target.value;a==null||a(o),c(!0)},p=()=>{c(!0)},N=s=>{s.key==="Escape"&&c(!1)},v=s=>{var o;a==null||a(s.source_name),l==null||l(s),c(!1),(o=t.current)==null||o.focus()};return d.useEffect(()=>{const s=o=>{h.current&&!h.current.contains(o.target)&&c(!1)};return document.addEventListener("mousedown",s),()=>{document.removeEventListener("mousedown",s)}},[]),e.jsxs("div",{className:"relative",ref:h,children:[e.jsx(b,{ref:t,value:r||"",onChange:f,onFocus:p,onKeyDown:N,placeholder:S,className:g,disabled:C}),i&&e.jsxs("div",{className:"absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-md border border-gray-200 bg-white shadow-lg",children:[x&&e.jsx("div",{className:"py-6 text-center text-sm text-gray-500",children:"Đang tải..."}),!x&&u.length===0&&e.jsx("div",{className:"py-6 text-center text-sm text-gray-500",children:r&&r.trim()?`Nhấn Enter để sử dụng "${r.trim()}"`:"Không tìm thấy nguồn nào."}),!x&&u.length>0&&e.jsx("ul",{className:"py-1",children:u.map((s,o)=>e.jsx("li",{className:"cursor-pointer px-3 py-2 text-sm text-gray-900 hover:bg-gray-100",onClick:()=>v(s),children:s.source_name},`${s.source_id}-${o}`))})]})]})}function z({open:r,onOpenChange:a,selectedStoreIds:l,onStoreSelectionChange:S}){const[g,C]=d.useState(""),[i,c]=d.useState(!1),[h,t]=d.useState(!1),m=d.useMemo(()=>{try{const s=localStorage.getItem("pos_stores_data");if(s){const o=JSON.parse(s);return Array.isArray(o)?o.filter(y=>y.active===1):[]}return[]}catch{return[]}},[]),x=d.useMemo(()=>m.filter(s=>s.store_name.toLowerCase().includes(g.toLowerCase())),[m,g]),u=x.filter(s=>l.includes(s.id)),f=x.filter(s=>!l.includes(s.id)),p=s=>{const o=l.includes(s)?l.filter(y=>y!==s):[...l,s];S(o)},N=()=>{a(!1)},v=()=>{a(!1)};return e.jsx($,{open:r,onOpenChange:a,children:e.jsxs(K,{className:"max-w-md",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx(b,{placeholder:"Tìm kiếm nhà hàng",value:g,onChange:s=>C(s.target.value),className:"w-full"})}),e.jsxs(k,{open:!i,onOpenChange:c,children:[e.jsx(L,{asChild:!0,children:e.jsxs(j,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",u.length,")"]}),i?e.jsx(I,{className:"h-4 w-4"}):e.jsx(E,{className:"h-4 w-4"})]})}),e.jsxs(O,{className:"space-y-2",children:[u.map(s=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(D,{checked:!0,onCheckedChange:()=>p(s.id)}),e.jsx("span",{className:"text-sm",children:s.store_name})]},s.id)),u.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn cửa hàng nào"})]})]}),e.jsxs(k,{open:!h,onOpenChange:t,children:[e.jsx(L,{asChild:!0,children:e.jsxs(j,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",f.length,")"]}),h?e.jsx(I,{className:"h-4 w-4"}):e.jsx(E,{className:"h-4 w-4"})]})}),e.jsxs(O,{className:"space-y-2",children:[f.map(s=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(D,{checked:!1,onCheckedChange:()=>p(s.id)}),e.jsx("span",{className:"text-sm",children:s.store_name})]},s.id)),f.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có cửa hàng nào"})]})]})]}),e.jsxs(U,{children:[e.jsx(j,{variant:"outline",onClick:v,children:"Hủy"}),e.jsx(j,{onClick:N,children:"Lưu"})]})]})})}const H=()=>{const r="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";let a="";for(let l=0;l<4;l++)a+=r.charAt(Math.floor(Math.random()*r.length));return`SOURCE-${a}`};function J({sourceId:r}){const a=M(),{createOrderSource:l,isCreating:S}=A(),{updateOrderSource:g,isUpdating:C}=T(),i=!!r,{data:c,isLoading:h}=G(r),[t,m]=d.useState({name:"",code:"",autoGenerateCode:!0,selectedStores:[],selectedSourceId:""}),[x,u]=d.useState(!1);d.useEffect(()=>{if(c&&i){let n=[];Array.isArray(c.stores)&&(n=c.stores.map(w=>w.id)),m({name:c.source_name||"",code:c.source_id||"",autoGenerateCode:!c.source_id,selectedStores:n,selectedSourceId:c.source_id||""})}},[c,i]);const f=()=>{a({to:"/setting/source"})},p=t.name.trim()!==""&&t.selectedStores.length>0,N=S||C,v=async()=>{if(!p)return;let n;t.selectedSourceId?n=t.selectedSourceId:t.autoGenerateCode?n=H():t.code.trim()?n=t.code.trim():n=void 0;const w={source_name:t.name,source_id:n,stores:t.selectedStores,description:null,active:1,sort:1e3,extra_data:{},source_type:[],is_fabi:1,partner_config:0};i?g(w,{onSuccess:()=>{a({to:"/setting/source"})}}):l(w,{onSuccess:()=>{a({to:"/setting/source"})}})},s=()=>{u(!0)},o=n=>{m({...t,selectedStores:n})},y=n=>{m({...t,name:n,selectedSourceId:""})},F=n=>{m({...t,name:n.source_name,code:n.source_id,selectedSourceId:n.source_id})};return i&&h?e.jsx("div",{className:"flex min-h-screen items-center justify-center bg-gray-50",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"}),e.jsx("p",{className:"mt-2 text-gray-600",children:"Đang tải dữ liệu..."})]})}):e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"border-b bg-white px-6 py-4",children:e.jsxs("div",{className:"mx-auto flex max-w-4xl items-center justify-between",children:[e.jsx(j,{variant:"ghost",size:"sm",onClick:f,className:"p-2",children:e.jsx(P,{className:"h-4 w-4"})}),e.jsx("div",{className:"flex-1 text-center",children:e.jsx("h1",{className:"text-xl font-semibold text-gray-900",children:i?"Chỉnh sửa nguồn đơn hàng":"Tạo nguồn mới"})}),e.jsx("div",{children:e.jsx(j,{onClick:v,disabled:!p||N,children:N?"Đang lưu...":"Lưu"})})]})}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Thông tin chi tiết"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(_,{htmlFor:"source-name",className:"min-w-[200px] text-sm font-medium",children:"Tên nguồn *"}),i?e.jsx(b,{value:t.name,disabled:!0,className:"flex-1",placeholder:"Tên nguồn đơn hàng"}):e.jsx(Q,{value:t.name,onValueChange:y,onSelectItem:F,placeholder:"Nhập tên nguồn đơn hàng",className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(_,{className:"min-w-[200px] text-sm font-medium",children:"Cửa hàng áp dụng *"}),e.jsx(j,{type:"button",variant:"outline",onClick:s,className:"flex-1 justify-start",children:t.selectedStores.length>0?`${t.selectedStores.length} điểm`:"0 điểm"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(_,{htmlFor:"source-code",className:"min-w-[200px] text-sm font-medium",children:"Mã nguồn"}),e.jsxs("div",{className:"flex flex-1 items-center gap-3",children:[e.jsx(b,{id:"source-code",value:t.code,onChange:n=>m({...t,code:n.target.value}),placeholder:"Nếu để trống, hệ thống sẽ tự động tạo một mã nguồn",disabled:i||t.autoGenerateCode,className:"flex-1"}),!i&&e.jsx(D,{id:"auto-generate-code",checked:t.autoGenerateCode,onCheckedChange:n=>m({...t,autoGenerateCode:n,code:n?"":t.code})})]})]})]})]})}),e.jsx(z,{open:x,onOpenChange:u,selectedStoreIds:t.selectedStores,onStoreSelectionChange:o})]})}function ce(){let r;try{const a=R({from:"/_authenticated/setting/source/detail/$sourceId"});r=a==null?void 0:a.sourceId}catch{r=void 0}return e.jsx(J,{sourceId:r})}export{ce as O};
