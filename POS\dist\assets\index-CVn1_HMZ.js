import{r as y,j as e,c as H,B as O,h as xe,a4 as I,u as Q,R as je,a3 as J}from"./index-C21OP4ex.js";import{u as Se,a as we,C as _e,b as Te,c as Ne,D as Ce,d as ke,e as Ae,P as De}from"./core.esm-Dic0nloh.js";import{c as Ie,u as pe,t as Me,e as Ee}from"./use-tables-DC_pzXqw.js";import{T as Z}from"./trash-2-C_5rhUMO.js";import{a as he,b as W}from"./pos-api-D5WM5mnz.js";import"./date-range-picker-B1pgj5D_.js";import"./form-usWdQ_Nt.js";import{D as Le,a as Oe,b as Pe,c as Y}from"./dropdown-menu-DINyPmco.js";import{C as Fe,S as Ue,a as Re,b as Be,c as $e,d as ze}from"./select-B8Pw9rS-.js";import{U as ee}from"./upload-CZfvv05H.js";import{S as ge}from"./settings-B2dEoYrB.js";import{A as Ke}from"./arrow-up-down-DJLsa2K1.js";import{c as Ve}from"./createLucideIcon-CL0CQOA1.js";import{L as qe}from"./loader-circle-BImne5bD.js";import{u as te}from"./use-stores-BQdEFBhG.js";import{F as He}from"./filter-dropdown-_JnDjFx2.js";import"./vietqr-api-ruJT0-tj.js";import{b as We,u as ae,c as G}from"./use-areas-RQjhzRCK.js";import{a as Qe,C as Ye}from"./chevron-right-BAjIoZMb.js";import{P as Xe}from"./plus-C1IEs-Ov.js";import{D as z,a as K,b as V,c as q}from"./dialog-DXjwjGKV.js";import{G as Ge}from"./grip-vertical-Bo_DpjTT.js";import"./crm-api-BUMUQ8t4.js";import"./user-CgNoQQSj.js";import{D as fe}from"./download-dYw-Dq4T.js";import{E as se}from"./exceljs.min-BFFGgdR1.js";import{u as ne}from"./useMutation-Bh5DVQPI.js";import{s as be}from"./sources-api-Cb3TooI2.js";import{C as Je}from"./index-DemC1Nlr.js";import{T as Ze,a as et,b as ue,c as B,d as tt,e as $}from"./table-BIu4Pah2.js";import{C as at}from"./check-DcHT8QEO.js";import{P as st}from"./modal-CFi1vCFt.js";import{C as nt}from"./combobox-DyrNt90A.js";import{T as rt,k as lt}from"./react-icons.esm-DB-kGUq7.js";import{S as ot}from"./search-DHRhj6_i.js";import{u as it}from"./useQuery-BNGphiae.js";import{Q as re}from"./query-keys-3lmd-xp6.js";import"./calendar-BiBi2kQF.js";import"./index-Ct3V_iCU.js";import"./isSameMonth-C8JQo-AN.js";import"./popover-BtedB187.js";import"./index-Bh-UeytL.js";import"./index-UJ-79IIJ.js";import"./index-DuT2Ibxp.js";import"./stores-api-BIve2jSO.js";import"./command-BnLmWlRk.js";import"./chevrons-up-down-ChTQDbCM.js";import"./images-api-V0IeMkhY.js";import"./utils-km2FGkQ4.js";import"./sources-CfiQ7039.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ct=[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]],dt=Ve("palette",ct),me=({table:t,isSelected:a=!1,isDragging:n=!1,isOverlay:s=!1,onClick:i,storeUid:l})=>{var S,A,C,j,M,L,w;const[r,c]=y.useState(!1),{deleteTable:d,isDeleting:h}=Ie(l||""),{attributes:o,listeners:m,setNodeRef:p,transform:v,isDragging:f}=Se({id:t.id,data:{table:t}}),{setNodeRef:g,isOver:x}=we({id:t.id}),b=k=>{p(k),g(k)},_=s?{width:((S=t.size)==null?void 0:S.width)||180,height:150}:{transform:_e.Translate.toString(v),left:((A=t.position)==null?void 0:A.x)||t.position_x||0,top:((C=t.position)==null?void 0:C.y)||t.position_y||0,width:((j=t.size)==null?void 0:j.width)||180,height:150},N=n||f,u=a?"bg-blue-500":x?"bg-yellow-400":"bg-[#b9d9c2]",T=k=>{if(!k)return"";const D=k.split("-");return D.length>1?D[D.length-1]:k},E=async k=>{if(k.stopPropagation(),!!l)try{await d(t.id)}catch(D){console.error("Error deleting table:",D)}};return N&&!s?null:e.jsxs("div",{ref:s?void 0:b,style:_,...s?{}:m,...s?{}:o,onClick:s?void 0:i,onMouseEnter:()=>!s&&c(!0),onMouseLeave:()=>!s&&c(!1),className:H("relative cursor-pointer rounded-[20px] transition-all duration-200 select-none","bg-[#dbf2e2] shadow-lg hover:shadow-xl",{absolute:!s,"z-[1000]":(n||f)&&!s,"z-[1]":!(n||f)&&!s,"bg-blue-200/70":a,"bg-yellow-200/70":x&&!a,"scale-105":a,"scale-102":x&&!a,"scale-110 rotate-3 shadow-2xl":s}),children:[e.jsx("div",{className:H("pointer-events-none absolute -top-[18px] left-1/2 h-[18px] w-9 -translate-x-1/2","rounded-t-[18px]",u)}),e.jsx("div",{className:H("pointer-events-none absolute top-1/2 -right-[18px] h-9 w-[18px] -translate-y-1/2","rounded-r-[18px]",u)}),e.jsx("div",{className:H("pointer-events-none absolute -bottom-[18px] left-1/2 h-[18px] w-9 -translate-x-1/2","rounded-b-[18px]",u)}),e.jsx("div",{className:H("pointer-events-none absolute top-1/2 -left-[18px] h-9 w-[18px] -translate-y-1/2","rounded-l-[18px]",u)}),e.jsx("div",{className:"pointer-events-none absolute top-3 left-3 text-sm font-bold text-gray-800",children:t.table_name}),e.jsx("div",{className:"pointer-events-none absolute top-3 right-3 text-sm font-medium text-gray-700",children:T("table_id"in t?t.table_id:t.id)}),e.jsx("div",{className:"pointer-events-none absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",children:e.jsxs("div",{className:"rounded-lg bg-[#b9d9c2] px-3 py-1.5 text-xs font-medium text-gray-800",children:[((M=t.area)==null?void 0:M.area_name)||"N/A"," - ",T((L=t.area)==null?void 0:L.area_id)]})}),((w=t.extra_data)==null?void 0:w.order_list)&&t.extra_data.order_list.length>0&&e.jsxs("div",{className:"pointer-events-none absolute bottom-3 left-1/2 -translate-x-1/2 text-xs font-medium text-blue-600",children:[t.extra_data.order_list.length," món đặt trước"]}),r&&!s&&e.jsx("button",{onClick:E,disabled:h,className:"absolute right-2 bottom-2 rounded-full bg-red-500 p-1.5 text-white shadow-lg transition-all hover:bg-red-600 disabled:opacity-50",title:"Xóa bàn",children:e.jsx(Z,{className:"h-3 w-3"})})]})},ht=({tables:t,onTableSelect:a,selectedTableId:n,onTablePositionUpdate:s,storeUid:i})=>{const[l,r]=y.useState(null),c=Te(Ne(De,{activationConstraint:{distance:5}})),d=y.useCallback(m=>{const{active:p}=m,v=t.find(f=>f.id===p.id);v&&r(v)},[t]),h=y.useCallback(m=>{var x,b,_,N,u;const{active:p,over:v,delta:f}=m,g=t.find(T=>T.id===p.id);if(g&&(f.x!==0||f.y!==0)&&s){const T=((x=g.position)==null?void 0:x.x)||g.position_x||0,E=((b=g.position)==null?void 0:b.y)||g.position_y||0,S=v?t.find(A=>A.id===v.id):null;if(S&&g.id!==S.id){const A=((_=S.position)==null?void 0:_.x)||S.position_x||0,C=((N=S.position)==null?void 0:N.y)||S.position_y||0;s(g.id,{x:A,y:C}),s(S.id,{x:T,y:E})}else{const A=T+f.x,C=E+f.y,j=((u=g.size)==null?void 0:u.width)||180,M=150,L=18,w=1200,k=600,D=Math.max(L,Math.min(A,w-j-L)),U=Math.max(L,Math.min(C,k-M-L));s(g.id,{x:D,y:U})}}r(null)},[t,s]),o=y.useCallback(m=>{a==null||a(m)},[a]);return e.jsx("div",{className:"relative h-[600px] w-full overflow-auto rounded-lg border bg-gray-50",children:e.jsxs(Ce,{sensors:c,collisionDetection:ke,onDragStart:d,onDragEnd:h,children:[e.jsx("div",{className:"relative h-full w-full min-w-[1200px]",children:t.map(m=>e.jsx(me,{table:m,isSelected:n===m.id,onClick:()=>o(m),storeUid:i},m.id))}),e.jsx(Ae,{adjustScale:!1,dropAnimation:null,children:l&&e.jsx(me,{table:l,isSelected:!1,isDragging:!1,isOverlay:!0})})]})})},ut=({onCreateNew:t,onSaveLayout:a,isUpdating:n=!1,onImportTables:s,onEditTableInfo:i,onSortTables:l,onCustomizeTable:r})=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(Le,{children:[e.jsx(Oe,{asChild:!0,children:e.jsxs(O,{variant:"outline",size:"sm",children:["Tiện ích",e.jsx(Fe,{className:"ml-2 h-4 w-4"})]})}),e.jsxs(Pe,{align:"end",className:"w-56",children:[e.jsxs(Y,{onClick:s,children:[e.jsx(ee,{className:"mr-2 h-4 w-4"}),"Thêm bàn từ file"]}),e.jsxs(Y,{onClick:i,children:[e.jsx(ge,{className:"mr-2 h-4 w-4"}),"Sửa thông tin bàn"]}),e.jsxs(Y,{onClick:l,children:[e.jsx(Ke,{className:"mr-2 h-4 w-4"}),"Sắp xếp bàn"]}),e.jsxs(Y,{onClick:r,children:[e.jsx(dt,{className:"mr-2 h-4 w-4"}),"Cấu hình bàn"]})]})]}),e.jsx(O,{onClick:t,className:"bg-blue-600 hover:bg-blue-700",size:"sm",children:"Tạo bàn mới"}),e.jsxs(O,{onClick:a,className:"bg-blue-600 hover:bg-blue-700",size:"sm",disabled:n,children:[n&&e.jsx(qe,{className:"mr-2 h-4 w-4 animate-spin"}),"Lưu"]})]}),le=({value:t,onValueChange:a,placeholder:n="Chọn cửa hàng",className:s="w-48"})=>{const{data:i,isLoading:l}=te(),r=(i==null?void 0:i.map(c=>({value:c.id,label:c.name})))||[];return e.jsx(He,{value:t,onValueChange:a,options:r,isLoading:l,placeholder:n,className:s,showAllOption:!1,loadingText:"Đang tải...",emptyText:"Không có cửa hàng có khu vực"})},mt=({selectedStoreId:t="all",onStoreChange:a,onCreateNew:n,onSaveLayout:s,isUpdating:i=!1,onImportTables:l,onEditTableInfo:r,onSortTables:c,onCustomizeTable:d})=>e.jsxs("div",{className:"flex items-center justify-between border-b bg-white p-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("span",{className:"text-sm font-bold text-gray-700",children:"Danh sách bàn"}),e.jsx(le,{value:t,onValueChange:a||(()=>{})})]}),e.jsx(ut,{onCreateNew:n,onSaveLayout:s,isUpdating:i,onImportTables:l,onEditTableInfo:r,onSortTables:c,onCustomizeTable:d})]}),xt=({areas:t,selectedAreaId:a,onAreaChange:n,onCreateNew:s,storeUid:i})=>{const l=xe(),r=y.useRef(null),[c,d]=y.useState(!1),[h,o]=y.useState(!1),[m,p]=y.useState(null),{deleteAreas:v,isDeleting:f}=We(),g=()=>{const u=r.current;u&&(d(u.scrollLeft>0),o(u.scrollLeft<u.scrollWidth-u.clientWidth))};y.useEffect(()=>{g();const u=r.current;if(u)return u.addEventListener("scroll",g),()=>u.removeEventListener("scroll",g)},[t]);const x=()=>{const u=r.current;u&&u.scrollBy({left:-200,behavior:"smooth"})},b=()=>{const u=r.current;u&&u.scrollBy({left:200,behavior:"smooth"})},_=(u,T)=>{T.stopPropagation(),i&&l({to:`/setting/area/detail/${u}`,search:{store_uid:i}})},N=(u,T)=>{T.stopPropagation(),i&&v({areaIds:[u],storeUid:i})};return e.jsxs("div",{className:"flex items-center gap-2 border-b bg-white px-4 py-3",children:[e.jsx("div",{className:"mr-4 flex items-center gap-2",children:e.jsx("span",{className:"text-sm font-medium whitespace-nowrap text-gray-700",children:"Khu vực"})}),e.jsx(O,{variant:"ghost",size:"sm",onClick:x,disabled:!c,className:"h-8 w-8 flex-shrink-0 p-1",children:e.jsx(Qe,{className:"h-4 w-4"})}),e.jsx("div",{ref:r,className:"flex-1 overflow-x-auto",style:{scrollbarWidth:"none",msOverflowStyle:"none"},children:e.jsx("div",{className:"flex min-w-max gap-1",children:t.map(u=>e.jsxs("div",{className:"relative",onMouseEnter:()=>p(u.id),onMouseLeave:()=>p(null),children:[e.jsx("button",{onClick:()=>n(u.id),className:`rounded-md px-4 py-2 text-sm font-medium whitespace-nowrap transition-colors ${a===u.id?"border border-blue-200 bg-blue-100 text-blue-700":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"} ${m===u.id?"pr-16":""}`,children:u.area_name}),m===u.id&&e.jsxs("div",{className:"absolute top-1/2 right-2 flex -translate-y-1/2 items-center gap-1",children:[e.jsx("button",{onClick:T=>_(u.id,T),className:"rounded p-1 transition-colors hover:bg-gray-200",title:"Cài đặt",children:e.jsx(ge,{className:"h-3 w-3 text-gray-500"})}),e.jsx("button",{onClick:T=>N(u.id,T),disabled:f,className:"rounded p-1 transition-colors hover:bg-red-100",title:"Xóa khu vực",children:e.jsx(Z,{className:"h-3 w-3 text-red-500"})})]})]},u.id))})}),e.jsx(O,{variant:"ghost",size:"sm",onClick:b,disabled:!h,className:"h-8 w-8 flex-shrink-0 p-1",children:e.jsx(Ye,{className:"h-4 w-4"})}),e.jsxs(O,{variant:"outline",size:"sm",onClick:s,className:"flex flex-shrink-0 items-center gap-1 border-blue-200 text-blue-600 hover:bg-blue-50",children:[e.jsx(Xe,{className:"h-4 w-4"}),"Tạo khu vực mới"]})]})},pt=({area:t,index:a,onDragStart:n,onDragOver:s,onDrop:i,isDragging:l,isSelected:r})=>e.jsxs("div",{draggable:!0,onDragStart:c=>n(c,a),onDragOver:s,onDrop:c=>i(c,a),className:`flex min-w-0 items-center gap-3 rounded-lg border p-3 transition-all duration-200 ${l?"opacity-50 shadow-lg":r?"cursor-move border-blue-500 bg-blue-50 shadow-md hover:shadow-lg":"cursor-move border-gray-200 bg-white hover:bg-gray-50 hover:shadow-md"}`,children:[e.jsx(Ge,{className:"h-5 w-5 flex-shrink-0 text-gray-400"}),e.jsx("div",{className:"min-w-0 flex-1",children:e.jsx("div",{className:"truncate font-medium text-gray-900",title:t.area_name,children:t.area_name})})]}),gt=({areas:t,isLoading:a,selectedAreaId:n,draggedIndex:s,onAreaClick:i,onDragStart:l,onDragOver:r,onDrop:c})=>e.jsxs("div",{className:"flex h-full w-1/3 min-w-0 flex-col",children:[e.jsx("h3",{className:"mb-2 flex-shrink-0 truncate font-medium text-gray-900",children:"Danh sách khu vực"}),e.jsx("div",{className:"min-h-0 flex-1 space-y-2 overflow-x-hidden overflow-y-auto pr-2",children:a?e.jsx("div",{className:"flex h-full items-center justify-center text-gray-500",children:"Đang tải khu vực..."}):t.length===0?e.jsx("div",{className:"flex h-full items-center justify-center text-gray-500",children:"Không có khu vực nào"}):t.map((d,h)=>e.jsx("div",{onClick:()=>i(d.id),children:e.jsx(pt,{area:d,index:h,onDragStart:l,onDragOver:r,onDrop:c,isDragging:s===h,isSelected:n===d.id})},d.id))})]}),ft=({table:t,index:a,onDragStart:n,onDragOver:s,onDrop:i,isDragging:l})=>e.jsx("div",{draggable:!0,onDragStart:r=>n(r,a),onDragOver:s,onDrop:r=>i(r,a),className:`min-w-0 p-2 transition-all duration-200 ${l?"scale-105 opacity-50":"cursor-move"}`,children:e.jsxs("div",{className:`relative mx-auto h-16 w-20 rounded-[12px] bg-gray-300 shadow-md transition-all duration-200 ${l?"shadow-xl":"hover:shadow-lg"}`,children:[e.jsx("div",{className:"pointer-events-none absolute -top-[8px] left-1/2 h-[8px] w-6 -translate-x-1/2 rounded-t-[8px] bg-gray-400"}),e.jsx("div",{className:"pointer-events-none absolute top-1/2 -right-[8px] h-6 w-[8px] -translate-y-1/2 rounded-r-[8px] bg-gray-400"}),e.jsx("div",{className:"pointer-events-none absolute -bottom-[8px] left-1/2 h-[8px] w-6 -translate-x-1/2 rounded-b-[8px] bg-gray-400"}),e.jsx("div",{className:"pointer-events-none absolute top-1/2 -left-[8px] h-6 w-[8px] -translate-y-1/2 rounded-l-[8px] bg-gray-400"}),e.jsx("div",{className:"pointer-events-none absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",children:e.jsx("span",{className:"truncate text-center text-xs font-medium text-gray-800",title:t.table_name,children:t.table_name})})]})}),bt=({areas:t,tables:a,isLoading:n,selectedAreaId:s,draggedTableIndex:i,onTableDragStart:l,onTableDragOver:r,onTableDrop:c})=>{var d;return e.jsxs("div",{className:"flex h-full min-w-0 flex-1 flex-col",children:[e.jsx("h3",{className:"mb-2 flex-shrink-0 truncate font-medium text-gray-900",children:s?`${((d=t.find(h=>h.id===s))==null?void 0:d.area_name)||""}`:"Chọn khu vực để xem bàn"}),e.jsx("div",{className:"min-h-0 flex-1 overflow-x-hidden overflow-y-auto",children:n?e.jsx("div",{className:"flex h-full items-center justify-center text-gray-500",children:"Đang tải bàn..."}):s?a.length===0?e.jsx("div",{className:"flex h-full items-center justify-center text-gray-500",children:"Không có bàn nào trong khu vực này"}):e.jsx("div",{className:"grid grid-cols-2 gap-3 p-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5",children:a.map((h,o)=>e.jsx(ft,{table:h,index:o,onDragStart:l,onDragOver:r,onDrop:c,isDragging:i===o},h.id))}):e.jsx("div",{className:"flex h-full items-center justify-center text-gray-500",children:"Chọn khu vực để xem danh sách bàn"})})]})},vt=t=>{const[a,n]=y.useState(""),[s,i]=y.useState([]),[l,r]=y.useState(null),[c,d]=y.useState(""),[h,o]=y.useState(null),[m,p]=y.useState({}),[v,f]=y.useState(!1),{data:g,isLoading:x}=ae({storeUid:a}),{data:b,isLoading:_}=pe({storeUid:a});y.useEffect(()=>{if(!a){i([]);return}if(g&&g.length>0){const w=[...g].sort((k,D)=>k.sort-D.sort);i(w)}else g&&i([])},[a,g==null?void 0:g.length,g==null?void 0:g.map(w=>w.id).join(",")]),y.useEffect(()=>{t||(n(""),i([]),r(null),d(""),o(null),p({}))},[t]);const N=y.useMemo(()=>!c||!b?[]:m[c]?m[c]:b.filter(w=>w.area_uid===c).sort((w,k)=>w.sort-k.sort),[c,b,m]);return{selectedStoreId:a,sortedAreas:s,draggedIndex:l,selectedAreaId:c,draggedTableIndex:h,filteredTables:N,isSaving:v,areasLoading:x,tablesLoading:_,handleStoreChange:w=>{n(w),d("")},handleAreaClick:w=>{d(w)},handleDragStart:(w,k)=>{r(k),w.dataTransfer.effectAllowed="move",w.dataTransfer.setData("text/html",k.toString())},handleDragOver:w=>{w.preventDefault(),w.dataTransfer.dropEffect="move"},handleDrop:(w,k)=>{if(w.preventDefault(),l===null||l===k){r(null);return}const D=[...s],U=D[l];D.splice(l,1),D.splice(k,0,U);const R=D.map((P,F)=>({...P,sort:F+1}));i(R),r(null)},handleTableDragStart:(w,k)=>{o(k),w.dataTransfer.effectAllowed="move",w.dataTransfer.setData("text/html",k.toString())},handleTableDragOver:w=>{w.preventDefault(),w.dataTransfer.dropEffect="move"},handleTableDrop:(w,k)=>{if(w.preventDefault(),h===null||h===k||!c){o(null);return}const D=[...N],U=D[h];D.splice(h,1),D.splice(k,0,U);const R=D.map((P,F)=>({...P,sort:F+1}));p(P=>({...P,[c]:R})),o(null)},handleSave:async(w,k)=>{if(!(!a||s.length===0)){f(!0);try{const D=s.map((P,F)=>({id:P.id,sort:F,company_uid:P.company_uid,brand_uid:P.brand_uid,store_uid:P.store_uid})),U=[];Object.values(m).forEach(P=>{P.forEach((F,X)=>{U.push({id:F.id,sort:X,company_uid:F.company_uid,brand_uid:F.brand_uid,store_uid:F.store_uid})})});const R=[];R.push(he.post("/pos/v1/area",D)),U.length>0&&R.push(he.post("/pos/v1/table",U)),await Promise.all(R),I.success("Đã cập nhật thứ tự bàn và khu vực thành công"),w(a,s),k(!1)}catch{I.error("Có lỗi xảy ra khi cập nhật thứ tự. Vui lòng thử lại.")}finally{f(!1)}}}}},yt=({open:t,onOpenChange:a,onSave:n})=>{const{selectedStoreId:s,sortedAreas:i,draggedIndex:l,selectedAreaId:r,draggedTableIndex:c,filteredTables:d,isSaving:h,areasLoading:o,tablesLoading:m,handleStoreChange:p,handleAreaClick:v,handleDragStart:f,handleDragOver:g,handleDrop:x,handleTableDragStart:b,handleTableDragOver:_,handleTableDrop:N,handleSave:u}=vt(t),T=()=>{u(async(S,A)=>{const C=A.map(j=>({id:j.id,position:{x:0,y:0},size:{width:100,height:100},table_name:j.area_name||"",area_uid:j.id,store_uid:j.store_uid||"",company_uid:j.company_uid||"",brand_uid:j.brand_uid||"",sort:j.sort||0,active:j.active||1,created_at:j.created_at||new Date().toISOString(),updated_at:j.updated_at||new Date().toISOString()}));await n(C)},a)};return e.jsx(z,{open:t,onOpenChange:a,children:e.jsxs(K,{className:"h-[85vh] !w-[90vw] !max-w-none sm:!max-w-none",children:[e.jsxs(V,{className:"space-y-3",children:[e.jsx(q,{children:"Sắp xếp bàn"}),e.jsx("div",{className:"w-full",children:e.jsx(le,{value:s,onValueChange:p,placeholder:"Chọn điểm áp dụng",className:"w-80"})})]}),e.jsxs("div",{className:"flex h-[calc(85vh-120px)] flex-col",children:[e.jsx("div",{className:"min-h-0 flex-1",children:s&&e.jsxs("div",{className:"flex h-full gap-4",children:[e.jsx(gt,{areas:i,isLoading:o,selectedAreaId:r,draggedIndex:l,onAreaClick:v,onDragStart:f,onDragOver:g,onDrop:x}),e.jsx(bt,{areas:i,tables:d,isLoading:m,selectedAreaId:r,draggedTableIndex:c,onTableDragStart:b,onTableDragOver:_,onTableDrop:N})]})}),e.jsxs("div",{className:"flex justify-end gap-2 border-t pt-4",children:[e.jsx(O,{variant:"outline",onClick:()=>a(!1),children:"Hủy"}),e.jsx(O,{onClick:T,disabled:!s||i.length===0||h,className:"bg-blue-600 hover:bg-blue-700",children:h?"Đang lưu...":"Lưu"})]})]})]})})},jt=({open:t,onOpenChange:a,onDownloadTemplate:n,onUploadFile:s})=>e.jsx(z,{open:t,onOpenChange:a,children:e.jsxs(K,{className:"max-w-2xl",children:[e.jsx(V,{children:e.jsx(q,{className:"text-xl font-semibold",children:"Thêm bàn"})}),e.jsxs("div",{className:"space-y-6 py-4",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:e.jsx("h3",{className:"font-medium text-gray-900",children:"Bước 1. Tải file mẫu"})}),e.jsxs(O,{variant:"outline",size:"sm",onClick:n,className:"flex items-center gap-2",children:[e.jsx(fe,{className:"h-4 w-4"}),"Tải xuống"]})]})}),e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 font-medium text-gray-900",children:"Bước 2. Thêm bàn vào file"}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsx("div",{children:"- Không sửa lại tên dòng tiêu đề"}),e.jsx("div",{children:"- Nhập thông tin bàn vào các dòng sau đó"})]})]}),e.jsx("div",{className:"rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 font-medium text-gray-900",children:"Bước 3. Tải file bàn lên"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Sau khi đã điền đầy đủ bàn bạn có thể tải file lên"})]}),e.jsxs(O,{variant:"outline",size:"sm",onClick:s,className:"flex items-center gap-2",children:[e.jsx(ee,{className:"h-4 w-4"}),"Tải file lên"]})]})})]})]})}),oe={getTables:async t=>{const a=new URLSearchParams;t.skip_limit&&a.append("skip_limit","true"),t.company_uid&&a.append("company_uid",t.company_uid),t.brand_uid&&a.append("brand_uid",t.brand_uid),t.store_uid&&a.append("store_uid",t.store_uid),t.area_uid&&a.append("area_uid",t.area_uid),t.order&&a.append("order",t.order);const n=`/pos/v1/table${a.toString()?`?${a.toString()}`:""}`,s=await W.get(n);return Array.isArray(s.data)?{data:s.data,total:s.data.length}:s.data&&typeof s.data=="object"&&"data"in s.data?s.data:{data:[],total:0}},updateTablePosition:async t=>{await W.patch(`/pos/v1/table/${t.table_uid}`,{position_x:t.position_x,position_y:t.position_y,sort:t.sort})},bulkUpdateTables:async t=>{await W.post("/pos/v1/table",t)}},St=t=>{const{areaUid:a,storeUid:n,enabled:s=!0}=t,{company:i,brands:l}=Q(o=>o.auth),r=l==null?void 0:l[0],c=je.useMemo(()=>{var o;if(n)return n;try{const m=localStorage.getItem("pos_stores_data");if(m){const p=JSON.parse(m);return((o=(Array.isArray(p)?p.filter(f=>f.active===1):[])[0])==null?void 0:o.id)||""}}catch(m){console.error("Error getting store from localStorage:",m)}return""},[n]),d={skip_limit:!0,company_uid:(i==null?void 0:i.id)||"",brand_uid:(r==null?void 0:r.id)||"",store_uid:c,area_uid:a,order:"sort-asc, table_name"},h=!!(i!=null&&i.id&&(r!=null&&r.id)&&a&&c);return it({queryKey:[re.TABLE_LAYOUT,d],queryFn:async()=>(await oe.getTables(d)).data||[],enabled:s&&h,staleTime:5*60*1e3,gcTime:10*60*1e3})},wt=()=>{const t=J(),{company:a,brands:n}=Q(r=>r.auth),s=n==null?void 0:n[0],{mutate:i,isPending:l}=ne({mutationFn:async r=>{const c=(a==null?void 0:a.id)||"",d=(s==null?void 0:s.id)||"";if(!c||!d||!r.storeUid)throw new Error("Thiếu thông tin cần thiết");const o=[...r.tables].sort((m,p)=>{const v=m.position.y-p.position.y;return Math.abs(v)>50?v:m.position.x-p.position.x}).map((m,p)=>({company_uid:c,brand_uid:d,store_uid:r.storeUid,id:m.id,sort:p}));return await Me.updateTablePositions(o)},onSuccess:()=>{t.invalidateQueries({queryKey:[re.TABLES_LIST]}),I.success("Lưu vị trí bàn thành công")},onError:r=>{var d,h;const c=((h=(d=r==null?void 0:r.response)==null?void 0:d.data)==null?void 0:h.message)||"Có lỗi xảy ra khi lưu vị trí bàn";I.error(c)}});return{saveTablePositions:i,isSaving:l}},_t=()=>{const t=J(),{mutate:a,isPending:n}=ne({mutationFn:async s=>{const i=s.map(l=>G.updateArea(l));return await Promise.all(i)},onSuccess:()=>{t.invalidateQueries({queryKey:[re.AREAS_LIST]}),I.success("Cập nhật thứ tự khu vực thành công")},onError:s=>{var l,r;const i=((r=(l=s==null?void 0:s.response)==null?void 0:l.data)==null?void 0:r.message)||"Có lỗi xảy ra khi cập nhật thứ tự khu vực";I.error(i)}});return{updateAreaSort:a,isUpdating:n}},Tt=()=>{const[t,a]=y.useState(""),[n,s]=y.useState(""),[i,l]=y.useState(null),[r,c]=y.useState([]),[d,h]=y.useState(!1),[o,m]=y.useState(!1),[p,v]=y.useState(!1),[f,g]=y.useState(!1),[x,b]=y.useState(!1),[_,N]=y.useState([]),u=y.useRef("");return{selectedAreaId:t,setSelectedAreaId:a,selectedStoreId:n,setSelectedStoreId:s,selectedTable:i,setSelectedTable:l,localTables:r,setLocalTables:c,isSortModalOpen:d,setIsSortModalOpen:h,isImportModalOpen:o,setIsImportModalOpen:m,isPreviewModalOpen:p,setIsPreviewModalOpen:v,isEditTableModalOpen:f,setIsEditTableModalOpen:g,isConfigureTableModalOpen:x,setIsConfigureTableModalOpen:b,importedTableData:_,setImportedTableData:N,lastTablesRef:u}},Nt=({selectedStoreId:t,selectedAreaId:a})=>{const{data:n,isLoading:s}=te(),{data:i,isLoading:l}=ae({storeUid:t}),r=y.useMemo(()=>i?i.sort((h,o)=>h.sort-o.sort).map(h=>({id:h.id,area_name:h.area_name,area_id:h.area_id})):[],[i]),{data:c=[],isLoading:d}=St({areaUid:a,storeUid:t,enabled:!!a&&!!t});return{allStores:n,isLoadingStores:s,areas:r,areasLoading:l,tables:c,tablesLoading:d}},Ct=({localTables:t,setIsEditTableModalOpen:a})=>{const{auth:n}=Q();return{handleEditTableInfo:()=>{a(!0)},handleDownloadExistingTableData:async r=>{var c,d,h;if(!r){I.error("Vui lòng chọn cửa hàng");return}try{I.info("Đang tải dữ liệu bàn...");let o=[],m=[];if((c=n.company)!=null&&c.id&&((h=(d=n.brands)==null?void 0:d[0])!=null&&h.id))try{o=(await oe.getTables({skip_limit:!0,company_uid:n.company.id,brand_uid:n.brands[0].id,store_uid:r,area_uid:""})).data||[],m=await be.getSources({skip_limit:!0,company_uid:n.company.id,brand_uid:n.brands[0].id,store_uid:r})||[]}catch(p){console.warn("API call failed, using local data:",p),o=t,m=[]}else o=t.length>0?t:kt(r),m=At();await Dt(o,m),I.success(`Đã tải xuống dữ liệu ${o.length} bàn`)}catch(o){console.error("Lỗi khi tải dữ liệu bàn:",o),I.error("Không thể tải dữ liệu bàn. Vui lòng thử lại.")}},handleUploadEditedTableFile:r=>{console.warn("handleUploadEditedTableFile is deprecated. Use useEditTablePreview hook instead.")}}},kt=t=>[{id:"table-001",table_name:"Bàn 01",table_id:"B01",description:"Bàn gần cửa sổ",source_id:"10000045",area_uid:"area-001",store_uid:t,company_uid:"",brand_uid:"",sort:1,active:1,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),extra_data:{order_list:[{item_id:"ITEM001",quantity:2},{item_id:"ITEM002",quantity:1}]},area:{id:"area-001",area_id:"AREA-A",area_name:"Khu vực A",active:1,sort:1}},{id:"table-002",table_name:"Bàn 02",table_id:"B02",description:"Bàn ở giữa",source_id:"10000046",area_uid:"area-001",store_uid:t,company_uid:"",brand_uid:"",sort:2,active:1,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),area:{id:"area-001",area_id:"AREA-A",area_name:"Khu vực A",active:1,sort:1}},{id:"table-003",table_name:"Bàn 03",table_id:"B03",description:"Bàn VIP",source_id:"10000047",area_uid:"area-002",store_uid:t,company_uid:"",brand_uid:"",sort:3,active:1,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),extra_data:{order_list:[{item_id:"ITEM003",quantity:3},{item_id:"ITEM004",quantity:1},{item_id:"ITEM005",quantity:2}]},area:{id:"area-002",area_id:"AREA-B",area_name:"Khu vực B",active:1,sort:2}}],At=()=>[{id:"1",sourceId:"10000045",sourceName:"Tại chỗ"},{id:"2",sourceId:"10000046",sourceName:"Mang về"},{id:"3",sourceId:"10000047",sourceName:"Giao hàng"}],Dt=async(t,a)=>{const n=new Map;a.forEach(o=>{n.set(o.sourceId,o.sourceName)});const s=new se.Workbook,i=s.addWorksheet("Sheet");i.addRow(["id","Tên bàn","Nguồn","Món đặt trước","Mô tả"]).eachCell(o=>{o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF79abe3"}},o.font={bold:!0,color:{argb:"FFFFFFFF"}},o.alignment={horizontal:"center",vertical:"middle"}}),t.forEach(o=>{var f;const m=o.source_id&&n.get(o.source_id)||"",p=((f=o.extra_data)==null?void 0:f.order_list)||[],v=p.length>0?p.map(g=>`${g.item_id}x${g.quantity}`).join(", "):"";i.addRow([o.id,o.table_name||"",m,v,o.description||""])}),i.columns=[{width:60},{width:15},{width:15},{width:30},{width:30}];const r=await s.xlsx.writeBuffer(),c=new Blob([r],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),d=document.createElement("a"),h=URL.createObjectURL(c);d.setAttribute("href",h),d.setAttribute("download","update-table.xlsx"),d.style.visibility="hidden",document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(h)},It={getTables:async(t={})=>{const a=new URLSearchParams;t.skip_limit&&a.append("skip_limit","true"),t.brand_uid&&a.append("brand_uid",t.brand_uid),t.company_uid&&a.append("company_uid",t.company_uid),t.list_store_uid&&a.append("list_store_uid",t.list_store_uid),t.store_uid&&a.append("store_uid",t.store_uid);const n=`/pos/v1/table${a.toString()?`?${a.toString()}`:""}`,s=await W.get(n);return Array.isArray(s.data)?{data:s.data,total:s.data.length}:s.data&&typeof s.data=="object"&&"data"in s.data?s.data:{data:[],total:0}},createTables:async t=>{const a=await W.post("/pos/v1/table",t);return a.data.data||a.data||[]}},ve=()=>{const t=J(),{company:a,brands:n}=Q(l=>l.auth),{data:s}=te(),i=ne({mutationFn:async({storeUid:l,tableData:r})=>{var N;const c=(a==null?void 0:a.id)||"",d=((N=n==null?void 0:n[0])==null?void 0:N.id)||"",o=(await G.getAreasList({company_uid:c,brand_uid:d,store_uid:l,skip_limit:!0})).data||[],m=new Set(o.map(u=>u.area_name)),v=Array.from(new Set(r.map(u=>u.khuVuc))).filter(u=>!m.has(u));let f=[];if(v.length>0){const u=v.map((T,E)=>({company_uid:c,brand_uid:d,area_name:T,area_id:`AREA-${Math.random().toString(36).substring(2,6).toUpperCase()}`,store_uid:l,sort:o.length+E+1}));f=await G.bulkImportAreas(u)}const g=[...o,...f],x=new Map(g.map(u=>[u.area_name,u])),b=r.map((u,T)=>{const E=x.get(u.khuVuc);if(!E)throw new Error(`Không tìm thấy khu vực: ${u.khuVuc}`);return{company_uid:c,brand_uid:d,table_name:u.tenBan,table_id:`TABLE-${Math.random().toString(36).substring(2,6).toUpperCase()}`,extra_data:{order_list:[]},description:u.moTa||`Khu vực ${u.khuVuc}`,store_uid:l,source_id:u.nguon||"10000171",area_uid:E.id,area_id:E.area_id,sort:T+1}}),_=await It.createTables(b);return{areas:f,tables:_,storeUid:l,existingAreasCount:o.length}},onSuccess:l=>{const r=s==null?void 0:s.find(o=>o.id===l.storeUid),c=(r==null?void 0:r.name)||l.storeUid,d=l.areas.length,h=l.existingAreasCount||0;d>0?I.success(`Đã tạo ${d} khu vực mới và ${l.tables.length} bàn cho cửa hàng: ${c}${h>0?` (${h} khu vực đã tồn tại)`:""}`):I.success(`Đã tạo ${l.tables.length} bàn cho cửa hàng: ${c} (sử dụng ${h} khu vực có sẵn)`),t.invalidateQueries({queryKey:["areas"]}),t.invalidateQueries({queryKey:["tables"]}),t.invalidateQueries({queryKey:["table-layout"]})},onError:l=>{I.error(l.message||"Có lỗi xảy ra khi import bàn")}});return{importTables:i.mutate,isImporting:i.isPending,error:i.error}},Mt=({selectedAreaId:t,localTables:a,setLocalTables:n,setIsSortModalOpen:s})=>{const{updateAreaSort:i,isUpdating:l}=_t();return{handleSortModalSave:async c=>{if(!t){I.error("Không tìm thấy khu vực được chọn");return}try{const d=c.map((h,o)=>({id:h.id,area_id:h.area_uid||"",area_name:h.table_name,description:"",extra_data:{},active:h.active,revision:1,sort:o+1,store_uid:h.store_uid,company_uid:h.company_uid,brand_uid:h.brand_uid,is_fabi:1,created_by:"system",created_at:h.created_at,updated_at:h.updated_at,list_table_id:[]}));await i(d),n(h=>h.map(o=>{const m=c.find(p=>p.id===o.id);return m?{...o,sort:m.sort}:o})),s(!1),I.success("Đã cập nhật thứ tự bàn thành công")}catch(d){console.error("Lỗi khi cập nhật thứ tự bàn:",d),I.error("Không thể cập nhật thứ tự bàn. Vui lòng thử lại.")}},isUpdatingSort:l}},Et=({selectedStoreId:t,selectedAreaId:a,setSelectedAreaId:n,setSelectedStoreId:s,setSelectedTable:i,setLocalTables:l,setIsSortModalOpen:r,setIsConfigureTableModalOpen:c})=>{const d=xe();return{handleAreaChange:b=>{n(b),i(null)},handleStoreChange:b=>{s(b),n(""),i(null)},handleCreateNewArea:()=>{if(!t){I.error("Vui lòng chọn cửa hàng trước");return}d({to:"/setting/area",search:{store_id:t}})},handleTableSelect:b=>{i(b),d({to:"/setting/table/detail/$tableId",params:{tableId:b.id},search:{store_uid:t,area_uid:a}})},handleTablePositionUpdate:(b,_)=>{l(N=>N.map(u=>u.id===b?{...u,position:_}:u))},handleCreateNew:()=>{if(!t){I.error("Vui lòng chọn cửa hàng trước");return}if(!a){I.error("Vui lòng chọn khu vực trước");return}d({to:"/setting/table",search:{store_id:t,area_id:a}})},handleSortTables:()=>{if(!a){I.error("Vui lòng chọn khu vực để sắp xếp bàn");return}r(!0)},handleCustomizeTable:()=>{c(!0)}}},Lt=({selectedStoreId:t,selectedAreaId:a,localTables:n,importedTableData:s,setSelectedAreaId:i,setSelectedStoreId:l,setSelectedTable:r,setLocalTables:c,setIsSortModalOpen:d,setIsImportModalOpen:h,setIsPreviewModalOpen:o,setIsEditTableModalOpen:m,setIsConfigureTableModalOpen:p,setImportedTableData:v})=>{const{saveTablePositions:f,isSaving:g}=wt(),x=Et({selectedStoreId:t,selectedAreaId:a,setSelectedAreaId:i,setSelectedStoreId:l,setSelectedTable:r,setLocalTables:c,setIsSortModalOpen:d,setIsConfigureTableModalOpen:p}),b=Ct({localTables:n,setIsEditTableModalOpen:m}),_=Mt({selectedAreaId:a,localTables:n,setLocalTables:c,setIsSortModalOpen:d}),N=ve();return{...x,...b,..._,...N,handleImportTables:()=>{if(!t){I.error("Vui lòng chọn cửa hàng trước");return}h(!0)},handleDownloadTemplate:()=>{const j=document.createElement("a");j.href="/files/setting/table/table-import-template.xlsx",j.download="create-table.xlsx",j.click()},handleUploadFile:()=>{const j=document.createElement("input");j.type="file",j.accept=".xlsx,.xls,.csv",j.onchange=async M=>{var w;const L=(w=M.target.files)==null?void 0:w[0];if(L)try{I.info("Đang đọc file...");const k=await Ot(L);v(k),o(!0),I.success(`Đã đọc ${k.length} bàn từ file`)}catch(k){console.error("Error parsing file:",k),I.error("Không thể đọc file. Vui lòng kiểm tra định dạng file.")}},j.click()},handlePreviewSave:()=>{if(s.length===0){I.error("Không có dữ liệu để import");return}const j=s.map((M,L)=>({id:`imported-${Date.now()}-${L}`,table_name:M.tenBan,table_code:M.tenBan,description:M.moTa,area_uid:a,store_uid:t,company_uid:"",brand_uid:"",sort:L+1,active:1,position:{x:100+L%5*120,y:100+Math.floor(L/5)*80},size:{width:100,height:60},created_at:new Date().toISOString(),updated_at:new Date().toISOString()}));c(M=>[...M,...j]),o(!1),h(!1),v([]),I.success(`Đã import ${j.length} bàn thành công`)},handlePreviewCancel:()=>{o(!1),v([])},handleSaveLayout:()=>{if(!t||n.length===0){I.error("Không có dữ liệu để lưu");return}f({tables:n,storeUid:t})},setIsSortModalOpen:d,setIsImportModalOpen:h,setIsPreviewModalOpen:o,setIsEditTableModalOpen:m,setIsConfigureTableModalOpen:p,isSaving:g,isUpdatingSort:_.isUpdatingSort}},Ot=async t=>new Promise((a,n)=>{const s=new FileReader;s.onload=async i=>{var l;try{const r=(l=i.target)==null?void 0:l.result,c=new se.Workbook;await c.xlsx.load(r);const d=c.getWorksheet(1);if(!d)throw new Error("Không thể đọc worksheet từ file Excel");const h=[];d.eachRow((o,m)=>{var p,v,f,g,x;if(m>1){const b={tenBan:((p=o.getCell(1).value)==null?void 0:p.toString())||"",khuVuc:((v=o.getCell(2).value)==null?void 0:v.toString())||"",nguon:((f=o.getCell(3).value)==null?void 0:f.toString())||"",monDatTruoc:((g=o.getCell(4).value)==null?void 0:g.toString())||"",moTa:((x=o.getCell(5).value)==null?void 0:x.toString())||""};b.tenBan.trim()&&h.push(b)}}),a(h)}catch(r){n(r)}},s.onerror=()=>n(new Error("Failed to read file")),s.readAsArrayBuffer(t)}),Pt=({allStores:t,selectedStoreId:a,setSelectedStoreId:n,areas:s,selectedAreaId:i,setSelectedAreaId:l,tables:r,setLocalTables:c,lastTablesRef:d})=>{y.useEffect(()=>{t&&t.length>0&&!a&&n(t[0].id)},[t,a,n]),y.useEffect(()=>{s.length>0&&!i&&l(s[0].id)},[s,i,l]),y.useEffect(()=>{const h=JSON.stringify(r.map(x=>({id:x.id,position_x:x.position_x,position_y:x.position_y})));if(d.current===h)return;if(d.current=h,r.length===0){c([]);return}const o=180,m=60,p=50,v=50,f=5,g=r.map((x,b)=>{var E,S,A,C,j,M,L,w;const _=x.position_x!==void 0&&x.position_y!==void 0,N="extra_data"in x&&((S=(E=x.extra_data)==null?void 0:E.position)==null?void 0:S.x)!==void 0&&((C=(A=x.extra_data)==null?void 0:A.position)==null?void 0:C.y)!==void 0;let u,T;if(_)u=x.position_x,T=x.position_y;else if(N)u=x.extra_data.position.x,T=x.extra_data.position.y;else{const k=Math.floor(b/f),D=b%f;u=p+D*(o+m),T=v+k*(o+m)}return{...x,position:{x:u,y:T},size:{width:x.width||("extra_data"in x?(M=(j=x.extra_data)==null?void 0:j.size)==null?void 0:M.width:void 0)||o,height:x.height||("extra_data"in x?(w=(L=x.extra_data)==null?void 0:L.size)==null?void 0:w.height:void 0)||o}}});c(g)},[r,i,a,c,d])},Ft=()=>{const t=Tt(),a=Nt({selectedStoreId:t.selectedStoreId,selectedAreaId:t.selectedAreaId}),n=Lt({selectedStoreId:t.selectedStoreId,selectedAreaId:t.selectedAreaId,localTables:t.localTables,importedTableData:t.importedTableData,setSelectedAreaId:t.setSelectedAreaId,setSelectedStoreId:t.setSelectedStoreId,setSelectedTable:t.setSelectedTable,setLocalTables:t.setLocalTables,setIsSortModalOpen:t.setIsSortModalOpen,setIsImportModalOpen:t.setIsImportModalOpen,setIsPreviewModalOpen:t.setIsPreviewModalOpen,setIsEditTableModalOpen:t.setIsEditTableModalOpen,setIsConfigureTableModalOpen:t.setIsConfigureTableModalOpen,setImportedTableData:t.setImportedTableData});return Pt({allStores:a.allStores,selectedStoreId:t.selectedStoreId,setSelectedStoreId:t.setSelectedStoreId,areas:a.areas,selectedAreaId:t.selectedAreaId,setSelectedAreaId:t.setSelectedAreaId,tables:a.tables,setLocalTables:t.setLocalTables,lastTablesRef:t.lastTablesRef}),{selectedAreaId:t.selectedAreaId,selectedStoreId:t.selectedStoreId,selectedTable:t.selectedTable,localTables:t.localTables,isSortModalOpen:t.isSortModalOpen,isImportModalOpen:t.isImportModalOpen,isPreviewModalOpen:t.isPreviewModalOpen,isEditTableModalOpen:t.isEditTableModalOpen,isConfigureTableModalOpen:t.isConfigureTableModalOpen,importedTableData:t.importedTableData,allStores:a.allStores,isLoadingStores:a.isLoadingStores,areas:a.areas,areasLoading:a.areasLoading,tables:a.tables,tablesLoading:a.tablesLoading,...n}},Ut=({onSuccess:t}={})=>{const{auth:a}=Q(),[n,s]=y.useState(!1),[i,l]=y.useState(!1),[r,c]=y.useState([]),[d,h]=y.useState("");return{isPreviewModalOpen:n,isSuccessModalOpen:i,previewData:r,handleUploadFile:g=>{if(h(g),!g){I.error("Vui lòng chọn cửa hàng");return}const x=document.createElement("input");x.type="file",x.accept=".csv,.xlsx,.xls",x.onchange=async b=>{var N;const _=(N=b.target.files)==null?void 0:N[0];if(_)try{I.info("Đang đọc file...");const u=await Bt(_);c(u),s(!0)}catch(u){console.error("Error parsing file:",u),I.error("Không thể đọc file. Vui lòng kiểm tra định dạng file.")}},x.click()},handleRemoveRow:g=>{c(x=>x.filter((b,_)=>_!==g))},handleConfirmImport:async()=>{var g,x,b;try{I.info("Đang cập nhật thông tin bàn...");const _=await be.getSources({skip_limit:!0,company_uid:((g=a.company)==null?void 0:g.id)||"",brand_uid:((b=(x=a.brands)==null?void 0:x[0])==null?void 0:b.id)||"",store_uid:d}),N=new Map;_.forEach(T=>{N.set(T.sourceName,T.sourceId)});const u=r.map(T=>{var E,S,A;return{id:T.id,table_name:T.tenBan,store_uid:d,company_uid:((E=a.company)==null?void 0:E.id)||"",brand_uid:((A=(S=a.brands)==null?void 0:S[0])==null?void 0:A.id)||"",source_id:N.get(T.nguon)||"",extra_data:{order_list:T.monDatTruoc?Rt(T.monDatTruoc):[]},description:T.moTa}});await oe.bulkUpdateTables(u),s(!1),l(!0),t==null||t()}catch(_){console.error("Error updating tables:",_),I.error("Không thể cập nhật thông tin bàn. Vui lòng thử lại.")}},handleClosePreview:()=>{s(!1),c([])},handleCloseSuccess:()=>{l(!1),c([]),h("")}}},Rt=t=>t.trim()?t.split(",").map(a=>{const n=a.trim(),s=n.split("x");return s.length===2?{item_id:s[0].trim(),quantity:parseInt(s[1].trim())||1}:{item_id:n,quantity:1}}).filter(a=>a.item_id):[],Bt=async t=>new Promise((a,n)=>{const s=new FileReader;s.onload=async i=>{var l;try{const r=(l=i.target)==null?void 0:l.result,c=new se.Workbook;await c.xlsx.load(r);const d=c.getWorksheet(1);if(!d)throw new Error("Không thể đọc worksheet từ file Excel");const h=[];d.eachRow((o,m)=>{var p,v,f,g,x;if(m>1){const b={id:((p=o.getCell(1).value)==null?void 0:p.toString())||"",tenBan:((v=o.getCell(2).value)==null?void 0:v.toString())||"",nguon:((f=o.getCell(3).value)==null?void 0:f.toString())||"",monDatTruoc:((g=o.getCell(4).value)==null?void 0:g.toString())||"",moTa:((x=o.getCell(5).value)==null?void 0:x.toString())||""};b.id.trim()&&h.push(b)}}),a(h)}catch(r){n(r)}},s.onerror=()=>n(new Error("Failed to read file")),s.readAsArrayBuffer(t)}),$t=({open:t,onOpenChange:a,tableData:n,onSave:s,onCancel:i})=>{const[l,r]=y.useState(""),{importTables:c,isImporting:d}=ve(),h=()=>{l&&n.length>0&&c({storeUid:l,tableData:n},{onSuccess:()=>{s(l),a(!1)}})},o=()=>{i(),a(!1)};return e.jsx(z,{open:t,onOpenChange:a,children:e.jsxs(K,{className:"flex max-h-[90vh] !max-w-4xl flex-col overflow-hidden",children:[e.jsx(V,{children:e.jsx(q,{className:"text-xl font-semibold",children:"Thêm bàn"})}),e.jsxs("div",{className:"flex flex-1 flex-col space-y-4 overflow-hidden",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Các bàn sẽ áp dụng cho cửa hàng được chọn"}),e.jsx(le,{value:l,onValueChange:r,placeholder:"Chọn điểm áp dụng",className:"w-64"})]}),e.jsx("div",{className:"rounded-lg border border-orange-200 bg-orange-50 p-3",children:e.jsx("p",{className:"text-sm font-medium text-orange-600",children:"Các khu vực chưa tồn tại sẽ được tạo trước khi thêm bàn"})}),e.jsx("div",{className:"flex-1 overflow-auto rounded-lg border",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"sticky top-0 bg-gray-100",children:e.jsxs("tr",{children:[e.jsx("th",{className:"border-r px-4 py-3 text-left text-sm font-medium text-gray-700",children:"Tên bàn"}),e.jsx("th",{className:"border-r px-4 py-3 text-left text-sm font-medium text-gray-700",children:"Khu vực"}),e.jsx("th",{className:"border-r px-4 py-3 text-left text-sm font-medium text-gray-700",children:"Nguồn"}),e.jsx("th",{className:"border-r px-4 py-3 text-left text-sm font-medium text-gray-700",children:"Món đặt trước"}),e.jsx("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-700",children:"Mô tả"})]})}),e.jsx("tbody",{children:n.map((m,p)=>e.jsxs("tr",{className:"border-b hover:bg-gray-50",children:[e.jsx("td",{className:"border-r px-4 py-3 text-sm text-gray-900",children:m.tenBan}),e.jsx("td",{className:"border-r px-4 py-3 text-sm text-gray-900",children:m.khuVuc}),e.jsx("td",{className:"border-r px-4 py-3 text-sm text-gray-900",children:m.nguon}),e.jsx("td",{className:"border-r px-4 py-3 text-sm text-gray-900",children:m.monDatTruoc}),e.jsx("td",{className:"px-4 py-3 text-sm text-gray-900",children:m.moTa})]},p))})]})}),e.jsxs("div",{className:"flex justify-end space-x-3 border-t pt-4",children:[e.jsx(O,{variant:"outline",onClick:o,children:"Đóng"}),e.jsx(O,{onClick:h,disabled:!l||d,className:"bg-green-600 hover:bg-green-700",children:d?"Đang lưu...":"Lưu"})]})]})]})})},zt=({open:t,onOpenChange:a,data:n,onConfirm:s,onRemoveRow:i})=>{const[l,r]=y.useState(!1),[c,d]=y.useState(null),h=p=>{d(p),r(!0)},o=()=>{c!==null&&(i(c),d(null)),r(!1)},m=()=>{d(null),r(!1)};return e.jsxs(e.Fragment,{children:[e.jsx(z,{open:t,onOpenChange:a,children:e.jsxs(K,{className:"flex max-h-[80vh] !max-w-6xl flex-col overflow-hidden",children:[e.jsx(V,{children:e.jsx(q,{className:"text-xl font-semibold",children:"Sửa thông tin bàn"})}),e.jsx("div",{className:"flex-1 overflow-auto",children:e.jsxs(Ze,{children:[e.jsx(et,{children:e.jsxs(ue,{children:[e.jsx(B,{className:"w-8"}),e.jsx(B,{className:"min-w-[300px]",children:"id"}),e.jsx(B,{className:"min-w-[120px]",children:"Tên bàn"}),e.jsx(B,{className:"min-w-[120px]",children:"Nguồn"}),e.jsx(B,{className:"min-w-[150px]",children:"Món đặt trước"}),e.jsx(B,{className:"min-w-[200px]",children:"Mô tả"})]})}),e.jsx(tt,{children:n.map((p,v)=>e.jsxs(ue,{className:"hover:bg-gray-50",children:[e.jsx($,{children:e.jsx(O,{variant:"ghost",size:"sm",onClick:()=>h(v),className:"h-8 w-8 p-0 text-red-500 hover:bg-red-50 hover:text-red-700",children:e.jsx(Z,{className:"h-4 w-4"})})}),e.jsx($,{className:"font-mono text-xs break-all",children:p.id}),e.jsx($,{className:"font-medium",children:p.tenBan}),e.jsx($,{children:p.nguon}),e.jsx($,{className:"font-mono text-sm",children:p.monDatTruoc}),e.jsx($,{children:p.moTa})]},v))})]})}),e.jsx("div",{className:"flex items-center justify-end border-t pt-4",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(O,{variant:"outline",onClick:()=>a(!1),children:"Đóng"}),e.jsx(O,{onClick:s,className:"bg-green-600 hover:bg-green-700",disabled:n.length===0,children:"Lưu"})]})})]})}),e.jsx(Je,{open:l,onOpenChange:r,content:"Bạn có muốn bỏ món",onConfirm:o,onCancel:m})]})},Kt=({open:t,onOpenChange:a})=>e.jsx(z,{open:t,onOpenChange:a,children:e.jsxs(K,{className:"sm:max-w-md",children:[e.jsx(V,{children:e.jsx(q,{className:"text-center text-xl font-semibold",children:"Sửa thông tin bàn"})}),e.jsxs("div",{className:"flex flex-col items-center space-y-6 py-8",children:[e.jsx("div",{className:"flex h-20 w-20 items-center justify-center rounded-full bg-green-100",children:e.jsx("div",{className:"flex h-16 w-16 items-center justify-center rounded-full bg-green-500",children:e.jsx(at,{className:"h-8 w-8 text-white",strokeWidth:3})})}),e.jsx("div",{className:"text-center",children:e.jsx("h3",{className:"text-lg font-medium text-green-600",children:"Tải lên thành công"})}),e.jsx(O,{onClick:()=>a(!1),variant:"outline",className:"px-8",children:"Đóng"})]})]})}),Vt=({open:t,onOpenChange:a,onDownloadExistingData:n,onUploadFile:s,stores:i})=>{const[l,r]=y.useState(""),{isPreviewModalOpen:c,isSuccessModalOpen:d,previewData:h,handleUploadFile:o,handleRemoveRow:m,handleConfirmImport:p,handleClosePreview:v,handleCloseSuccess:f}=Ut({onSuccess:()=>a(!1)}),g=_=>{r(_)},x=()=>{l&&n(l)},b=()=>{l&&o(l)};return y.useEffect(()=>{t||r("")},[t]),e.jsxs(e.Fragment,{children:[e.jsx(z,{open:t,onOpenChange:a,children:e.jsxs(K,{className:"max-w-2xl",children:[e.jsx(V,{children:e.jsx(q,{className:"text-xl font-semibold",children:"Sửa thông tin bàn"})}),e.jsxs("div",{className:"space-y-6 py-4",children:[e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 font-medium text-gray-900",children:"Bước 1. Chọn cửa hàng"}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Chọn cửa hàng"}),e.jsxs(Ue,{value:l,onValueChange:g,children:[e.jsx(Re,{children:e.jsx(Be,{placeholder:"Chọn cửa hàng"})}),e.jsx($e,{children:i.map(_=>e.jsx(ze,{value:_.id,children:_.name},_.id))})]})]})]}),e.jsx("div",{className:"rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"Bước 2. Tải file dữ liệu bàn đã có."}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Tải xuống"})]}),e.jsx("div",{className:"flex gap-2",children:e.jsxs(O,{variant:"outline",size:"sm",onClick:x,className:"flex items-center gap-2",disabled:!l,children:[e.jsx(fe,{className:"h-4 w-4"}),"Tải xuống"]})})]})}),e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 font-medium text-gray-900",children:"Bước 3. Thêm cấu hình vào file"}),e.jsx("div",{className:"space-y-2 text-sm text-gray-600",children:e.jsxs("div",{children:["Không sửa các cột ",e.jsx("span",{className:"font-medium",children:"id"}),"."]})})]}),e.jsx("div",{className:"rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 font-medium text-gray-900",children:"Bước 4. Tải file lên"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Sau khi đã điền đầy đủ bàn có thể tải file lên"})]}),e.jsxs(O,{variant:"outline",size:"sm",onClick:b,className:"flex items-center gap-2",disabled:!l,children:[e.jsx(ee,{className:"h-4 w-4"}),"Tải file lên"]})]})})]})]})}),e.jsx(zt,{open:c,onOpenChange:v,data:h,onConfirm:p,onRemoveRow:m}),e.jsx(Kt,{open:d,onOpenChange:f})]})},qt=["#1E40AF","#EA580C","#C026D3","#FCD34D","#6B7280","#000000","#FF4500","#32CD32","#00BFFF","#4169E1","#FF1493","#8B4513","#FFB6C1","#DDA0DD","#CD5C5C","#A0522D","#8B0000","#2F4F4F"],Ht=({selectedColor:t,onColorSelect:a})=>e.jsx("div",{className:"absolute top-8 left-0 z-50 w-80 rounded-lg border bg-white p-4 shadow-lg",children:e.jsx("div",{className:"mb-4",children:e.jsx("div",{className:"grid grid-cols-6 gap-2",children:qt.map(n=>e.jsx("div",{className:`h-8 w-8 cursor-pointer rounded border-2 ${t===n?"border-blue-500":"border-gray-300"}`,style:{backgroundColor:n},onClick:()=>a(n)},n))})})}),Wt=({stores:t,selectedStoreId:a,config:n,showColorPicker:s,onStoreChange:i,onConfigChange:l,onColorPickerToggle:r,onColorSelect:c})=>{const d=o=>{c(o),r(!1)},h=[{value:"",label:"A Hùng"},...t.map(o=>({value:o.id,label:o.store_name}))];return e.jsx("div",{className:"w-full border-b bg-gray-50 p-4",children:e.jsx("div",{className:"flex items-center justify-between gap-4",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(nt,{options:h,value:a,onValueChange:i,className:"min-w-[250px]"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Màu chữ"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"h-6 w-8 cursor-pointer rounded border",style:{backgroundColor:n.color},onClick:()=>r(!s)}),s&&e.jsx(Ht,{selectedColor:n.color,onColorSelect:d})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Kích cỡ chữ"}),e.jsx("input",{type:"range",min:"10",max:"30",value:n.fontSize,onChange:o=>l({fontSize:o.target.value}),className:"w-20"}),e.jsxs("span",{className:"min-w-[40px] text-sm",children:[n.fontSize,"px"]})]})]})})})},Qt=({areas:t,selectedAreaId:a,isLoadingTables:n,onAreaSelect:s})=>{if(n)return e.jsx("div",{className:"w-80 overflow-y-auto border-r bg-gray-50",children:e.jsx("div",{className:"p-4 text-center text-gray-500",children:"Đang tải dữ liệu bàn..."})});const i=[...t].sort((l,r)=>{const c=l.sort??0,d=r.sort??0;return c-d});return e.jsx("div",{className:"w-80 overflow-y-auto border-r bg-gray-50",children:i.map(l=>{const r=a===l.id;return e.jsx("div",{className:`cursor-pointer border-b transition-colors ${r?"border-blue-200 bg-blue-50":"hover:bg-gray-100"}`,onClick:()=>s(l.id),children:e.jsxs("div",{className:"flex items-center gap-3 p-4",children:[e.jsxs("div",{className:"flex flex-col items-center justify-center",children:[e.jsx(rt,{className:`h-4 w-4 ${r?"text-blue-600":"text-gray-400"}`}),e.jsx(lt,{className:`h-4 w-4 ${r?"text-blue-600":"text-gray-400"}`})]}),e.jsx("span",{className:`text-sm font-medium ${r?"text-blue-900":"text-gray-700"}`,children:l.area_name})]})},l.id)})})},Yt=({table:t,isSelected:a,displayColor:n,displayFontSize:s,onSelect:i})=>e.jsx("div",{className:`relative cursor-pointer transition-all ${a?"opacity-100":"opacity-70 hover:opacity-100"}`,onClick:()=>i(t.id),children:e.jsx("div",{className:"relative flex flex-col items-center",children:e.jsx("div",{className:"relative",children:e.jsxs("div",{className:`relative mx-auto h-16 w-20 cursor-pointer rounded-[12px] shadow-md transition-all duration-200 hover:shadow-lg ${a?"bg-blue-500":"bg-gray-300"}`,children:[e.jsx("div",{className:`pointer-events-none absolute -top-[8px] left-1/2 h-[8px] w-6 -translate-x-1/2 rounded-t-[8px] ${a?"bg-blue-600":"bg-gray-400"}`}),e.jsx("div",{className:`pointer-events-none absolute top-1/2 -right-[8px] h-6 w-[8px] -translate-y-1/2 rounded-r-[8px] ${a?"bg-blue-600":"bg-gray-400"}`}),e.jsx("div",{className:`pointer-events-none absolute -bottom-[8px] left-1/2 h-[8px] w-6 -translate-x-1/2 rounded-b-[8px] ${a?"bg-blue-600":"bg-gray-400"}`}),e.jsx("div",{className:`pointer-events-none absolute top-1/2 -left-[8px] h-6 w-[8px] -translate-y-1/2 rounded-l-[8px] ${a?"bg-blue-600":"bg-gray-400"}`}),e.jsx("div",{className:"pointer-events-none absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",children:e.jsx("span",{className:"truncate text-center text-xs font-medium",title:t.table_name,style:{color:a?"white":n,fontSize:`${Math.min(parseInt(s),12)}px`},children:t.table_name})})]})})})}),Xt=({selectedStoreId:t,selectedAreaId:a,areas:n,tablesByArea:s,selectedTables:i,config:l,selectAllInArea:r,applyToAllTables:c,searchTerm:d,onTableSelect:h,onSelectAllInAreaChange:o,onApplyToAllTablesChange:m,onSearchChange:p})=>{var v;return t?e.jsxs("div",{className:"flex flex-1 flex-col",children:[a&&e.jsx("div",{className:"border-b bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("h3",{className:"font-medium text-gray-900",children:((v=n.find(f=>f.id===a))==null?void 0:v.area_name)||"Khu vực"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Vui lòng chọn bàn"})]}),e.jsxs("div",{className:"flex items-center gap-6",children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:r,onChange:f=>o(f.target.checked),disabled:!a,id:"selectAllInArea"}),e.jsx("label",{htmlFor:"selectAllInArea",className:"text-sm",children:"Chọn tất cả bàn trong khu vực"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:c,onChange:f=>m(f.target.checked),id:"applyToAllTables"}),e.jsx("label",{htmlFor:"applyToAllTables",className:"text-sm",children:"Áp dụng với toàn bộ bàn tại cửa hàng"})]})]}),e.jsxs("div",{className:"relative",children:[e.jsx(ot,{className:"absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Tìm kiếm tên bàn",value:d,onChange:f=>p(f.target.value),className:"w-48 rounded-md border py-2 pr-4 pl-10 text-sm"})]})]})]})}),e.jsx("div",{className:"flex-1 overflow-y-auto p-6",children:Object.keys(s).length===0?e.jsx("div",{className:"flex h-full items-center justify-center text-gray-500",children:a?"Không có bàn nào trong khu vực này":"Vui lòng chọn khu vực để xem danh sách bàn"}):Object.entries(s).map(([f,g])=>e.jsx("div",{className:"mb-8",children:e.jsx("div",{className:"mb-6 grid grid-cols-5 gap-6",children:g.map(x=>{var E,S;const b=i.has(x.id),_=((E=x.extra_data)==null?void 0:E.color)||"#6B7280",N=((S=x.extra_data)==null?void 0:S.font_size)||"15",u=b?l.color:_,T=b?l.fontSize:N;return e.jsx(Yt,{table:x,isSelected:b,displayColor:u,displayFontSize:T,onSelect:h},x.id)})})},f))})]}):e.jsx("div",{className:"flex flex-1 items-center justify-center text-gray-500",children:"Vui lòng chọn cửa hàng"})},Gt=({selectedStoreId:t,selectedAreaId:a,searchTerm:n,selectedTables:s,config:i,setSelectedStoreId:l,setSelectedAreaId:r,setSelectedTables:c,setSearchTerm:d,setSelectAllInArea:h,setApplyToAllTables:o})=>{const{data:m=[]}=ae({storeUid:t,page:1,results_per_page:1e3}),{data:p=[],isLoading:v}=pe({storeUid:t,page:1,limit:1e3}),{mutateAsync:f,isPending:g}=Ee(),x=y.useMemo(()=>n?p.filter(S=>S.table_name.toLowerCase().includes(n.toLowerCase())):p,[p,n]),b=y.useMemo(()=>{const S={};return(a?x.filter(C=>C.area_uid===a):x).forEach(C=>{var M;const j=((M=C.area)==null?void 0:M.area_name)||"Không có khu vực";S[j]||(S[j]=[]),S[j].push(C)}),S},[x,a]);return{areas:m,tables:p,filteredTables:x,tablesByArea:b,isLoadingTables:v,isUpdating:g,handleStoreChange:S=>{l(S),r(""),c(new Set),d(""),h(!1),o(!1)},handleTableSelect:S=>{const A=new Set(s);A.has(S)?A.delete(S):A.add(S),c(A)},handleSelectAllInArea:S=>{var A,C;if(h(S),S&&a){const j=(A=m.find(M=>M.id===a))==null?void 0:A.area_name;if(j&&b[j]){const M=b[j].map(L=>L.id);c(new Set([...s,...M]))}}else if(!S&&a){const j=(C=m.find(M=>M.id===a))==null?void 0:C.area_name;if(j&&b[j]){const M=new Set(b[j].map(w=>w.id)),L=new Set([...s].filter(w=>!M.has(w)));c(L)}}},handleApplyToAllTables:S=>{if(o(S),S){const A=p.map(C=>C.id);c(new Set(A))}else c(new Set)},handleSave:async()=>{if(s.size!==0)try{const A=p.filter(C=>s.has(C.id)).map(C=>{var j;return{...C,store_uid:C.store_uid||t,company_uid:C.company_uid||"",brand_uid:C.brand_uid||"",source_id:C.source_id||"",area_uid:C.area_uid||"",sort:C.sort||1,description:C.description||"",extra_data:{...C.extra_data,color:i.color,font_size:i.fontSize,order_list:((j=C.extra_data)==null?void 0:j.order_list)||[]}}});return await f({storeUid:t,tables:A}),!0}catch(S){return console.error("Error updating tables:",S),!1}}}},Jt=()=>{const[t,a]=y.useState(""),[n,s]=y.useState(""),[i,l]=y.useState(""),[r,c]=y.useState(new Set),[d,h]=y.useState(!1),[o,m]=y.useState({color:"#1E40AF",fontSize:"15"}),[p,v]=y.useState(!1),[f,g]=y.useState(!1),x=y.useMemo(()=>{try{const _=localStorage.getItem("pos_stores_data");if(_){const N=JSON.parse(_);return Array.isArray(N)?N.filter(u=>u.active===1):[]}return[]}catch{return[]}},[]);return{selectedStoreId:t,selectedAreaId:n,searchTerm:i,selectedTables:r,showColorPicker:d,config:o,selectAllInArea:p,applyToAllTables:f,stores:x,setSelectedStoreId:a,setSelectedAreaId:s,setSearchTerm:l,setSelectedTables:c,setShowColorPicker:h,setConfig:m,setSelectAllInArea:v,setApplyToAllTables:g,resetState:()=>{a(""),s(""),l(""),c(new Set),h(!1),v(!1),g(!1),m({color:"#1E40AF",fontSize:"15"})}}},Zt=()=>{const t=Jt(),a=Gt({selectedStoreId:t.selectedStoreId,selectedAreaId:t.selectedAreaId,searchTerm:t.searchTerm,selectedTables:t.selectedTables,config:t.config,setSelectedStoreId:t.setSelectedStoreId,setSelectedAreaId:t.setSelectedAreaId,setSelectedTables:t.setSelectedTables,setSearchTerm:t.setSearchTerm,setSelectAllInArea:t.setSelectAllInArea,setApplyToAllTables:t.setApplyToAllTables});return{...t,...a,handleClose:async()=>(t.resetState(),!0),handleSave:async()=>{const i=await a.handleSave();return i&&t.resetState(),i}}},ea=({open:t,onOpenChange:a,onCancel:n})=>{const{selectedStoreId:s,selectedAreaId:i,searchTerm:l,selectedTables:r,showColorPicker:c,config:d,selectAllInArea:h,applyToAllTables:o,stores:m,setShowColorPicker:p,setConfig:v,setSearchTerm:f,setSelectedAreaId:g,areas:x,tablesByArea:b,isLoadingTables:_,isUpdating:N,handleStoreChange:u,handleTableSelect:T,handleSelectAllInArea:E,handleApplyToAllTables:S,handleClose:A,handleSave:C}=Zt(),j=async()=>{await A(),a(!1),n()},M=async()=>{await C()&&(a(!1),n())},L=D=>{g(D)},w=D=>{v({...d,color:D})},k=D=>{v({...d,...D})};return e.jsx(st,{title:"Cấu hình bàn",open:t,onOpenChange:a,onCancel:j,onConfirm:()=>{},confirmText:"Lưu",cancelText:"Đóng",centerTitle:!0,maxWidth:"sm:max-w-7xl",isLoading:!1,confirmDisabled:!1,hideButtons:!0,children:e.jsxs("div",{className:"flex h-[75vh] flex-col",children:[e.jsx(Wt,{stores:m,selectedStoreId:s,config:d,showColorPicker:c,onStoreChange:u,onConfigChange:k,onColorPickerToggle:p,onColorSelect:w}),s?e.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[e.jsx(Qt,{areas:x,tables:Object.values(b).flat(),selectedAreaId:i,isLoadingTables:_,onAreaSelect:L}),e.jsx(Xt,{selectedStoreId:s,selectedAreaId:i,areas:x,tablesByArea:b,selectedTables:r,config:d,selectAllInArea:h,applyToAllTables:o,searchTerm:l,onTableSelect:T,onSelectAllInAreaChange:E,onApplyToAllTablesChange:S,onSearchChange:f})]}):e.jsx("div",{className:"flex flex-1 items-center justify-center text-gray-500",children:"Vui lòng chọn cửa hàng"}),e.jsxs("div",{className:"flex justify-end gap-3 border-t p-6",children:[e.jsx(O,{variant:"outline",onClick:j,disabled:N,children:"Đóng"}),e.jsx(O,{onClick:M,disabled:r.size===0||N,children:N?"Đang lưu...":"Lưu"})]})]})})},ta=()=>{const{selectedAreaId:t,selectedStoreId:a,selectedTable:n,localTables:s,isSortModalOpen:i,isImportModalOpen:l,isPreviewModalOpen:r,isEditTableModalOpen:c,isConfigureTableModalOpen:d,importedTableData:h,allStores:o,isLoadingStores:m,areas:p,areasLoading:v,tablesLoading:f,handleAreaChange:g,handleStoreChange:x,handleCreateNewArea:b,handleTableSelect:_,handleTablePositionUpdate:N,handleCreateNew:u,handleImportTables:T,handleEditTableInfo:E,handleDownloadExistingTableData:S,handleUploadEditedTableFile:A,handleSortTables:C,handleSortModalSave:j,handleCustomizeTable:M,handleSaveLayout:L,handleDownloadTemplate:w,handleUploadFile:k,handlePreviewSave:D,handlePreviewCancel:U,setIsSortModalOpen:R,setIsImportModalOpen:P,setIsPreviewModalOpen:F,setIsEditTableModalOpen:X,setIsConfigureTableModalOpen:ie,isSaving:ye}=Ft(),ce=s;return m||v?e.jsx("div",{className:"flex h-64 items-center justify-center",children:e.jsx("div",{className:"text-gray-500",children:m?"Đang tải cửa hàng...":"Đang tải khu vực..."})}):e.jsxs("div",{className:"flex h-screen flex-col bg-gray-50",children:[e.jsx(mt,{selectedStoreId:a,onStoreChange:x,onCreateNew:u,onSaveLayout:L,isUpdating:ye,onImportTables:T,onEditTableInfo:E,onSortTables:C,onCustomizeTable:M}),e.jsx(xt,{areas:p,selectedAreaId:t,onAreaChange:g,onCreateNew:b,storeUid:a}),e.jsx("div",{className:"flex-1 p-4",children:f?e.jsx("div",{className:"flex h-full items-center justify-center",children:e.jsx("div",{className:"text-gray-500",children:"Đang tải bàn..."})}):ce.length===0?e.jsx("div",{className:"flex h-full items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mb-2 text-lg text-gray-500",children:"Chưa có thông tin bàn"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Vui lòng thêm bàn mới hoặc chọn khu vực khác"})]})}):e.jsx(ht,{tables:ce,onTableSelect:_,selectedTableId:n==null?void 0:n.id,onTablePositionUpdate:N,storeUid:a})}),e.jsx(yt,{open:i,onOpenChange:R,onSave:j}),e.jsx(jt,{open:l,onOpenChange:P,onDownloadTemplate:w,onUploadFile:k}),e.jsx($t,{open:r,onOpenChange:F,tableData:h,onSave:D,onCancel:U}),e.jsx(Vt,{open:c,onOpenChange:X,onDownloadExistingData:S,onUploadFile:A,stores:(o==null?void 0:o.map(de=>({id:de.id,name:de.name})))||[{id:"test-store-1",name:"Cửa hàng Test 1"},{id:"test-store-2",name:"Cửa hàng Test 2"}]}),e.jsx(ea,{open:d,onOpenChange:ie,onCancel:()=>ie(!1)})]})},Ja=function(){return e.jsx(ta,{})};export{Ja as component};
