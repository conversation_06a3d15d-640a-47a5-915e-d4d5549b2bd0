import{e as a,r as p,j as t}from"./index-Bnt3OGV2.js";import{I as e}from"./item-detail-form-BAdu5tyD.js";import"./form-wT1R35uI.js";import"./exceljs.min-Da2DFgpf.js";import"./pos-api-BwpRFGce.js";import{j as c}from"./customization-dialog-Djqz8P-n.js";import"./user-oq7iQk7S.js";import"./vietqr-api-DAENYiJ_.js";import"./crm-api-Dd9UhSCJ.js";import"./header-stuEr_6l.js";import"./main-Dj7NWzIf.js";import"./search-context-DLufo9i0.js";import"./date-range-picker-CVvofQC0.js";import"./multi-select-DmQbcyz3.js";import"./zod-CNC8A7Cl.js";import"./use-upload-image-Dtz4u4ii.js";import"./images-api-ij8RVLRT.js";import"./use-item-types-Q-0SzpLA.js";import"./useQuery-DSrD7NAp.js";import"./utils-km2FGkQ4.js";import"./useMutation-d67-fNFq.js";import"./query-keys-3lmd-xp6.js";import"./use-item-classes-CsFb8pem.js";import"./use-units-DeWvOXUG.js";import"./use-items-DpA4YqSE.js";import"./item-api-aC51KELB.js";import"./use-removed-items-niCKPB6t.js";import"./use-customizations-DcL8Qx9h.js";import"./use-customization-by-id-BDmYKkLo.js";import"./use-sources-ByVM6ea-.js";import"./sources-api-DQD3aj-a.js";import"./sources-CfiQ7039.js";import"./calendar-CzR6WBaB.js";import"./createLucideIcon-CNa_hh6B.js";import"./index-BT7Z3RDV.js";import"./isSameMonth-C8JQo-AN.js";import"./checkbox-BiVztVsP.js";import"./index-C2T2k_Lh.js";import"./check-apx2eTVC.js";import"./input-CiKEYbig.js";import"./textarea-Da8YG9AB.js";import"./combobox-Dfida1wD.js";import"./command-ByfqjQDn.js";import"./dialog-hQ-PVOWr.js";import"./search-BVQVKwPC.js";import"./popover-C2mvzdeD.js";import"./chevrons-up-down-BYsm4o8o.js";import"./upload-BdXwcNQr.js";import"./collapsible-CfXqqCTe.js";import"./confirm-dialog-D0ceQYlS.js";import"./alert-dialog-BfzPuNaL.js";import"./date-picker-DhOVzRMj.js";import"./calendar-BgsBunwq.js";import"./circle-help-vaGE5apo.js";import"./select-Czd7KcZQ.js";import"./index-Bl1CGAiZ.js";import"./chevron-right-sZt3EK3r.js";import"./use-dialog-state-jYRzWcqs.js";import"./modal-B0J8RkN-.js";import"./separator-CClVRZ9M.js";import"./createReactComponent-BD5R5KSl.js";import"./scroll-area-BeVbW7LP.js";import"./IconChevronRight-BM-o6vT_.js";import"./react-icons.esm-B7rNr9e-.js";import"./badge-u6qfWPSj.js";import"./circle-x-Cw52ZI-1.js";const vt=function(){const i=a({from:"/_authenticated/menu/items/items-in-city/detail"}),[r,s]=p.useState(null),o=i==null?void 0:i.id,{data:m,isLoading:n}=c(o,!!o);return o?(p.useEffect(()=>{m&&s(m.data)},[m]),n||!r?t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"flex items-center justify-center",children:t.jsx("div",{className:"text-lg",children:"Đang tải..."})})}):t.jsx(e,{currentRow:{...r,item_id:"",item_id_barcode:""},isCopyMode:!!r})):t.jsx(e,{})};export{vt as component};
