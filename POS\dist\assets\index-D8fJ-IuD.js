import{h as L,r as c,j as e,B as I,l as G,b as Q,ay as R,a4 as _}from"./index-Bnt3OGV2.js";import{u as X,b as Y,d as Z,a as ee}from"./use-areas-DwZKIlN8.js";import{S as V,a as B,b as U,c as $,d as J,C as P}from"./select-Czd7KcZQ.js";import{H as se}from"./header-stuEr_6l.js";import{M as te}from"./main-Dj7NWzIf.js";import{P as ae}from"./profile-dropdown-BQhzNXaW.js";import{S as re,T as ne}from"./search-BwtQTbOQ.js";import{u as le,e as ce,f as O}from"./index-BUjvK8mU.js";import{C as ie}from"./index-DT8_cgZn.js";import"./pos-api-BwpRFGce.js";import"./date-range-picker-CVvofQC0.js";import"./form-wT1R35uI.js";import{T as oe,a as de,b as A,c as me,d as he,e as E}from"./table-C602nYEy.js";import{T as xe}from"./trash-2-ChT0a0C6.js";import{C as D}from"./checkbox-BiVztVsP.js";import{S as ue}from"./status-badge-BaFhTlYc.js";import{u as pe}from"./use-tables-DZtz_Itl.js";import{C as F,a as K,b as z}from"./collapsible-CfXqqCTe.js";import{I as ge}from"./input-CiKEYbig.js";import{P as W}from"./modal-B0J8RkN-.js";import{C as H}from"./chevron-right-sZt3EK3r.js";import{P as q}from"./plus-CGatRjL7.js";import{I as fe}from"./IconUpload-DsMx8yHq.js";import{U as je}from"./upload-BdXwcNQr.js";import"./useQuery-DSrD7NAp.js";import"./utils-km2FGkQ4.js";import"./useMutation-d67-fNFq.js";import"./images-api-ij8RVLRT.js";import"./query-keys-3lmd-xp6.js";import"./index-Bl1CGAiZ.js";import"./index-BT7Z3RDV.js";import"./index-C2T2k_Lh.js";import"./createLucideIcon-CNa_hh6B.js";import"./check-apx2eTVC.js";import"./separator-CClVRZ9M.js";import"./avatar-D33aZz95.js";import"./dropdown-menu-BjJ0HZAV.js";import"./index-UiaF_xtq.js";import"./search-context-DLufo9i0.js";import"./command-ByfqjQDn.js";import"./calendar-CzR6WBaB.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-hQ-PVOWr.js";import"./search-BVQVKwPC.js";import"./createReactComponent-BD5R5KSl.js";import"./scroll-area-BeVbW7LP.js";import"./IconChevronRight-BM-o6vT_.js";import"./IconSearch-DCdKv7Cy.js";import"./react-icons.esm-B7rNr9e-.js";import"./popover-C2mvzdeD.js";import"./badge-u6qfWPSj.js";function ve(){var C;const n=L(),s=c.useMemo(()=>{try{const o=localStorage.getItem("pos_stores_data");if(o){const y=JSON.parse(o);return Array.isArray(y)?y.filter(S=>S.active===1):[]}return[]}catch(o){return console.error("Error parsing pos_stores_data:",o),[]}},[]),[t,r]=c.useState(((C=s[0])==null?void 0:C.id)||""),[i,d]=c.useState(!1),{data:m=[],isLoading:g,error:x}=X({storeUid:t}),v=c.useMemo(()=>we(t),[t]),u=()=>{n({to:"/setting/area/detail"})},h=()=>{t&&d(!0)},f=()=>{d(!1)},b=()=>{d(!1)};return e.jsxs(e.Fragment,{children:[e.jsx(se,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(re,{}),e.jsx(ne,{}),e.jsx(ae,{})]})}),e.jsxs(te,{children:[e.jsx("div",{className:"mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4"}),e.jsxs("div",{className:"container mx-auto space-y-6 py-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:"Danh sách khu vực"}),e.jsx("div",{className:"flex-1",children:e.jsxs(V,{value:t,onValueChange:r,children:[e.jsx(B,{className:"w-[300px]",children:e.jsx(U,{placeholder:"Chọn cửa hàng"})}),e.jsx($,{children:s.map(o=>e.jsx(J,{value:o.id,children:o.store_name},o.id))})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(I,{variant:"outline",onClick:h,children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),"Thêm khu vực từ file"]}),e.jsxs(I,{onClick:u,children:[e.jsx(q,{className:"mr-2 h-4 w-4"}),"Tạo khu vực mới"]})]})]}),g&&e.jsx("div",{className:"rounded-md border p-8 text-center",children:e.jsx("p",{className:"text-muted-foreground",children:"Đang tải..."})}),x&&e.jsx("div",{className:"rounded-md border p-8 text-center",children:e.jsx("p",{className:"text-muted-foreground",children:"Có lỗi xảy ra khi tải dữ liệu"})}),!g&&!x&&e.jsx(be,{columns:v,data:m,storeUid:t}),!t&&s.length>0&&e.jsx("div",{className:"bg-card rounded-lg border p-8 text-center",children:e.jsx("p",{className:"text-muted-foreground",children:"Vui lòng chọn cửa hàng để xem danh sách khu vực"})}),s.length===0&&e.jsx("div",{className:"bg-card rounded-lg border p-8 text-center",children:e.jsx("p",{className:"text-muted-foreground",children:"Không có cửa hàng nào khả dụng"})}),e.jsx(Te,{open:i,onOpenChange:d,onCancel:f,onSuccess:b})]})]})]})}function be({columns:n,data:s,storeUid:t}){var S;const r=L(),[i,d]=c.useState({}),[m,g]=c.useState(!1),{deleteAreas:x,isDeleting:v}=Y(),u=le({data:s,columns:n,state:{rowSelection:i},enableRowSelection:!0,onRowSelectionChange:d,getCoreRowModel:ce()}),h=u.getFilteredSelectedRowModel().rows,f=h.length,b=()=>{g(!0)},C=()=>{const j=h.map(a=>a.original.id);x({areaIds:j,storeUid:t}),d({}),g(!1)},o=()=>{g(!1)},y=(j,a)=>{const l=a.target;l.closest('input[type="checkbox"]')||l.closest("button")||l.closest('[role="button"]')||r({to:"/setting/area/detail/$areaId",params:{areaId:j.id},search:{store_uid:t}})};return e.jsxs("div",{className:"space-y-4",children:[f>0&&e.jsx("div",{className:"bg-muted/50 flex items-center justify-start rounded-md border p-3",children:e.jsxs(I,{variant:"destructive",size:"sm",onClick:b,disabled:v,className:"h-8",children:[e.jsx(xe,{className:"mr-2 h-4 w-4"}),v?"Đang xóa...":"Xóa khu vực"]})}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(oe,{children:[e.jsx(de,{children:u.getHeaderGroups().map(j=>e.jsx(A,{children:j.headers.map(a=>e.jsx(me,{style:{width:a.getSize()},children:a.isPlaceholder?null:O(a.column.columnDef.header,a.getContext())},a.id))},j.id))}),e.jsx(he,{children:(S=u.getRowModel().rows)!=null&&S.length?u.getRowModel().rows.map(j=>{const a=j.original;return e.jsx(A,{"data-state":j.getIsSelected()&&"selected",className:"cursor-pointer hover:bg-gray-50",onClick:l=>y(a,l),children:j.getVisibleCells().map(l=>e.jsx(E,{style:{width:l.column.getSize()},children:O(l.column.columnDef.cell,l.getContext())},l.id))},j.id)}):e.jsx(A,{children:e.jsx(E,{colSpan:n.length,className:"h-24 text-center",children:e.jsxs("div",{className:"flex flex-col items-center justify-center space-y-2",children:[e.jsx("p",{className:"text-muted-foreground",children:"Không có khu vực nào"}),e.jsx("p",{className:"text-muted-foreground text-sm",children:"Hãy tạo khu vực mới để bắt đầu quản lý"})]})})})})]})}),e.jsx(ie,{open:m,onOpenChange:g,title:"Lưu ý: Xóa khu vực có thể ảnh hưởng đến dữ liệu đang vận hành tại POS",content:`Bạn có muốn xoá ${f} khu vực đã chọn ?`,confirmText:"Xóa",cancelText:"Hủy",onConfirm:C,onCancel:o,isLoading:v})]})}function Ne({open:n,onOpenChange:s,area:t,storeUid:r}){var k,M;const[i,d]=c.useState(""),[m,g]=c.useState([]),[x,v]=c.useState(!0),[u,h]=c.useState(!0),f=G();Q();const{data:b=[],isLoading:C}=pe({skip_limit:!0,brand_uid:((k=f==null?void 0:f.selectedBrand)==null?void 0:k.id)||""});c.useEffect(()=>{if(n&&b.length>0){const p=b.filter(N=>{var w;return(w=t.list_table_id)==null?void 0:w.includes(N.table_id)}).map(N=>N.id);g(p)}},[n,b,t.list_table_id,t.area_name]);const o=c.useMemo(()=>i?b.filter(p=>p.table_name.toLowerCase().includes(i.toLowerCase())):b,[b,i]),{selectedTables:y,remainingTables:S}=c.useMemo(()=>{const p=o.filter(w=>m.includes(w.id)),N=o.filter(w=>!m.includes(w.id));return{selectedTables:p,remainingTables:N}},[o,m]),j=p=>{g(N=>N.includes(p)?N.filter(w=>w!==p):[...N,p])},a=()=>{d(""),s(!1)},l=()=>{a()},T=p=>{var N;return e.jsxs("div",{className:"flex items-center justify-between py-2",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(D,{checked:m.includes(p.id),onCheckedChange:()=>j(p.id)}),e.jsx("span",{className:"text-sm",children:p.table_name})]}),e.jsx("span",{className:"text-sm text-gray-500",children:((N=p.area)==null?void 0:N.area_name)||"Chưa có khu vực"})]},p.id)};return e.jsx(W,{title:`Thêm bàn vào khu vực ${t.area_name}`,open:n,onOpenChange:s,onCancel:a,onConfirm:l,confirmText:"Xác nhận",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",confirmDisabled:m.length===0,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("p",{className:"text-sm text-gray-600",children:["Bàn được chọn sẽ chuyển sang khu vực ",t.area_name]}),e.jsxs("p",{className:"text-sm font-medium",children:["Khu vực ",t.area_name," có ",((M=t.list_table_id)==null?void 0:M.length)||0," bàn"]}),e.jsx(ge,{placeholder:"Tìm kiếm",value:i,onChange:p=>d(p.target.value),className:"w-full"}),C?e.jsx("div",{className:"py-8 text-center text-sm text-gray-500",children:"Đang tải danh sách bàn..."}):e.jsxs("div",{className:"space-y-3",children:[e.jsxs(F,{open:x,onOpenChange:v,children:[e.jsxs(K,{className:"flex w-full items-center justify-between rounded-md border p-3 text-left hover:bg-gray-50",children:[e.jsxs("span",{className:"text-sm font-medium",children:["Đã chọn ",y.length]}),x?e.jsx(P,{className:"h-4 w-4"}):e.jsx(H,{className:"h-4 w-4"})]}),e.jsx(z,{className:"rounded-b-md border-r border-b border-l",children:e.jsx("div",{className:"space-y-1 p-3",children:y.length===0?e.jsx("p",{className:"py-2 text-sm text-gray-500",children:"Chưa chọn bàn nào"}):y.map(T)})})]}),e.jsxs(F,{open:u,onOpenChange:h,children:[e.jsxs(K,{className:"flex w-full items-center justify-between rounded-md border p-3 text-left hover:bg-gray-50",children:[e.jsxs("span",{className:"text-sm font-medium",children:["Còn lại ",S.length]}),u?e.jsx(P,{className:"h-4 w-4"}):e.jsx(H,{className:"h-4 w-4"})]}),e.jsx(z,{className:"rounded-b-md border-r border-b border-l",children:e.jsx("div",{className:"space-y-1 p-3",children:S.length===0?e.jsx("p",{className:"py-2 text-sm text-gray-500",children:"Không có bàn nào"}):S.map(T)})})]})]})]})})}function ye(n){try{const s=localStorage.getItem("pos_stores_data");if(s){const r=JSON.parse(s).find(i=>i.id===n);return(r==null?void 0:r.store_name)||"Không xác định"}return"Không xác định"}catch(s){return console.error("Error parsing pos_stores_data:",s),"Không xác định"}}function Se({area:n}){const{toggleAreaStatus:s,isToggling:t}=Z(),r=()=>{console.log("Toggling area status:",n.area_name,"from",n.active===1?"Active":"Inactive"),s(n)};return e.jsx("div",{className:"flex items-center justify-center gap-2",children:e.jsx("button",{onClick:r,disabled:t,className:"cursor-pointer disabled:cursor-not-allowed disabled:opacity-50",children:e.jsx(ue,{isActive:n.active===1,activeText:"Active",inactiveText:"Deactive"})})})}function Ce({area:n,storeUid:s}){const[t,r]=c.useState(!1),i=()=>{console.log("handleAddTables clicked for area:",n.area_name),r(!0)},d=m=>`${(m==null?void 0:m.length)||0} bàn trong khu vực`;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx("button",{className:"cursor-pointer text-sm text-blue-600 hover:text-blue-800 hover:underline",onClick:i,children:d(n.list_table_id)}),e.jsx(I,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",onClick:i,children:e.jsx(q,{className:"h-4 w-4"})})]}),e.jsx(Ne,{open:t,onOpenChange:r,area:n,storeUid:s})]})}const we=n=>[{id:"select",header:({table:s})=>e.jsx("div",{className:"flex justify-center",children:e.jsx(D,{checked:s.getIsAllPageRowsSelected()||s.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>s.toggleAllPageRowsSelected(!!t),"aria-label":"Select all",className:"translate-y-[2px]"})}),cell:({row:s})=>e.jsx("div",{className:"flex justify-center",children:e.jsx(D,{checked:s.getIsSelected(),onCheckedChange:t=>s.toggleSelected(!!t),"aria-label":"Select row",className:"translate-y-[2px]"})}),enableSorting:!1,enableHiding:!1,size:50},{id:"index",header:()=>e.jsx("div",{className:"text-center",children:"#"}),cell:({row:s})=>e.jsx("div",{className:"w-[50px] text-center font-medium",children:s.index+1}),enableSorting:!1,enableHiding:!1,size:60},{accessorKey:"area_name",header:()=>e.jsx("div",{className:"text-center",children:"Tên khu vực"}),cell:({row:s})=>e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"font-medium",children:s.getValue("area_name")})}),size:200},{accessorKey:"area_id",header:()=>e.jsx("div",{className:"text-center",children:"Mã khu vực"}),cell:({row:s})=>e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"font-mono text-sm",children:s.getValue("area_id")})}),size:150},{id:"applied_stores",header:()=>e.jsx("div",{className:"text-center",children:"Điểm áp dụng"}),cell:({row:s})=>{const t=s.original,r=ye(t.store_uid||"");return e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"text-sm",children:r})})},enableSorting:!1,size:120},{id:"add_tables",header:()=>e.jsx("div",{className:"text-center",children:"Thêm bàn"}),cell:({row:s})=>e.jsx(Ce,{area:s.original,storeUid:n}),enableSorting:!1,size:120},{id:"actions",header:()=>e.jsx("div",{className:"text-center",children:"Thao tác"}),cell:({row:s})=>e.jsx(Se,{area:s.original}),enableSorting:!1,size:100}];function _e(){return{parseExcelFile:async t=>{try{const r=await R(()=>import("./xlsx-DkH2s96g.js"),[]),i=await t.arrayBuffer(),d=r.read(i,{type:"array"}),m=d.SheetNames[0],g=d.Sheets[m],x=r.utils.sheet_to_json(g,{header:1}),v=[];for(let u=1;u<x.length;u++){const h=x[u];if(!h||h.length===0||!h[0])continue;const f={area_name:String(h[0]||"").trim(),description:h[1]?String(h[1]).trim():void 0,sort:void 0,active:1};if(!f.area_name){_.error(`Dòng ${u+1}: Tên khu vực không được để trống`);continue}v.push(f)}if(v.length===0)throw _.error("Không tìm thấy dữ liệu hợp lệ trong file"),new Error("No valid data found");return v}catch(r){throw console.error("Error parsing Excel file:",r),r instanceof Error&&r.message!=="No valid data found"&&_.error("Lỗi khi đọc file Excel. Vui lòng kiểm tra định dạng file."),r}},downloadTemplate:()=>{const t=[["name","description"],["Test 1","Khu vực tầng 2"],["Test 2","Khu vực tầng 3"]];R(()=>import("./xlsx-DkH2s96g.js"),[]).then(r=>{const i=r.utils.book_new(),d=r.utils.aoa_to_sheet(t);r.utils.book_append_sheet(i,d,"Areas Template"),r.writeFile(i,"import_area_template.xlsx"),_.success("Đã tải file mẫu thành công!")})}}}function Te({open:n,onOpenChange:s,onCancel:t,onSuccess:r}){const[i,d]=c.useState(null),[m,g]=c.useState([]),[x,v]=c.useState(!1),u=c.useRef(null),[h,f]=c.useState(""),{parseExcelFile:b}=_e(),C=ee(),o=c.useMemo(()=>{try{const a=localStorage.getItem("pos_stores_data");if(a){const l=JSON.parse(a);return Array.isArray(l)?l.filter(T=>T.active===1):[]}return[]}catch(a){return console.error("Error parsing pos_stores_data:",a),[]}},[]);c.useMemo(()=>{o.length>0&&!h&&f(o[0].id)},[o,h]),c.useMemo(()=>{var a;n||(v(!1),g([]),d(null),f(((a=o[0])==null?void 0:a.id)||""))},[n,o]);const y=()=>{var a;(a=u.current)==null||a.click()},S=async a=>{var T;const l=(T=a.target.files)==null?void 0:T[0];if(l){d(l);try{const k=await b(l);g(k),v(!0),_.success(`Đã phân tích ${k.length} khu vực từ file!`)}catch{}}},j=async()=>{if(x&&h){if(m.length===0){_.error("Không có dữ liệu để lưu");return}try{await C.mutateAsync({storeUid:h,areas:m}),_.success(`Đã tạo thành công ${m.length} khu vực!`),r()}catch{_.error("Lỗi khi tạo khu vực. Vui lòng thử lại.")}}};return e.jsxs(W,{title:"Thêm khu vực từ file",open:n,onOpenChange:s,onCancel:t,onConfirm:j,confirmText:x?"Lưu":"Tiếp tục",cancelText:"Đóng",centerTitle:!0,maxWidth:x?"sm:max-w-4xl":"sm:max-w-[400px]",isLoading:C.isPending,hideButtons:!x,confirmDisabled:x&&!h,children:[e.jsxs("div",{className:"space-y-4",children:[!x&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mb-4 text-sm text-gray-600",children:"Dữ liệu tải lên được áp dụng vào 1 cửa hàng cụ thể."}),e.jsx("div",{className:"mb-4 text-sm text-gray-600",children:"File tải lên có cấu trúc như sau:"}),e.jsx("div",{className:"mb-4 text-center",children:e.jsx("img",{src:"/images/setting/area/add-area.png",alt:"File structure example",className:"mx-auto h-auto max-w-full rounded-lg border"})}),e.jsxs("div",{className:"mb-4 text-sm text-gray-600",children:["Hoặc xem trong"," ",e.jsx("a",{href:"/files/setting/area/import_area_template.xlsx",download:"import_area_template.xlsx",className:"cursor-pointer text-blue-600 underline hover:text-blue-800",children:"file mẫu"}),"."]}),e.jsxs("div",{className:"text-center",children:[e.jsxs(I,{size:"default",variant:"default",onClick:y,className:"mx-auto flex items-center gap-2",children:[e.jsx(fe,{className:"h-4 w-4"}),"Tải file lên"]}),i&&e.jsxs("div",{className:"mt-2 text-xs text-gray-500",children:["File đã chọn: ",i.name]})]})]}),x&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"text-center text-red-500",children:"Chọn cửa hàng áp dụng"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Các khu vực sẽ áp dụng cho cửa hàng được chọn"}),e.jsxs(V,{value:h,onValueChange:f,children:[e.jsx(B,{className:"w-[200px]",children:e.jsx(U,{placeholder:"Chọn cửa hàng"})}),e.jsx($,{children:o.map(a=>e.jsx(J,{value:a.id,children:a.store_name},a.id))})]})]}),e.jsx("div",{className:"max-h-96 overflow-y-auto rounded-lg border",children:e.jsxs("table",{className:"w-full text-sm",children:[e.jsx("thead",{className:"sticky top-0 bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left",children:"Tên khu vực"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Mô tả"})]})}),e.jsx("tbody",{children:m.map((a,l)=>e.jsxs("tr",{className:"border-t",children:[e.jsx("td",{className:"px-4 py-2",children:a.area_name}),e.jsx("td",{className:"px-4 py-2",children:a.description||"-"})]},l))})]})})]})]}),e.jsx("input",{ref:u,type:"file",accept:".xlsx,.xls",onChange:S,className:"hidden"})]})}const ws=function(){return e.jsx(ve,{})};export{ws as component};
