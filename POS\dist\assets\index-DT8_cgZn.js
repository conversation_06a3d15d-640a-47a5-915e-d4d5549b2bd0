import{j as e,B as n}from"./index-Bnt3OGV2.js";import{D as j,a as f,b as h,c as u,e as p}from"./dialog-hQ-PVOWr.js";const g=({open:i,onOpenChange:l,title:s="",content:r="Bạn có muốn xóa",onConfirm:c,onCancel:t,confirmText:x="Xóa",cancelText:o="Hủy",isLoading:a=!1})=>{const d=()=>{t?t():l(!1)},m=()=>{c()};return e.jsx(j,{open:i,onOpenChange:l,children:e.jsxs(f,{className:"sm:max-w-[400px]",children:[s&&e.jsx(h,{children:e.jsx(u,{className:"text-lg font-medium",children:s})}),e.jsx("div",{className:`py-4 ${s?"":"pt-6"}`,children:e.jsx("p",{className:`${s?"text-sm":"text-center text-base"} text-gray-600`,children:r})}),e.jsx(p,{children:e.jsxs("div",{className:"flex w-full justify-between",children:[e.jsx(n,{type:"button",variant:"outline",onClick:d,disabled:a,children:o}),e.jsx(n,{variant:"destructive",onClick:m,disabled:a,children:a?"Đang xử lý...":x})]})})]})})};export{g as C};
