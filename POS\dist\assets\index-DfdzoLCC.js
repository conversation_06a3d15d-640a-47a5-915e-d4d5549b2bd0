import{h as T,r as u,j as e,B as r}from"./index-C21OP4ex.js";import"./pos-api-D5WM5mnz.js";import"./vietqr-api-ruJT0-tj.js";import"./user-CgNoQQSj.js";import"./crm-api-BUMUQ8t4.js";import"./header-DNPEfjkR.js";import"./main-DRnqW_wu.js";import"./search-context-DtMZc3QX.js";import"./date-range-picker-B1pgj5D_.js";import{L as o}from"./form-usWdQ_Nt.js";import{b as L}from"./use-payment-methods-BLNq78Mk.js";import{C as j}from"./checkbox-DUpnJ1Rx.js";import{I as n}from"./input-4sMIt001.js";import{S as M,a as _,b as B,c as G,d as v}from"./select-B8Pw9rS-.js";import{S as q}from"./store-selection-modal-_l8ZTCX7.js";import{X as I}from"./calendar-BiBi2kQF.js";import"./separator-ZoxOB1XH.js";import"./command-BnLmWlRk.js";import"./dialog-DXjwjGKV.js";import"./search-DHRhj6_i.js";import"./createLucideIcon-CL0CQOA1.js";import"./createReactComponent-zh6rKAzG.js";import"./scroll-area-DKiYF9x5.js";import"./index-Bh-UeytL.js";import"./IconChevronRight-Bwbz4HuV.js";import"./chevron-right-BAjIoZMb.js";import"./react-icons.esm-DB-kGUq7.js";import"./popover-BtedB187.js";import"./index-Ct3V_iCU.js";import"./isSameMonth-C8JQo-AN.js";import"./useQuery-BNGphiae.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bh5DVQPI.js";import"./payment-methods-api-B4cpiZ_z.js";import"./query-keys-3lmd-xp6.js";import"./index-DuT2Ibxp.js";import"./check-DcHT8QEO.js";import"./collapsible-B7-SIusD.js";function D(){var h;const i=T(),{createPaymentMethod:f,isCreating:c}=L(),[t,a]=u.useState({name:"",code:"",autoGenerateCode:!0,cardProcessingFee:"0",feeBearer:"customer",requireTransactionCode:!1,selectedStores:[],logoFile:null,logoPreview:""}),[N,m]=u.useState(!1),y=()=>{i({to:"/setting/payment-method"})},C=async()=>{if(!d)return;const s={payment_method_name:t.name,payment_method_id:t.autoGenerateCode?void 0:t.code,payment_fee_extra:parseFloat(t.cardProcessingFee)/100,payment_fee_type:0,stores:t.selectedStores,extra_data:t.requireTransactionCode?{require_traceno:1}:void 0,logoFile:t.logoFile};f(s,{onSuccess:()=>{i({to:"/setting/payment-method"})}})},d=t.name.trim()!==""&&t.selectedStores.length>0,S=()=>{m(!0)},b=s=>{a({...t,selectedStores:s})},w=s=>{var x;const l=(x=s.target.files)==null?void 0:x[0];if(l){const p=new FileReader;p.onload=P=>{var g;const k=(g=P.target)==null?void 0:g.result;a({...t,logoFile:l,logoPreview:k})},p.readAsDataURL(l)}},F=()=>{a({...t,logoFile:null,logoPreview:""})};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(r,{variant:"ghost",size:"sm",onClick:y,className:"flex items-center",children:e.jsx(I,{className:"h-4 w-4"})}),e.jsx(r,{type:"button",disabled:c||!d,className:"min-w-[100px]",onClick:C,children:c?"Đang tạo...":"Lưu"})]}),e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:"Tạo phương thức thanh toán mới"})})]}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Thông tin chi tiết"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(o,{htmlFor:"payment-method-name",className:"min-w-[200px] text-sm font-medium",children:["Tên phương thức ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(n,{id:"payment-method-name",value:t.name,onChange:s=>a({...t,name:s.target.value}),placeholder:"Nhập tên phương thức thanh toán",className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(o,{className:"min-w-[200px] text-sm font-medium",children:["Cửa hàng áp dụng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(r,{variant:"outline",onClick:S,className:"flex-1 justify-start",children:t.selectedStores.length>0?`${t.selectedStores.length} điểm`:"0 điểm"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(o,{htmlFor:"payment-method-code",className:"min-w-[200px] text-sm font-medium",children:"Mã PTTT"}),e.jsx(n,{id:"payment-method-code",value:t.code,onChange:s=>a({...t,code:s.target.value}),placeholder:"Nếu để trống, hệ thống sẽ tự động tạo một mã PTTT",disabled:t.autoGenerateCode,className:"flex-1"}),e.jsx(j,{id:"auto-generate-code",checked:t.autoGenerateCode,onCheckedChange:s=>a({...t,autoGenerateCode:s,code:s?"":t.code})})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(o,{htmlFor:"card-processing-fee",className:"min-w-[200px] text-sm font-medium",children:"Phí cà thẻ"}),e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(n,{id:"card-processing-fee",type:"number",value:t.cardProcessingFee,onChange:s=>a({...t,cardProcessingFee:s.target.value}),placeholder:"0",className:"pr-8",min:"0",max:"100",step:"0.01"}),e.jsx("span",{className:"absolute top-1/2 right-3 -translate-y-1/2 text-sm text-gray-500",children:"%"})]})})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(o,{className:"min-w-[200px] text-sm font-medium",children:"Bên chịu phí cà thẻ"}),e.jsxs(M,{value:t.feeBearer,onValueChange:s=>a({...t,feeBearer:s}),children:[e.jsx(_,{className:"flex-1",children:e.jsx(B,{})}),e.jsxs(G,{children:[e.jsx(v,{value:"customer",children:"Khách hàng"}),e.jsx(v,{value:"restaurant",children:"Nhà hàng"})]})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(o,{className:"min-w-[200px] text-sm font-medium",children:"Yêu cầu nhập mã giao dịch khi thanh toán"}),e.jsx("div",{className:"flex-1",children:e.jsx(j,{id:"require-transaction-code",checked:t.requireTransactionCode,onCheckedChange:s=>a({...t,requireTransactionCode:s})})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Logo"}),e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx(o,{className:"min-w-[200px] pt-2 text-sm font-medium",children:"Logo tải lên"}),e.jsx("div",{className:"flex-1",children:t.logoPreview?e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"relative inline-block",children:[e.jsx("img",{src:t.logoPreview,alt:"Logo preview",className:"h-24 w-24 rounded-md border object-cover"}),e.jsx(r,{type:"button",variant:"destructive",size:"sm",className:"absolute -top-2 -right-2 h-6 w-6 rounded-full p-0",onClick:F,children:"×"})]}),e.jsx("p",{className:"text-muted-foreground text-xs",children:(h=t.logoFile)==null?void 0:h.name})]}):e.jsx("div",{className:"rounded-md border-2 border-dashed border-gray-300 p-6 text-center transition-colors hover:border-gray-400",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"mx-auto h-12 w-12 text-gray-400",children:e.jsx("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})}),e.jsx("div",{children:e.jsxs("label",{htmlFor:"payment-method-logo",className:"cursor-pointer",children:[e.jsx("span",{className:"text-sm font-medium text-blue-600 hover:text-blue-500",children:"Tải ảnh lên"}),e.jsx(n,{id:"payment-method-logo",type:"file",accept:"image/*",onChange:w,className:"hidden"})]})}),e.jsx("p",{className:"text-xs text-gray-500",children:"PNG, JPG, GIF up to 10MB"})]})})})]})]})]})})}),e.jsx(q,{open:N,onOpenChange:m,selectedStoreIds:t.selectedStores,onStoreSelectionChange:b})]})}const Ce=function(){return e.jsx(D,{})};export{Ce as component};
