import{a3 as G,a4 as k,u as xe,r as u,j as e,R as ue,B as v,l as ge,b as je}from"./index-C21OP4ex.js";import{H as Ue}from"./header-DNPEfjkR.js";import{M as He}from"./main-DRnqW_wu.js";import{P as Ve}from"./profile-dropdown-DlMxjxHH.js";import{S as qe,T as ze}from"./search-5KGATEvM.js";import{C as B}from"./checkbox-DUpnJ1Rx.js";import{D as Xe}from"./data-table-column-header-7_8s6zRv.js";import{u as Ke}from"./use-dialog-state-BYP8UC3r.js";import{T as fe}from"./trash-2-C_5rhUMO.js";import"./pos-api-D5WM5mnz.js";import{u as Qe}from"./use-printer-positions-data-DnTjyrKp.js";import{C as ye}from"./confirm-dialog-AXI5ANMc.js";import{u as J}from"./useMutation-Bh5DVQPI.js";import{p as Q}from"./printer-position-api-Q51LgdFK.js";import{Q as $}from"./query-keys-3lmd-xp6.js";import{u as $e}from"./use-stores-BQdEFBhG.js";import{u as Ye}from"./use-item-types-DA0U4OWS.js";import"./vietqr-api-ruJT0-tj.js";import{u as We}from"./use-items-zU7JIOkv.js";import{u as Ge}from"./use-removed-items-Ck-YNHyo.js";import{u as Je}from"./use-sources-CSlkwBP-.js";import{u as Ze}from"./useQuery-BNGphiae.js";import"./user-CgNoQQSj.js";import"./crm-api-BUMUQ8t4.js";import{I as z}from"./input-4sMIt001.js";import{L as I}from"./form-usWdQ_Nt.js";import{C as Ne,f as ve,S as ie,a as ne,b as ae,c as re,d as le}from"./select-B8Pw9rS-.js";import{D as es,a as ss,e as ts}from"./dialog-DXjwjGKV.js";import{S as is}from"./scroll-area-DKiYF9x5.js";import{X as Ce}from"./calendar-BiBi2kQF.js";import{S as oe}from"./search-DHRhj6_i.js";import{S as f}from"./skeleton-xUyFo20h.js";import{T as be,a as Se,b as X,c as q,d as we,e as A}from"./table-BIu4Pah2.js";import{u as ns,g as as,a as rs,b as ls,c as os,d as cs,e as ds,f as ce}from"./index-B_FCwlUM.js";import{D as ms}from"./data-table-pagination-BKENav2P.js";import{P as ps}from"./plus-C1IEs-Ov.js";import{D as hs}from"./data-table-view-options-Cfbuac3T.js";import"./separator-ZoxOB1XH.js";import"./date-range-picker-B1pgj5D_.js";import"./createLucideIcon-CL0CQOA1.js";import"./chevron-right-BAjIoZMb.js";import"./react-icons.esm-DB-kGUq7.js";import"./popover-BtedB187.js";import"./index-Ct3V_iCU.js";import"./isSameMonth-C8JQo-AN.js";import"./avatar-C98m2_SM.js";import"./dropdown-menu-DINyPmco.js";import"./index-Bh-UeytL.js";import"./index-UJ-79IIJ.js";import"./check-DcHT8QEO.js";import"./search-context-DtMZc3QX.js";import"./command-BnLmWlRk.js";import"./createReactComponent-zh6rKAzG.js";import"./IconChevronRight-Bwbz4HuV.js";import"./IconSearch-CyZD7dtp.js";import"./index-DuT2Ibxp.js";import"./alert-dialog-CzdUByb-.js";import"./utils-km2FGkQ4.js";import"./stores-api-BIve2jSO.js";import"./item-api-C8PXkgMG.js";import"./sources-api-Cb3TooI2.js";import"./sources-CfiQ7039.js";const xs=()=>{const s=G();return J({mutationFn:t=>Q.createPrinterPosition(t),onSuccess:t=>{s.invalidateQueries({queryKey:[$.PRINTER_POSITIONS]}),k.success(t.message||"Tạo vị trí máy in thành công")},onError:t=>{k.error(t.message||"Có lỗi xảy ra khi tạo vị trí máy in")}})},us=()=>{const s=G();return J({mutationFn:t=>Q.updatePrinterPosition(t),onSuccess:t=>{s.invalidateQueries({queryKey:[$.PRINTER_POSITIONS]}),k.success(t.message||"Cập nhật vị trí máy in thành công")},onError:t=>{console.error("Error updating printer position:",t),k.error(t.message||"Có lỗi xảy ra khi cập nhật vị trí máy in")}})},Pe=()=>{const s=G(),{company:t,brands:i}=xe(r=>r.auth),n=i==null?void 0:i[0];return J({mutationFn:r=>{if(!(t!=null&&t.id)||!(n!=null&&n.id))throw new Error("Missing company or brand information");const o={company_uid:t.id,brand_uid:n.id,id:Array.isArray(r)?r:[r]};return Q.deletePrinterPosition(o)},onSuccess:r=>{s.invalidateQueries({queryKey:[$.PRINTER_POSITIONS]}),k.success(r.message||"Xóa vị trí máy in thành công")},onError:r=>{k.error(r.message||"Có lỗi xảy ra khi xóa vị trí máy in")}})},gs=(s={})=>{const{data:t,isLoading:i,error:n,refetch:r}=Qe({params:s.params,enabled:s.enabled});return{data:u.useMemo(()=>!t||!Array.isArray(t)?[]:t.map(a=>({id:a.id,printerPositionId:a.printer_position_id,printerPositionName:a.printer_position_name,listItemTypeId:a.list_item_type_id||"",listItemId:a.list_item_id||"",storeUid:a.store_uid??null,areaIds:a.area_ids||"",sources:a.sources||"",applyWithStore:a.apply_with_store||0,sort:a.sort||0,brandUid:a.brand_uid||"",companyUid:a.company_uid||"",revision:a.revision||0,isActive:!a.deleted,createdAt:new Date(a.created_at),updatedAt:new Date(a.updated_at),createdBy:a.created_by||"",updatedBy:a.updated_by||"",deleted:!!a.deleted,deletedBy:a.deleted_by||null,deletedAt:a.deleted_at?new Date(a.deleted_at):null,originalData:a})),[t]),isLoading:i,error:n,refetch:r}},js=(s={})=>{const{id:t,enabled:i=!0}=s,{company:n,brands:r}=xe(m=>m.auth),o=r==null?void 0:r[0],a={company_uid:(n==null?void 0:n.id)||"",brand_uid:(o==null?void 0:o.id)||"",id:t||""},c=!!(n!=null&&n.id&&(o!=null&&o.id)&&t);return Ze({queryKey:[$.PRINTER_POSITIONS,"detail",t],queryFn:async()=>(await Q.getPrinterPositionDetail(a)).data,enabled:i&&c,staleTime:5*60*1e3,gcTime:10*60*1e3})},Te=ue.createContext(null);function fs({children:s}){const[t,i]=Ke(null),[n,r]=u.useState(null),[o,a]=u.useState([]);return e.jsx(Te,{value:{open:t,setOpen:i,currentRow:n,setCurrentRow:r,selectedRows:o,setSelectedRows:a},children:s})}const O=()=>{const s=ue.useContext(Te);if(!s)throw new Error("usePrinterPositionInBrand has to be used within <PrinterPositionInBrandContext>");return s};function ys({row:s}){const t=s.original,{setOpen:i,setCurrentRow:n}=O(),r=()=>{n(t),i("delete")};return e.jsxs(v,{variant:"ghost",size:"sm",onClick:r,className:"h-8 w-8 p-0 text-destructive hover:text-destructive",children:[e.jsx("span",{className:"sr-only",children:"Xóa"}),e.jsx(fe,{className:"h-4 w-4"})]})}const Ns=[{id:"select",header:({table:s})=>e.jsx(B,{checked:s.getIsAllPageRowsSelected()||s.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>s.toggleAllPageRowsSelected(!!t),"aria-label":"Select all",className:"translate-y-[2px]"}),cell:({row:s})=>e.jsx(B,{checked:s.getIsSelected(),onCheckedChange:t=>s.toggleSelected(!!t),"aria-label":"Select row",className:"translate-y-[2px]"}),enableSorting:!1,enableHiding:!1,size:50},{id:"index",header:"#",cell:({row:s})=>e.jsx("div",{className:"w-[50px]",children:s.index+1}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"printerPositionName",header:({column:s})=>e.jsx(Xe,{column:s,title:"Tên vị trí máy in"}),cell:({row:s})=>e.jsx("div",{className:"text-sm font-medium",children:s.getValue("printerPositionName")}),enableSorting:!1,enableHiding:!1},{id:"actions",header:"",cell:({row:s})=>e.jsx(ys,{row:s}),enableSorting:!1,enableHiding:!1,size:50}];function vs(){const{setOpen:s}=O();return e.jsx("div",{className:"flex items-center space-x-2",children:e.jsxs(v,{size:"sm",className:"h-8",onClick:()=>s("create"),children:[e.jsx(ps,{className:"mr-2 h-4 w-4"}),"Tạo vị trí máy in"]})})}function Cs(){const{open:s,setOpen:t,selectedRows:i,setSelectedRows:n}=O(),r=Pe(),o=s==="bulk-delete",a=()=>{t(null),n([])},c=async()=>{if(i.length!==0)try{const x=i.map(g=>g.id);await r.mutateAsync(x),a()}catch{}},m=e.jsxs(e.Fragment,{children:["Bạn có chắc chắn muốn xóa ",e.jsx("strong",{children:i.length})," vị trí máy in đã chọn không?",e.jsx("br",{}),e.jsx("br",{}),"Danh sách sẽ bị xóa:",e.jsxs("ul",{className:"mt-2 list-inside list-disc space-y-1",children:[i.slice(0,5).map(x=>e.jsx("li",{className:"text-sm",children:x.printerPositionName},x.id)),i.length>5&&e.jsxs("li",{className:"text-muted-foreground text-sm",children:["... và ",i.length-5," vị trí khác"]})]}),e.jsx("br",{}),"Hành động này không thể hoàn tác."]});return e.jsx(ye,{destructive:!0,open:o,onOpenChange:a,handleConfirm:c,title:"Xác nhận xóa",desc:m,confirmText:`Xóa ${i.length} vị trí`,className:"max-w-md"})}function bs({table:s}){var c;const{setOpen:t,setSelectedRows:i}=O(),n=s.getState().columnFilters.length>0,r=s.getFilteredSelectedRowModel().rows,o=r.length>0,a=()=>{const m=r.map(x=>x.original);i(m),t("bulk-delete")};return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[e.jsx(z,{placeholder:"Tìm kiếm vị trí máy in...",value:((c=s.getColumn("printerPositionName"))==null?void 0:c.getFilterValue())??"",onChange:m=>{var x;return(x=s.getColumn("printerPositionName"))==null?void 0:x.setFilterValue(m.target.value)},className:"h-8 w-[150px] lg:w-[250px]"}),n&&e.jsxs(v,{variant:"ghost",onClick:()=>s.resetColumnFilters(),className:"h-8 px-2 lg:px-3",children:["Reset",e.jsx(Ce,{className:"ml-2 h-4 w-4"})]}),o&&e.jsxs("div",{className:"flex items-center space-x-2 ml-4",children:[e.jsxs("span",{className:"text-sm text-muted-foreground",children:[r.length," hàng được chọn"]}),e.jsx(v,{variant:"ghost",size:"sm",onClick:()=>s.resetRowSelection(),className:"h-8",children:"Hủy chọn"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[o&&e.jsxs(v,{variant:"destructive",size:"sm",onClick:a,className:"h-8",children:[e.jsx(fe,{className:"mr-2 h-4 w-4"}),"Xóa (",r.length,")"]}),e.jsx(hs,{table:s})]})]})}function Ss({categories:s,selectedCategories:t,onToggleCategory:i}){const[n,r]=u.useState(!1),o=s.filter(c=>t.includes(c.id)),a=s.filter(c=>!t.includes(c.id));return e.jsx("div",{className:"space-y-2",children:e.jsxs("div",{className:"rounded-md border",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between border-b bg-green-50 p-3 text-sm text-green-600 transition-colors hover:bg-green-100",onClick:()=>r(!n),children:[e.jsxs("span",{children:["✓ Đã chọn ",t.length]}),o.length>0&&(n?e.jsx(Ne,{className:"h-4 w-4"}):e.jsx(ve,{className:"h-4 w-4"}))]}),o.length>0&&!n&&e.jsx("div",{className:"border-b",children:e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"max-h-48 space-y-2 overflow-y-auto",children:o.map(c=>e.jsx(de,{category:c,isSelected:!0,onToggle:i},c.id))})})}),a.length>0&&e.jsxs("div",{children:[o.length>0&&e.jsx("div",{className:"border-b bg-gray-50 p-3 text-sm text-gray-600",children:e.jsxs("span",{children:["Còn lại ",a.length]})}),e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"max-h-48 space-y-2 overflow-y-auto",children:a.map(c=>e.jsx(de,{category:c,isSelected:!1,onToggle:i},c.id))})})]})]})})}function de({category:s,isSelected:t,onToggle:i}){return e.jsxs("div",{className:"flex items-center space-x-3 rounded border-b border-gray-100 py-2 last:border-b-0 hover:bg-gray-50",children:[e.jsx(B,{id:s.id,checked:t,onCheckedChange:n=>i(s.id,!!n),className:"h-4 w-4"}),e.jsx(I,{htmlFor:s.id,className:"flex-1 cursor-pointer text-sm leading-relaxed",children:s.item_type_name})]})}function ws({items:s,isLoading:t=!1,cities:i=[],stores:n=[],mode:r,onChangeMode:o,selectedCityUid:a,onChangeCity:c,selectedStoreUid:m,onChangeStore:x,selectedItems:g=[],onItemsChange:w}){const[C,D]=u.useState(!1),y=s.filter(l=>g.includes(l.id)),P=s.filter(l=>!g.includes(l.id)),p=(l,j)=>{w&&w(j?[...g,l]:g.filter(E=>E!==l))};return t?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"}),e.jsx("p",{className:"mt-2 text-gray-600",children:"Đang tải dữ liệu món ăn..."})]})}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(v,{variant:r==="city"?"default":"outline",size:"sm",onClick:()=>o("city"),className:"h-10",children:"Món tại thành phố"}),e.jsx(v,{variant:r==="store"?"default":"outline",size:"sm",onClick:()=>o("store"),className:"h-10",children:"Món tại cửa hàng"}),e.jsx("div",{className:"ml-auto",children:r==="store"?e.jsxs(ie,{value:m,onValueChange:l=>x==null?void 0:x(l),children:[e.jsx(ne,{className:"h-10 w-56",children:e.jsx(ae,{placeholder:"Chọn cửa hàng"})}),e.jsx(re,{children:n.map(l=>e.jsx(le,{value:l.id,children:l.store_name||l.name},l.id))})]}):e.jsxs(ie,{value:a,onValueChange:l=>c==null?void 0:c(l),children:[e.jsx(ne,{className:"h-10 w-40",children:e.jsx(ae,{placeholder:"Chọn thành phố"})}),e.jsx(re,{children:i.map(l=>e.jsx(le,{value:l.id,children:l.city_name},l.id))})]})})]}),e.jsxs("div",{className:"rounded-md border",children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between border-b bg-green-50 p-3 text-sm text-green-600 transition-colors hover:bg-green-100",onClick:()=>D(!C),children:[e.jsxs("span",{children:["✓ Đã chọn ",g.length]}),y.length>0&&(C?e.jsx(Ne,{className:"h-4 w-4"}):e.jsx(ve,{className:"h-4 w-4"}))]}),y.length>0&&!C&&e.jsx("div",{className:"border-b",children:e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"max-h-48 space-y-2 overflow-y-auto",children:y.map(l=>e.jsx(me,{item:l,isSelected:!0,onToggle:p},l.id))})})}),P.length>0&&e.jsxs("div",{children:[y.length>0&&e.jsx("div",{className:"border-b bg-gray-50 p-3 text-sm text-gray-600",children:e.jsxs("span",{children:["Còn lại ",P.length]})}),e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"max-h-48 space-y-2 overflow-y-auto",children:P.map(l=>e.jsx(me,{item:l,isSelected:!1,onToggle:p},l.id))})})]})]})]})}function me({item:s,isSelected:t,onToggle:i}){return e.jsxs("div",{className:"flex items-center space-x-3 rounded border-b border-gray-100 py-2 last:border-b-0 hover:bg-gray-50",children:[e.jsx(B,{id:s.id,checked:t,onCheckedChange:n=>i(s.id,!!n),className:"h-4 w-4"}),e.jsxs(I,{htmlFor:s.id,className:"flex-1 cursor-pointer text-sm leading-relaxed",children:[s.item_name," - ",s.item_id]})]})}function Ps({open:s,onOpenChange:t,orderSources:i,selectedOrderSources:n,onConfirm:r,onCancel:o}){const[a,c]=u.useState([]),[m,x]=u.useState(""),g=i.filter((p,l,j)=>l===j.findIndex(E=>E.sourceId===p.sourceId));u.useEffect(()=>{s&&c([...n])},[s,n]);const w=g.filter(p=>a.includes(p.sourceId)&&p.sourceName.toLowerCase().includes(m.toLowerCase())),C=g.filter(p=>!a.includes(p.sourceId)&&p.sourceName.toLowerCase().includes(m.toLowerCase())),D=()=>{r(a),x("")},y=()=>{c([]),x(""),o()},P=(p,l)=>{c(l?[...a,p]:a.filter(j=>j!==p))};return e.jsx(es,{open:s,onOpenChange:t,children:e.jsxs(ss,{className:"max-h-[90vh] w-[800px] max-w-4xl lg:max-w-4xl",children:[e.jsxs("div",{className:"space-y-6 p-2",children:[e.jsx(Ts,{value:m,onChange:x}),e.jsx(_s,{selectedSources:w,unselectedSources:C,selectedIds:a,onToggle:P})]}),e.jsxs(ts,{children:[e.jsx(v,{variant:"outline",onClick:y,children:"Hủy"}),e.jsx(v,{onClick:D,children:"Xong"})]})]})})}function Ts({value:s,onChange:t}){return e.jsx(z,{placeholder:"Tìm kiếm",value:s,onChange:i=>t(i.target.value)})}function _s({selectedSources:s,unselectedSources:t,onToggle:i}){return e.jsx(is,{className:"h-[60vh] w-full",children:e.jsxs("div",{className:"space-y-4",children:[s.length>0&&e.jsxs("div",{children:[e.jsx("div",{className:"mb-3 flex items-center gap-2 rounded bg-green-50 p-3 text-base font-medium text-green-600",children:e.jsxs("span",{children:["✓ Đã chọn ",s.length]})}),e.jsx("div",{className:"space-y-3",children:s.map(n=>e.jsx(pe,{source:n,isSelected:!0,onToggle:i},n.sourceId))})]}),t.length>0&&e.jsxs("div",{children:[e.jsx("div",{className:"mb-3 rounded bg-gray-50 p-3 text-base font-medium text-gray-600",children:e.jsxs("span",{children:["Còn lại ",t.length]})}),e.jsx("div",{className:"space-y-3",children:t.map(n=>e.jsx(pe,{source:n,isSelected:!1,onToggle:i},n.sourceId))})]})]})})}function pe({source:s,isSelected:t,onToggle:i}){return e.jsxs("div",{className:"flex items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(B,{id:s.sourceId,checked:t,onCheckedChange:n=>i(s.sourceId,!!n),className:"h-5 w-5"}),e.jsx(I,{htmlFor:s.sourceId,className:"flex-1 cursor-pointer text-base",children:s.sourceName})]})}function he({open:s,onOpenChange:t,currentRow:i}){const{selectedBrand:n}=ge(),{companyUid:r}=je(),o=!!i,a=xs(),c=us(),{data:m,isLoading:x}=js({id:i==null?void 0:i.id,enabled:o&&!!(i!=null&&i.id)}),[g,w]=u.useState(""),[C,D]=u.useState(""),[y,P]=u.useState("city"),{data:p,isLoading:l}=Je(),{data:j,isLoading:E}=Ye(),{data:K=[]}=Ge(),{data:_e=[]}=$e(),Ie=()=>y==="city"?{city_uid:g||void 0,skip_limit:!0,active:1}:{list_store_uid:C||void 0,skip_limit:!0,active:1},{data:R,isLoading:De}=We({params:Ie(),enabled:y==="city"?!!g:!!C}),[ke,M]=u.useState(!1),[Y,Z]=u.useState(""),Oe=(j||[]).filter(h=>h.item_type_name.toLowerCase().includes(Y.toLowerCase())),[d,b]=u.useState({printerPositionName:"",applyToAllCategories:!1,selectedCategories:[],selectedItems:[],applicationType:"category",selectedOrderSources:[]});u.useEffect(()=>{s&&!i&&b({printerPositionName:"",applyToAllCategories:!1,selectedCategories:[],selectedItems:[],applicationType:"category",selectedOrderSources:[]})},[s,i]),u.useEffect(()=>{if(s&&o&&m&&j){const F=(m.list_item_type_id?m.list_item_type_id.split(",").filter(Boolean):[]).map(N=>{const U=j.find(S=>S.id===N);if(U)return U.id;const H=j.find(S=>S.item_type_id===N);if(H)return H.id;const V=j.find(S=>S.item_type_name.toLowerCase()===N.toLowerCase());return V?V.id:N}).filter(Boolean),T=m.sources?m.sources.split(",").filter(Boolean):[],L=(j||[]).filter(N=>N.item_type_id!=="ITEM_TYPE_OTHER").map(N=>N.id),W=L.length>0&&L.every(N=>F.includes(N)),te=(m.list_item_id?m.list_item_id.split(",").filter(Boolean):[]).map(N=>{if(!R)return N;const U=R.find(S=>S.id===N);if(U)return U.id;const H=R.find(S=>S.item_id===N);if(H)return H.id;const V=R.find(S=>S.item_name.toLowerCase()===N.toLowerCase());return V?V.id:N}).filter(Boolean),Le=te.length>0?"item":"category";b({printerPositionName:m.printer_position_name||"",applyToAllCategories:W,selectedCategories:F,selectedItems:te,applicationType:Le,selectedOrderSources:T})}},[s,o,m,j,R]);const ee=()=>{t(!1),b({printerPositionName:"",applyToAllCategories:!1,selectedCategories:[],selectedItems:[],applicationType:"category",selectedOrderSources:[]}),M(!1)};u.useEffect(()=>{!g&&K.length>0&&w(K[0].id)},[K,g]);const se=d.printerPositionName.trim()!=="",Re=async()=>{if(se){if(!r||!(n!=null&&n.id)){k.error("Thiếu thông tin công ty hoặc thương hiệu");return}try{const h={printer_position_name:d.printerPositionName,company_uid:r,brand_uid:n.id,printer_position_id:`POSITION_PRINTER-${Math.random().toString(36).substr(2,4).toUpperCase()}`,list_item_type_id:d.applicationType==="category"?d.selectedCategories.join(","):"",list_item_id:d.applicationType==="item"?d.selectedItems.join(","):"",sources:d.selectedOrderSources.join(",")};o&&i?await c.mutateAsync({id:i.id,...h}):await a.mutateAsync(h),ee()}catch{}}},Fe=h=>{if(h){const F=(j||[]).filter(T=>T.item_type_id!=="ITEM_TYPE_OTHER").map(T=>T.id);b({...d,applyToAllCategories:h,selectedCategories:F})}else b({...d,applyToAllCategories:h,selectedCategories:[]})},Ae=()=>{M(!0)},Be=h=>{b({...d,selectedOrderSources:h}),M(!1)},Ee=()=>{M(!1)},Me=(h,F)=>{const T=F?[...d.selectedCategories,h]:d.selectedCategories.filter(_=>_!==h),L=(j||[]).filter(_=>_.item_type_id!=="ITEM_TYPE_OTHER").map(_=>_.id),W=L.length>0&&L.every(_=>T.includes(_));b({...d,selectedCategories:T,applyToAllCategories:W})};return s?l||E||o&&x?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"mx-auto max-w-4xl rounded-lg bg-white p-6 shadow-lg",children:e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"}),e.jsx("p",{className:"mt-2 text-gray-600",children:"Đang tải dữ liệu..."})]})})})}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(v,{variant:"ghost",size:"sm",onClick:ee,className:"flex items-center",children:e.jsx(Ce,{className:"h-4 w-4"})}),e.jsx(v,{type:"button",disabled:!se||a.isPending||c.isPending,onClick:Re,children:a.isPending||c.isPending?"Đang lưu...":"Lưu"})]}),e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:o?"Chỉnh sửa vị trí máy in":"Tạo vị trí máy in"})})]}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"rounded-lg border bg-white p-6 shadow-sm",children:e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(I,{htmlFor:"printer-name",className:"min-w-[200px] text-sm font-medium",children:["Nhập tên vị trí máy in ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(z,{id:"printer-name",value:d.printerPositionName,onChange:h=>b({...d,printerPositionName:h.target.value}),placeholder:"Tên vị trí máy in",className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(I,{className:"min-w-[200px] text-sm font-medium",children:"Áp dụng ngành đơn"}),e.jsx(v,{type:"button",variant:"outline",onClick:Ae,className:"border-blue-200 text-sm text-blue-600 hover:bg-blue-50",children:d.selectedOrderSources.length>0?`${d.selectedOrderSources.length} nguồn đơn được áp dụng`:"0 nguồn đơn được áp dụng"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(I,{className:"text-sm font-medium",children:"Các nhóm món được dùng tại vị trí"}),d.applicationType==="category"&&e.jsxs("div",{className:"flex items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex flex-shrink-0 items-center space-x-2",children:[e.jsx(B,{id:"apply-all",checked:d.applyToAllCategories,onCheckedChange:Fe}),e.jsx(I,{htmlFor:"apply-all",className:"text-sm whitespace-nowrap text-blue-600",children:"Áp dụng cho tất cả nhóm món"})]}),e.jsx("div",{className:"max-w-full flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(oe,{className:"absolute top-1/2 left-2 h-3 w-3 -translate-y-1/2 text-gray-400"}),e.jsx(z,{placeholder:"Tìm kiếm nhóm món...",value:Y,onChange:h=>Z(h.target.value),className:"h-8 w-full pl-7 text-sm"})]})})]}),d.applicationType==="item"&&e.jsx("div",{className:"flex items-center justify-between gap-4",children:e.jsx("div",{className:"max-w-full flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(oe,{className:"absolute top-1/2 left-2 h-3 w-3 -translate-y-1/2 text-gray-400"}),e.jsx(z,{placeholder:"Tìm kiếm",value:Y,onChange:h=>Z(h.target.value),className:"h-8 w-full pl-7 text-sm"})]})})}),e.jsxs("div",{className:"flex items-center gap-4 border-y py-4",children:[e.jsx(I,{className:"min-w-[200px] text-sm font-medium",children:"Áp dụng cho"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{type:"button",variant:d.applicationType==="category"?"default":"outline",size:"sm",onClick:()=>b({...d,applicationType:"category"}),children:"Nhóm"}),e.jsx(v,{type:"button",variant:d.applicationType==="item"?"default":"outline",size:"sm",onClick:()=>b({...d,applicationType:"item"}),children:"Món ăn"})]})]}),d.applicationType==="category"?e.jsx(Ss,{categories:Oe,selectedCategories:d.selectedCategories,onToggleCategory:Me}):e.jsx(ws,{items:R||[],isLoading:De,cities:K,selectedCityUid:g,onChangeCity:w,stores:_e,selectedStoreUid:C,onChangeStore:D,mode:y,onChangeMode:P,selectedItems:d.selectedItems,onItemsChange:h=>b({...d,selectedItems:h})})]})]})})})}),e.jsx(Ps,{open:ke,onOpenChange:M,orderSources:p||[],selectedOrderSources:d.selectedOrderSources,onConfirm:Be,onCancel:Ee})]}):null}function Is(){const{open:s,setOpen:t,currentRow:i,setCurrentRow:n}=O(),r=Pe();return e.jsxs(e.Fragment,{children:[e.jsx(he,{open:s==="create",onOpenChange:()=>t(null)},"printer-position-create"),i&&e.jsxs(e.Fragment,{children:[e.jsx(he,{open:s==="update",onOpenChange:()=>{t(null),setTimeout(()=>{n(null)},500)},currentRow:i},`printer-position-update-${i.id}`),e.jsx(ye,{destructive:!0,open:s==="delete",onOpenChange:()=>{t(null),setTimeout(()=>{n(null)},500)},handleConfirm:async()=>{if(i)try{await r.mutateAsync(i.id),t(null),setTimeout(()=>{n(null)},500)}catch{}},className:"max-w-md",title:`Bạn có muốn xoá vị trí máy in "${i.printerPositionName}"?`,desc:e.jsx(e.Fragment,{children:"Hành động không thể hoàn tác."}),confirmText:"Xoá"},"printer-position-delete")]}),e.jsx(Cs,{})]})}function Ds(){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[e.jsx(f,{className:"h-8 w-[250px]"}),e.jsx(f,{className:"h-8 w-[180px]"})]}),e.jsx(f,{className:"h-8 w-[100px]"})]}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(be,{children:[e.jsx(Se,{children:e.jsxs(X,{children:[e.jsx(q,{className:"w-[50px]",children:e.jsx(f,{className:"h-4 w-4"})}),e.jsx(q,{className:"w-[50px]",children:e.jsx(f,{className:"h-4 w-6"})}),e.jsx(q,{children:e.jsx(f,{className:"h-4 w-32"})}),e.jsx(q,{className:"w-[50px]"})]})}),e.jsx(we,{children:Array.from({length:8}).map((s,t)=>e.jsxs(X,{children:[e.jsx(A,{children:e.jsx(f,{className:"h-4 w-4"})}),e.jsx(A,{children:e.jsx(f,{className:"h-4 w-6"})}),e.jsx(A,{children:e.jsx(f,{className:"h-4 w-40"})}),e.jsx(A,{children:e.jsx(f,{className:"h-8 w-8"})})]},t))})]})}),e.jsxs("div",{className:"flex items-center justify-between px-2",children:[e.jsx(f,{className:"h-4 w-32"}),e.jsxs("div",{className:"flex items-center space-x-6 lg:space-x-8",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(f,{className:"h-4 w-24"}),e.jsx(f,{className:"h-8 w-16"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(f,{className:"h-8 w-8"}),e.jsx(f,{className:"h-8 w-8"}),e.jsx(f,{className:"h-8 w-8"}),e.jsx(f,{className:"h-8 w-8"})]})]})]})]})}function ks({columns:s,data:t}){var P;const{setOpen:i,setCurrentRow:n,open:r}=O(),[o,a]=u.useState({}),[c,m]=u.useState({}),[x,g]=u.useState([]),[w,C]=u.useState([]);u.useEffect(()=>{r!=="bulk-delete"&&a({})},[r]);const D=p=>{n(p),i("update")},y=ns({data:t,columns:s,state:{sorting:w,columnVisibility:c,rowSelection:o,columnFilters:x},enableRowSelection:!0,onRowSelectionChange:a,onSortingChange:C,onColumnFiltersChange:g,onColumnVisibilityChange:m,getCoreRowModel:ds(),getFilteredRowModel:cs(),getPaginationRowModel:os(),getSortedRowModel:ls(),getFacetedRowModel:rs(),getFacetedUniqueValues:as()});return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between space-y-2 gap-x-4",children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Vị trí máy in trong thương hiệu"}),e.jsx(vs,{})]}),e.jsx(bs,{table:y}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(be,{children:[e.jsx(Se,{children:y.getHeaderGroups().map(p=>e.jsx(X,{children:p.headers.map(l=>e.jsx(q,{colSpan:l.colSpan,children:l.isPlaceholder?null:ce(l.column.columnDef.header,l.getContext())},l.id))},p.id))}),e.jsx(we,{children:(P=y.getRowModel().rows)!=null&&P.length?y.getRowModel().rows.map(p=>e.jsx(X,{"data-state":p.getIsSelected()&&"selected",className:"cursor-pointer",onClick:l=>{const j=l.target;j.closest("[data-actions]")||j.closest('[role="checkbox"]')||D(p.original)},children:p.getVisibleCells().map(l=>e.jsx(A,{...l.column.id==="actions"?{"data-actions":!0}:{},children:ce(l.column.columnDef.cell,l.getContext())},l.id))},p.id)):e.jsx(X,{children:e.jsx(A,{colSpan:s.length,className:"h-24 text-center",children:"Không có dữ liệu vị trí máy in."})})})]})}),e.jsx(ms,{table:y})]})}function Os(){const{selectedBrand:s}=ge(),{companyUid:t}=je(),{open:i}=O(),{data:n,isLoading:r,error:o,refetch:a}=gs({params:{company_uid:t||"",brand_uid:(s==null?void 0:s.id)||""},enabled:!0}),c=!i||i!=="create"&&i!=="update";return o?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-muted-foreground mb-2 text-sm",children:"Có lỗi xảy ra khi tải dữ liệu vị trí máy in"}),e.jsx("button",{onClick:()=>a(),className:"text-primary text-sm hover:underline",children:"Thử lại"})]})}):e.jsxs(e.Fragment,{children:[c&&e.jsxs(e.Fragment,{children:[e.jsx(Ue,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(qe,{}),e.jsx(ze,{}),e.jsx(Ve,{})]})}),e.jsx(He,{children:e.jsxs("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12",children:[r&&e.jsx(Ds,{}),!r&&e.jsx(ks,{columns:Ns,data:n})]})})]}),e.jsx(Is,{})]})}function Rs(){return e.jsx(fs,{children:e.jsx(Os,{})})}const Vt=Rs;export{Vt as component};
