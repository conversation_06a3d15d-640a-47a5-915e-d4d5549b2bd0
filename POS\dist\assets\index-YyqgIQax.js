import{j as e,B as z,l as H,b as W,h as Y,r as v,a as J}from"./index-C21OP4ex.js";import"./pos-api-D5WM5mnz.js";import"./vietqr-api-ruJT0-tj.js";import{u as re,a as oe,b as de,c as le,d as me,e as q}from"./use-service-charge-form-SyGvhPw1.js";import"./user-CgNoQQSj.js";import"./crm-api-BUMUQ8t4.js";import{H as he}from"./header-DNPEfjkR.js";import{M as xe}from"./main-DRnqW_wu.js";import{P as ue}from"./profile-dropdown-DlMxjxHH.js";import{S as pe,T as je}from"./search-5KGATEvM.js";import"./date-range-picker-B1pgj5D_.js";import"./form-usWdQ_Nt.js";import{S as k,a as D,b as V,c as B,d as C}from"./select-B8Pw9rS-.js";import{B as G}from"./badge-gtDUxDTX.js";import{T as U,a as X,b as E,c as b,d as F,e as _}from"./table-BIu4Pah2.js";import{P as Q}from"./modal-CFi1vCFt.js";import{C as O}from"./checkbox-DUpnJ1Rx.js";import{a as ge,C as fe}from"./chevron-right-BAjIoZMb.js";import"./date-utils-DBbLjCz0.js";import"./useQuery-BNGphiae.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bh5DVQPI.js";import"./query-keys-3lmd-xp6.js";import"./separator-ZoxOB1XH.js";import"./avatar-C98m2_SM.js";import"./dropdown-menu-DINyPmco.js";import"./index-Bh-UeytL.js";import"./index-Ct3V_iCU.js";import"./index-UJ-79IIJ.js";import"./check-DcHT8QEO.js";import"./createLucideIcon-CL0CQOA1.js";import"./search-context-DtMZc3QX.js";import"./command-BnLmWlRk.js";import"./calendar-BiBi2kQF.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-DXjwjGKV.js";import"./search-DHRhj6_i.js";import"./createReactComponent-zh6rKAzG.js";import"./scroll-area-DKiYF9x5.js";import"./IconChevronRight-Bwbz4HuV.js";import"./IconSearch-CyZD7dtp.js";import"./react-icons.esm-DB-kGUq7.js";import"./popover-BtedB187.js";import"./index-DuT2Ibxp.js";const M={ALL:"all",ALL_LABEL:"Tất cả các điểm"},I={ALL:"all",ACTIVE:"1",INACTIVE:"0",ALL_LABEL:"Tất cả trạng thái",ACTIVE_LABEL:"Active",INACTIVE_LABEL:"Deactive"},w={ALL:"all",EXPIRED:"expired",UNEXPIRED:"unexpired",ALL_LABEL:"Tất cả ngày áp dụng",EXPIRED_LABEL:"Hết hạn",UNEXPIRED_LABEL:"Chưa hết hạn"};function Ne({selectedStoreId:n,setSelectedStoreId:u,selectedStatus:d,setSelectedStatus:l,selectedExpiry:c,setSelectedExpiry:p,currentBrandStores:j,onCreateNew:f,onCopy:N}){return e.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[e.jsx("h2",{className:"text-xl font-semibold whitespace-nowrap",children:"Phí dịch vụ"}),e.jsx("div",{className:"min-w-[160px]",children:e.jsxs(k,{value:n,onValueChange:u,children:[e.jsx(D,{children:e.jsx(V,{placeholder:M.ALL_LABEL})}),e.jsxs(B,{children:[e.jsx(C,{value:M.ALL,children:M.ALL_LABEL}),j.map(o=>e.jsx(C,{value:o.id,children:o.store_name},o.id))]})]})}),e.jsx("div",{className:"min-w-[130px]",children:e.jsxs(k,{value:d,onValueChange:l,children:[e.jsx(D,{children:e.jsx(V,{placeholder:I.ALL_LABEL})}),e.jsxs(B,{children:[e.jsx(C,{value:I.ALL,children:I.ALL_LABEL}),e.jsx(C,{value:I.ACTIVE,children:I.ACTIVE_LABEL}),e.jsx(C,{value:I.INACTIVE,children:I.INACTIVE_LABEL})]})]})}),e.jsx("div",{className:"min-w-[150px]",children:e.jsxs(k,{value:c,onValueChange:p,children:[e.jsx(D,{children:e.jsx(V,{placeholder:w.ALL_LABEL})}),e.jsxs(B,{children:[e.jsx(C,{value:w.ALL,children:w.ALL_LABEL}),e.jsx(C,{value:w.EXPIRED,children:w.EXPIRED_LABEL}),e.jsx(C,{value:w.UNEXPIRED,children:w.UNEXPIRED_LABEL})]})]})}),e.jsxs("div",{className:"ml-auto flex items-center gap-2",children:[e.jsx(z,{variant:"outline",size:"sm",onClick:N,children:"Đồng bộ"}),e.jsx(z,{size:"sm",onClick:f,children:"Tạo phí dịch vụ"})]})]})}function _e({serviceCharge:n,open:u,onOpenChange:d}){var g,T,A,s,i,r;const{selectedBrand:l}=H(),{company:c}=W(),p=((T=(g=n.extra_data)==null?void 0:g.area_ids)==null?void 0:T.join(","))||"",j=((s=(A=n.extra_data)==null?void 0:A.source_ids)==null?void 0:s.join(","))||"",f=((i=n.extra_data)==null?void 0:i.is_area)===1&&p,N=((r=n.extra_data)==null?void 0:r.is_source)===1&&j,{data:o}=re({company_uid:(c==null?void 0:c.id)||"",brand_uid:(l==null?void 0:l.id)||"",store_uid:n.store_uid,list_area_id:p},u&&!!f),{data:h}=oe({company_uid:(c==null?void 0:c.id)||"",brand_uid:(l==null?void 0:l.id)||"",store_uid:n.store_uid,list_source_id:j},u&&!!N),L=(o==null?void 0:o.data)||[],x=(h==null?void 0:h.data)||[],S=()=>{const a=[];return(n.from_amount>0||n.to_amount>0)&&(n.from_amount===n.to_amount&&n.from_amount===0?a.push("tất cả đơn hàng"):a.push(`hoá đơn có giá trị từ ${n.from_amount.toLocaleString("vi-VN")} ₫ đến ${n.to_amount.toLocaleString("vi-VN")} ₫`)),a.join(" và ")};return e.jsx(Q,{title:"Điều kiện mở rộng",open:u,onOpenChange:d,onCancel:()=>d(!1),onConfirm:()=>d(!1),hideButtons:!0,maxWidth:"sm:max-w-[600px]",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-sm text-gray-600",children:["Phí dịch vụ được áp dụng khi ",S()," và thoả mãn điều kiện mở rộng"]}),f&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"text-sm font-medium",children:"Khu vực"}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(U,{children:[e.jsx(X,{children:e.jsx(E,{children:e.jsx(b,{children:"Khu vực"})})}),e.jsx(F,{children:L.length>0?L.map(a=>e.jsx(E,{children:e.jsx(_,{children:a.area_name})},a.area_id)):e.jsx(E,{children:e.jsx(_,{className:"py-4 text-center text-gray-500",children:"Không có Khu vực được áp dụng"})})})]})})]}),N&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"text-sm font-medium",children:"Nguồn"}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(U,{children:[e.jsx(X,{children:e.jsx(E,{children:e.jsx(b,{children:"Nguồn"})})}),e.jsx(F,{children:x.length>0?x.map(a=>e.jsx(E,{children:e.jsx(_,{children:a.source_name})},a.source_id)):e.jsx(E,{children:e.jsx(_,{className:"py-4 text-center text-gray-500",children:"Không có Nguồn được áp dụng"})})})]})})]}),!f&&!N&&e.jsx("div",{className:"py-4 text-center text-gray-500",children:"Không có điều kiện mở rộng nào được áp dụng"})]})})}function Se({serviceCharges:n,isLoading:u,currentBrandStores:d,onToggleActive:l,currentPage:c,pageSize:p=20}){const j=Y(),[f,N]=v.useState(null),[o,h]=v.useState(!1),L=s=>{const i=d.find(r=>r.id===s);return(i==null?void 0:i.store_name)||"Không xác định"},x=(s,i)=>{const r=i.target;r.closest("button")||r.closest('[role="button"]')||r.closest(".badge")||r.tagName==="BUTTON"||j({to:"/sale/service-charge/detail/$id",params:{id:s.id}})},S=(s,i)=>{const r=new Date(s).toLocaleDateString("vi-VN"),a=new Date(i).toLocaleDateString("vi-VN");return`${r} - ${a}`},g=s=>s.charge_type==="PERCENT"?`${(s.service_charge*100).toFixed(0)}%`:`${s.service_charge.toLocaleString("vi-VN")} ₫`,T=s=>{var r,a,y,t,m,R;const i=[];return(s.from_amount>0||s.to_amount>0)&&(s.from_amount===s.to_amount&&s.from_amount===0?i.push("Tất cả đơn hàng"):i.push(`Từ ${s.from_amount.toLocaleString("vi-VN")}₫ đến ${s.to_amount.toLocaleString("vi-VN")}₫`)),((r=s.extra_data)==null?void 0:r.is_source)===1&&((y=(a=s.extra_data)==null?void 0:a.source_ids)==null?void 0:y.length)>0&&i.push(`Nguồn: ${s.extra_data.source_ids.length} nguồn`),((t=s.extra_data)==null?void 0:t.is_table)===1&&((R=(m=s.extra_data)==null?void 0:m.table_ids)==null?void 0:R.length)>0&&i.push(`Bàn: ${s.extra_data.table_ids.length} bàn`),i.length>0?i.join(", "):"Không có điều kiện"},A=s=>{N(s),h(!0)};return e.jsxs("div",{className:"rounded-md border",children:[e.jsxs(U,{children:[e.jsx(X,{children:e.jsxs(E,{children:[e.jsx(b,{className:"w-[50px]",children:"#"}),e.jsx(b,{children:"Tên"}),e.jsx(b,{children:"Cửa hàng"}),e.jsx(b,{children:"Điều kiện áp dụng"}),e.jsx(b,{children:"Thời gian áp dụng"}),e.jsx(b,{children:"Số tiền"}),e.jsx(b,{children:"Thao tác"})]})}),e.jsx(F,{children:u?e.jsx(E,{children:e.jsx(_,{colSpan:7,className:"h-24 text-center",children:"Đang tải dữ liệu..."})}):n.length===0?e.jsx(E,{children:e.jsx(_,{colSpan:7,className:"text-muted-foreground h-24 text-center",children:"Không có dữ liệu"})}):n.map((s,i)=>{const r=(c-1)*p+i+1;return e.jsxs(E,{className:"hover:bg-muted/50 cursor-pointer",onClick:a=>x(s,a),children:[e.jsx(_,{className:"font-medium",children:r}),e.jsx(_,{children:e.jsxs("div",{className:"font-medium",children:["Phí dịch vụ ",g(s)]})}),e.jsx(_,{children:L(s.store_uid)}),e.jsx(_,{children:e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"text-sm",children:T(s)}),e.jsx("button",{onClick:a=>{a.stopPropagation(),A(s)},className:"cursor-pointer text-xs text-blue-600 hover:text-blue-800 hover:underline",children:"Điều kiện mở rộng"})]})}),e.jsx(_,{children:e.jsx("div",{className:"space-y-1",children:s.to_date<Date.now()?e.jsx("div",{className:"font-medium text-red-600",children:"Hết hạn"}):e.jsx("div",{className:"text-sm",children:S(s.from_date,s.to_date)})})}),e.jsx(_,{className:"font-medium",children:g(s)}),e.jsx(_,{children:s.active===1?e.jsx(G,{variant:"default",className:"cursor-pointer bg-green-100 text-green-800 hover:bg-green-200",onClick:a=>{a.stopPropagation(),l(s)},children:"Active"}):e.jsx(G,{variant:"destructive",className:"cursor-pointer bg-red-100 text-red-800 hover:bg-red-200",onClick:a=>{a.stopPropagation(),l(s)},children:"Deactive"})})]},s.id)})})]}),f&&e.jsx(_e,{serviceCharge:f,open:o,onOpenChange:h})]})}function ve({isOpen:n,onClose:u}){const[d,l]=v.useState(""),[c,p]=v.useState(new Set),[j,f]=v.useState(new Set),{currentBrandStores:N}=J(),{selectedBrand:o}=H(),{company:h}=W(),L=de(),{data:x=[],isLoading:S}=le({company_uid:h==null?void 0:h.id,brand_uid:o==null?void 0:o.id,store_uid:d,enabled:!!d}),g=N.filter(t=>t.id!==d),T=t=>{const m=new Set(j);m.has(t)?m.delete(t):m.add(t),f(m)},A=t=>{const m=new Set(c);m.has(t)?m.delete(t):m.add(t),p(m)},s=()=>{c.size===g.length?p(new Set):p(new Set(g.map(t=>t.id)))},i=async()=>{if(!(!(h!=null&&h.id)||!(o!=null&&o.id)||!d||c.size===0||j.size===0))try{const t=Array.from(j),m=Array.from(c);await L.mutateAsync({company_uid:h.id,brand_uid:o.id,list_service_charge_uid:t,list_store_uid_target:m}),r()}catch(t){console.error("Error copying service charge programs:",t)}},r=()=>{l(""),p(new Set),f(new Set),u()},a=t=>t.charge_type==="PERCENT"?`${(t.service_charge*100).toFixed(0)}%`:`${t.service_charge.toLocaleString("vi-VN")} ₫`,y=x.filter(t=>j.has(t.id));return e.jsx(Q,{title:"Sao chép phí dịch vụ",open:n,onOpenChange:r,onCancel:r,onConfirm:i,confirmText:"Sao chép",confirmDisabled:!d||c.size===0||j.size===0||L.isPending,maxWidth:"sm:max-w-6xl",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium",children:"Cửa hàng nguồn"}),e.jsxs(k,{value:d,onValueChange:l,children:[e.jsx(D,{className:"mt-1",children:e.jsx(V,{placeholder:"Chọn cửa hàng nguồn"})}),e.jsx(B,{children:N.map(t=>e.jsx(C,{value:t.id,children:t.store_name},t.id))})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Phí dịch vụ"}),e.jsx("div",{className:"max-h-64 overflow-y-auto rounded-md border p-3",children:S?e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:"Đang tải..."}):x.length===0?e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:d?"Không có phí dịch vụ":"Chọn cửa hàng nguồn"}):e.jsx("div",{className:"space-y-3",children:x.map(t=>e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(O,{id:t.id,checked:j.has(t.id),onCheckedChange:()=>T(t.id)}),e.jsx("div",{className:"min-w-0 flex-1",children:e.jsxs("div",{className:"text-sm font-medium",children:["Phí dịch vụ ",a(t)]})})]},t.id))})})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium",children:"Cửa hàng đích"}),e.jsxs(k,{children:[e.jsx(D,{className:"mt-1",children:e.jsx(V,{placeholder:c.size===0?"Chọn cửa hàng đích":`Đã chọn ${c.size} cửa hàng`})}),e.jsx(B,{children:g.length===0?e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:"Chọn cửa hàng nguồn trước"}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center space-x-3 border-b px-2 py-1.5",children:[e.jsx(O,{id:"select-all-targets",checked:c.size===g.length&&g.length>0,onCheckedChange:s,onClick:t=>t.stopPropagation()}),e.jsx("label",{htmlFor:"select-all-targets",className:"flex-1 cursor-pointer text-sm font-medium",onClick:t=>{t.preventDefault(),s()},children:"Chọn tất cả"})]}),g.map(t=>e.jsxs("div",{className:"flex items-center space-x-3 px-2 py-1.5",children:[e.jsx(O,{id:`target-${t.id}`,checked:c.has(t.id),onCheckedChange:()=>A(t.id),onClick:m=>m.stopPropagation()}),e.jsx("label",{htmlFor:`target-${t.id}`,className:"flex-1 cursor-pointer text-sm font-medium",onClick:m=>{m.preventDefault(),A(t.id)},children:t.store_name})]},t.id))]})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Phí dịch vụ được chọn"}),e.jsx("div",{className:"max-h-64 overflow-y-auto rounded-md border p-3",children:y.length===0?e.jsx("div",{className:"text-muted-foreground py-4 text-center text-sm",children:"Chưa chọn phí dịch vụ nào"}):e.jsx("div",{className:"space-y-2",children:y.map(t=>e.jsx("div",{className:"bg-muted rounded-md p-2",children:e.jsxs("div",{className:"text-sm font-medium",children:["Phí dịch vụ ",a(t)]})},t.id))})})]})]})]})})}function Le({currentPage:n,onPageChange:u,hasNextPage:d}){const l=()=>{n>1&&u(n-1)},c=()=>{d&&u(n+1)};return e.jsxs("div",{className:"flex items-center justify-center gap-4 py-4",children:[e.jsxs(z,{variant:"outline",size:"sm",onClick:l,disabled:n===1,className:"flex items-center gap-2",children:[e.jsx(ge,{className:"h-4 w-4"}),"Trước"]}),e.jsx("span",{className:"text-sm font-medium",children:n}),e.jsxs(z,{variant:"outline",size:"sm",onClick:c,disabled:!d,className:"flex items-center gap-2",children:["Sau",e.jsx(fe,{className:"h-4 w-4"})]})]})}const $={storeId:"all",status:"all",expiry:"unexpired"},K=1;function Ee(){const n=Y(),[u,d]=v.useState($.storeId),[l,c]=v.useState($.status),[p,j]=v.useState($.expiry),[f,N]=v.useState(!1),[o,h]=v.useState(K),{currentBrandStores:L}=J(),{selectedBrand:x}=H(),{company:S}=W(),g=me(),A=u==="all"?L.map(P=>P.id):[u],s={company_uid:(S==null?void 0:S.id)||"",brand_uid:(x==null?void 0:x.id)||"",page:o,list_store_uid:A.join(","),status:p==="all"?void 0:p,active:l==="all"?void 0:parseInt(l)},{data:i=[],isLoading:r}=q({params:s}),a={...s,page:o+1},y=!!(S!=null&&S.id)&&!!(x!=null&&x.id)&&i.length>0,{data:t=[]}=q({params:a,enabled:y});v.useEffect(()=>{d($.storeId),h(K)},[x==null?void 0:x.id]),v.useEffect(()=>{h(K)},[u,l,p]);const m=P=>({...P,active:P.active===1?0:1}),R=P=>{const ce=m(P);g.mutate(ce)},Z=()=>{n({to:"/sale/service-charge/detail"})},ee=()=>{N(!0)},te=P=>{h(P)},se=i.length>0,ne=t.length>0,ae=!r&&se,ie=()=>{N(!1)};return e.jsxs(e.Fragment,{children:[e.jsx(he,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(pe,{}),e.jsx(je,{}),e.jsx(ue,{})]})}),e.jsx(xe,{children:e.jsxs("div",{className:"container mx-auto space-y-6 p-6",children:[e.jsx(Ne,{selectedStoreId:u,setSelectedStoreId:d,selectedStatus:l,setSelectedStatus:c,selectedExpiry:p,setSelectedExpiry:j,currentBrandStores:L,onCreateNew:Z,onCopy:ee}),e.jsx(Se,{serviceCharges:i,isLoading:r,currentBrandStores:L,onToggleActive:R,currentPage:o,pageSize:20}),ae&&e.jsx(Le,{currentPage:o,onPageChange:te,hasNextPage:ne}),e.jsx(ve,{isOpen:f,onClose:ie})]})})]})}const pt=function(){return e.jsx(Ee,{})};export{pt as component};
