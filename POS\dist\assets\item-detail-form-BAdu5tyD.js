import{u as He,l as Me,r as z,z as s,h as de,j as e,B as D,T as Ee,o as Ke,p as Le,q as Ue}from"./index-Bnt3OGV2.js";import{a as y,b as N,c as b,d as T,e as I,u as me,F as ue,k as $e}from"./form-wT1R35uI.js";import{s as he}from"./zod-CNC8A7Cl.js";import{u as Qe}from"./use-upload-image-Dtz4u4ii.js";import"./pos-api-BwpRFGce.js";import"./exceljs.min-Da2DFgpf.js";import"./vietqr-api-DAENYiJ_.js";import"./user-oq7iQk7S.js";import"./crm-api-Dd9UhSCJ.js";import{B as Xe,k as We,h as Ge,f as Ye}from"./customization-dialog-Djqz8P-n.js";import{u as Je}from"./use-item-types-Q-0SzpLA.js";import{u as Ze}from"./use-item-classes-CsFb8pem.js";import{u as Re}from"./use-units-DeWvOXUG.js";import{u as es}from"./use-items-DpA4YqSE.js";import{u as ss}from"./use-removed-items-niCKPB6t.js";import{u as ts}from"./use-customizations-DcL8Qx9h.js";import{u as is}from"./use-customization-by-id-BDmYKkLo.js";import{u as ns}from"./use-sources-ByVM6ea-.js";import{X as ie}from"./calendar-CzR6WBaB.js";import{C as U}from"./checkbox-BiVztVsP.js";import{I as E}from"./input-CiKEYbig.js";import{T as as}from"./textarea-Da8YG9AB.js";import{C as G}from"./combobox-Dfida1wD.js";import{D as xe,a as pe,b as _e,c as ge,e as ze,f as Pe}from"./dialog-hQ-PVOWr.js";import{U as qe}from"./upload-BdXwcNQr.js";import{C as ls,a as rs,b as cs}from"./collapsible-CfXqqCTe.js";import{C as os}from"./confirm-dialog-D0ceQYlS.js";import"./header-stuEr_6l.js";import"./main-Dj7NWzIf.js";import"./search-context-DLufo9i0.js";import"./date-range-picker-CVvofQC0.js";import"./multi-select-DmQbcyz3.js";import{D as Ie}from"./date-picker-DhOVzRMj.js";import{C as oe}from"./circle-help-vaGE5apo.js";import{C as ds}from"./select-Czd7KcZQ.js";import{C as ms}from"./chevron-right-sZt3EK3r.js";const R=()=>{const i="0123456789";return`ITEM-${Array.from({length:4},()=>i[Math.floor(Math.random()*i.length)]).join("")}`},us=(i,r)=>{var m;return(m=r==null?void 0:r.find(g=>g.sourceId===i||g.id===i))==null?void 0:m.sourceName},hs=i=>{const r={1:"Thứ 2",2:"Thứ 3",4:"Thứ 4",8:"Thứ 5",16:"Thứ 6",32:"Thứ 7",64:"Chủ nhật"};return i.map(m=>r[m]).join(", ")},xs=i=>i.map(r=>`${r}h`).join(", "),ps=i=>{const r=[];return i&1&&r.push(1),i&2&&r.push(2),i&4&&r.push(4),i&8&&r.push(8),i&16&&r.push(16),i&32&&r.push(32),i&64&&r.push(64),r},_s=i=>{const r=[];for(let m=0;m<24;m++)i&1<<m&&r.push(m);return r},Ve=i=>i.toLocaleDateString("vi-VN"),De=i=>i.map(r=>{const m=r.selectedDays.reduce((d,p)=>d|p,0),g=r.selectedHours.reduce((d,p)=>d|1<<p,0);return{price:r.price,from_date:r.startDate.getTime(),to_date:new Date(r.endDate.getTime()+24*60*60*1e3-1).getTime(),time_sale_date_week:m,time_sale_hour_day:g}});function Be({form:i}={}){const r=i==null?void 0:i.watch("city_uid"),m=i==null?void 0:i.watch("customization_uid"),{company:g}=He(j=>j.auth),{selectedBrand:d}=Me(),{data:p=[]}=ns({company_uid:g==null?void 0:g.id,brand_uid:d==null?void 0:d.id,skip_limit:!0,enabled:!!(g!=null&&g.id)&&!!(d!=null&&d.id)}),{data:C=[]}=ts({skip_limit:!0,list_city_uid:r!=="all"?[r||""]:void 0,enabled:!!r}),{data:h}=is(m||"",!!m&&m!=="none"),{data:V=[]}=es({params:{city_uid:r,skip_limit:!0},enabled:!!r}),{data:F=[]}=ss(),{data:k=[]}=Je(),{data:w=[]}=Re(),{data:o=[]}=Ze({skip_limit:!0});return{selectedCityUid:r,selectedCustomizationUid:m,company:g,selectedBrand:d,customizations:C,customizationDetails:h,items:V,sourcesData:p,cities:F,itemTypes:k,units:w,itemClasses:o}}function gs(){const[i,r]=z.useState(!1),[m,g]=z.useState(!1),[d,p]=z.useState(!1),[C,h]=z.useState(!1),[V,F]=z.useState(null);return{showQuantityInputs:i,isCustomizationDetailsOpen:m,isPriceSourceDialogOpen:d,isBuffetConfigModalOpen:C,confirmDeleteIndex:V,setShowQuantityInputs:r,setIsCustomizationDetailsOpen:g,setIsPriceSourceDialogOpen:p,setIsBuffetConfigModalOpen:h,setConfirmDeleteIndex:F,toggleQuantityInputs:()=>{r(!i)},openPriceSourceDialog:()=>{p(!0)},closePriceSourceDialog:()=>{p(!1)},openBuffetConfigModal:()=>{h(!0)},closeBuffetConfigModal:()=>{h(!1)},handleRemovePriceSource:n=>{F(n)},clearConfirmDelete:()=>{F(null)}}}s.object({id:s.string().optional(),item_id:s.string().optional(),item_name:s.string().optional(),description:s.string().optional(),ots_price:s.coerce.number().optional(),ots_tax:s.coerce.number().optional(),ta_price:s.coerce.number().optional(),ta_tax:s.coerce.number().optional(),time_sale_hour_day:s.coerce.number().optional(),time_sale_date_week:s.coerce.number().optional(),allow_take_away:s.coerce.number().optional(),is_eat_with:s.coerce.number().optional(),image_path:s.string().optional(),image_path_thumb:s.string().optional(),item_color:s.string().optional(),list_order:s.coerce.number().optional(),is_service:s.coerce.number().optional(),is_material:s.coerce.number().optional(),active:s.coerce.number().optional(),user_id:s.string().optional(),is_foreign:s.coerce.number().optional(),quantity_default:s.coerce.number().optional(),price_change:s.coerce.number().optional(),currency_type_id:s.string().optional(),point:s.coerce.number().optional(),is_gift:s.coerce.number().optional(),is_fc:s.coerce.number().optional(),show_on_web:s.coerce.number().optional(),show_price_on_web:s.coerce.number().optional(),cost_price:s.coerce.number().optional(),is_print_label:s.coerce.number().optional(),quantity_limit:s.coerce.number().optional(),is_kit:s.coerce.number().optional(),time_cooking:s.coerce.number().optional(),item_id_barcode:s.string().optional(),process_index:s.coerce.number().optional(),is_allow_discount:s.coerce.number().optional(),quantity_per_day:s.coerce.number().optional(),item_id_eat_with:s.string().optional(),is_parent:s.coerce.number().optional(),is_sub:s.coerce.number().optional(),item_id_mapping:s.string().optional(),effective_date:s.coerce.number().optional(),expire_date:s.coerce.number().optional(),sort:s.coerce.number().optional(),sort_online:s.coerce.number().optional(),revision:s.coerce.number().optional(),unit_uid:s.string().optional(),unit_secondary_uid:s.string().nullable().optional(),item_type_uid:s.string().optional(),item_class_uid:s.string().optional(),source_uid:s.string().nullable().optional(),brand_uid:s.string().optional(),city_uid:s.string().optional(),company_uid:s.string().optional(),customization_uid:s.string().nullable().optional(),is_fabi:s.coerce.number().optional(),deleted:s.boolean().optional(),created_by:s.string().optional(),updated_by:s.string().optional(),deleted_by:s.string().nullable().optional(),created_at:s.coerce.number().optional(),updated_at:s.coerce.number().optional(),deleted_at:s.coerce.number().nullable().optional(),status_trigger_disabled:s.boolean().optional(),is_featured:s.coerce.number().optional(),extra_data:s.object({cross_price:s.array(s.object({price:s.coerce.number().optional(),quantity:s.coerce.number().optional()})).optional(),formula_qrcode:s.string().optional(),is_buffet_item:s.coerce.number().optional(),up_size_buffet:s.array(s.any()).optional(),is_item_service:s.coerce.number().optional(),is_virtual_item:s.coerce.number().optional(),price_by_source:s.array(s.object({price:s.coerce.number().optional(),source_id:s.string().optional(),price_times:s.array(s.any()).optional(),is_source_exist_in_city:s.boolean().optional()})).optional(),enable_edit_price:s.coerce.number().optional(),exclude_items_buffet:s.array(s.string()).optional(),no_update_quantity_toping:s.coerce.number().optional()}).optional(),enable_custom_item_id:s.coerce.number().optional(),cities:s.array(s.object({id:s.string().optional(),city_id:s.string().optional(),fb_city_id:s.string().optional(),city_name:s.string().optional(),image_path:s.string().nullable().optional(),description:s.string().optional(),active:s.coerce.number().optional(),extra_data:s.any().nullable().optional(),revision:s.coerce.number().optional(),sort:s.coerce.number().optional(),created_by:s.string().nullable().optional(),updated_by:s.string().nullable().optional(),deleted_by:s.string().nullable().optional(),created_at:s.coerce.number().optional(),updated_at:s.coerce.number().optional(),deleted_at:s.coerce.number().nullable().optional(),items_cities:s.object({item_uid:s.string().optional(),city_uid:s.string().optional()}).optional()})).optional()});const bs=s.object({id:s.string().optional(),item_id:s.string().optional(),item_name:s.string().min(1,"Tên món là bắt buộc"),description:s.string().optional(),ots_price:s.coerce.number().min(0).optional(),ots_tax:s.coerce.number().min(0).max(1).optional(),ta_price:s.coerce.number().min(0).optional(),ta_tax:s.coerce.number().min(0).max(1).optional(),time_cooking:s.coerce.number().optional(),time_sale_hour_day:s.coerce.number().optional(),time_sale_date_week:s.coerce.number().optional(),allow_take_away:s.coerce.number().optional(),is_eat_with:s.coerce.number().optional(),image_path:s.string().url().optional(),image_path_thumb:s.string().url().optional(),item_color:s.string().optional(),list_order:s.coerce.number().optional(),is_service:s.coerce.number().optional(),is_material:s.coerce.number().optional(),is_print_label:s.coerce.number().optional(),is_allow_discount:s.coerce.number().optional(),item_id_barcode:s.string().optional(),process_index:s.coerce.number().optional(),quantity_per_day:s.coerce.number().optional(),item_id_eat_with:s.string().optional(),is_parent:s.coerce.number().optional(),is_sub:s.coerce.number().optional(),item_id_mapping:s.string().optional(),effective_date:s.coerce.number().optional(),expire_date:s.coerce.number().optional(),sort:s.coerce.number().optional(),sort_online:s.coerce.number().optional(),unit_uid:s.string().min(1,"Đơn vị là bắt buộc").optional(),unit_secondary_uid:s.string().nullable().optional(),item_type_uid:s.string().min(1,"Loại món là bắt buộc"),item_class_uid:s.string().optional(),city_uid:s.string().min(1,"Thành phố là bắt buộc"),customization_uid:s.string().nullable().optional(),is_featured:s.coerce.number().optional(),cross_price:s.array(s.object({price:s.coerce.number().min(0).optional(),quantity:s.coerce.number().min(0).optional()})).optional(),formula_qrcode:s.string().optional(),is_buffet_item:s.coerce.number().optional(),up_size_buffet:s.array(s.any()).optional(),is_item_service:s.coerce.number().optional(),is_virtual_item:s.coerce.number().optional(),price_by_source:s.array(s.object({price:s.coerce.number().min(0).optional(),source_id:s.string().optional(),price_times:s.array(s.any()).optional(),is_source_exist_in_city:s.boolean().optional()})).optional(),price_times:s.array(s.any()).optional(),enable_edit_price:s.coerce.number().optional(),exclude_items_buffet:s.array(s.string()).optional(),no_update_quantity_toping:s.coerce.number().optional(),quantity:s.coerce.number().optional(),price:s.coerce.number().optional(),enable_custom_item_id:s.coerce.number().optional()});function js({isUpdate:i,currentRow:r,isLoading:m,onSave:g,onSaveAndSync:d,onDeactive:p,onActive:C,isDeactivating:h=!1,isActivating:V=!1}){const F=de(),k=()=>{F({to:"/menu/items/items-in-city"})},w=()=>i?"Chi tiết món":"Tạo món";return e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("div",{children:e.jsx(D,{variant:"ghost",size:"sm",onClick:k,className:"flex items-center",children:e.jsx(ie,{className:"h-4 w-4"})})}),e.jsx("h2",{className:"mb-2 text-3xl font-medium",children:w()}),e.jsxs("div",{className:"flex gap-2",children:[i&&(r==null?void 0:r.active)&&p&&e.jsx(D,{type:"button",variant:"outline",className:"border-red-500 text-red-500 hover:bg-red-50",disabled:h,onClick:p,children:h?"Đang deactive...":"Deactive"}),i&&!(r!=null&&r.active)&&C&&e.jsx(D,{type:"button",variant:"outline",className:"border-green-500 text-green-500 hover:bg-green-50",disabled:V,onClick:C,children:V?"Đang active...":"Active"}),e.jsx(D,{type:"button",disabled:m,onClick:d,children:m?"Đang lưu và đồng bộ...":"Lưu và đồng bộ"}),e.jsx(D,{type:"button",disabled:m,onClick:g,children:m?"Đang lưu...":"Lưu"})]})]})})}const ys=["#3B82F6","#EF4444","#10B981","#F59E0B","#8B5CF6","#EC4899","#F97316","#84CC16","#06B6D4","#6366F1"],fs=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFEAA7","#DDA0DD","#98D8C8","#F7DC6F","#BB8FCE","#85C1E9"];function vs({open:i,onOpenChange:r,onImageSelect:m,onColorSelect:g,onSelfOrderToggle:d,selectedColor:p="#000000",useSelfOrderImage:C=!1,currentImageUrl:h}){const[V,F]=z.useState(null),[k,w]=z.useState(null),o=h||V,j=n=>{n||(w(null),F(null)),r(n)},P=n=>{var B;const c=(B=n.target.files)==null?void 0:B[0];if(c){w(c);const X=new FileReader;X.onload=x=>{var f;F((f=x.target)==null?void 0:f.result)},X.readAsDataURL(c)}},S=()=>{k?m(k):p&&p!=="#000000"&&g(p),j(!1)},A=n=>{g(n)};return e.jsx(xe,{open:i,onOpenChange:j,children:e.jsxs(pe,{className:"max-h-[90vh] w-[95vw] max-w-3xl overflow-y-auto lg:max-w-2xl",children:[e.jsx(_e,{children:e.jsx(ge,{className:"text-lg font-semibold",children:"Chọn ảnh hoặc màu"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Chọn ảnh"}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Bạn có thể chọn ảnh có kích thước 540x785px để sử dụng trên thiết bị Self Order"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(U,{checked:C,onCheckedChange:d}),e.jsx("label",{className:"text-sm font-medium",children:"Sử dụng ảnh cho thiết bị Self Order"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-4 transition-colors hover:bg-gray-100",style:{width:"467px",height:"420px",maxWidth:"100%",maxHeight:"380px"},onClick:()=>{var n;return(n=document.getElementById("dialog-image-upload"))==null?void 0:n.click()},children:o?e.jsx("img",{src:o,alt:"Preview",className:"h-full w-full rounded-lg object-cover"}):e.jsxs("div",{className:"text-center",children:[e.jsx(qe,{className:"mx-auto mb-2 h-8 w-8 text-gray-400"}),e.jsx("span",{className:"text-sm text-gray-500",children:"Click để chọn ảnh"})]})}),e.jsx("input",{type:"file",accept:"image/*",onChange:P,className:"hidden",id:"dialog-image-upload"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Hoặc chọn màu dưới đây"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"flex space-x-2",children:ys.map(n=>e.jsx("button",{className:`h-8 w-8 rounded-full border-2 transition-all ${p===n?"scale-110 border-gray-900":"border-gray-300 hover:border-gray-400"}`,style:{backgroundColor:n},onClick:()=>A(n)},n))}),e.jsx(E,{type:"text",value:p,onChange:n=>A(n.target.value),className:"w-24 text-center",placeholder:"#000000"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 text-sm font-medium text-gray-700",children:"Khác"}),e.jsx("div",{className:"flex space-x-2",children:fs.map(n=>e.jsx("button",{className:`h-8 w-8 rounded-full border-2 transition-all ${p===n?"scale-110 border-gray-900":"border-gray-300 hover:border-gray-400"}`,style:{backgroundColor:n},onClick:()=>A(n)},n))})]})]})]}),e.jsxs("div",{className:"flex justify-end space-x-2 pt-4",children:[e.jsx(D,{variant:"outline",onClick:()=>r(!1),children:"Huỷ"}),e.jsx(D,{onClick:S,children:"Xong"})]})]})})}function Ns({form:i,itemTypes:r,itemClasses:m,units:g,cities:d,onImageChange:p,imagePreview:C,onImageRemove:h}){const[V,F]=z.useState(!1),[k,w]=z.useState(()=>{const n=i.getValues("item_color");return n&&n.trim()!==""?n:"#000000"}),[o,j]=z.useState(!1),P=n=>{p({target:{files:[n]}}),w("#000000"),i.setValue("item_color","")},S=n=>{w(n),i.setValue("item_color",n),h&&h()},A=n=>{j(n)};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Chi tiết"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-12",children:[e.jsxs("div",{className:"space-y-6 lg:col-span-9",children:[e.jsx(y,{control:i.control,name:"item_name",render:({field:n})=>e.jsxs(N,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(b,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:["Tên ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(T,{className:"flex-1",children:e.jsx(E,{placeholder:"Nhập tên món",className:"w-full",...n})})]}),e.jsx(I,{})]})}),e.jsx(y,{control:i.control,name:"ots_price",render:({field:n})=>e.jsxs(N,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(b,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:["Giá ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(T,{className:"flex-1",children:e.jsx(E,{placeholder:"0",className:"w-full",value:n.value?new Intl.NumberFormat("vi-VN").format(n.value):"",onChange:c=>{const B=c.target.value.replace(/[^\d]/g,"");n.onChange(B?Number(B):0)}})})]}),e.jsx(I,{})]})}),e.jsx(y,{control:i.control,name:"item_id",render:({field:n})=>e.jsxs(N,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(b,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Mã món"}),e.jsx(T,{className:"flex-1",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(E,{placeholder:"Nếu để trống, hệ thống sẽ tự động tạo một mã món",className:"w-full",...n,readOnly:!i.watch("enable_custom_item_id")}),e.jsx(y,{control:i.control,name:"enable_custom_item_id",render:({field:c})=>e.jsx(T,{children:e.jsx(U,{checked:c.value,onCheckedChange:B=>{c.onChange(B),B?i.setValue("item_id","",{shouldDirty:!0,shouldValidate:!0}):i.setValue("item_id",R(),{shouldDirty:!0,shouldValidate:!0})},className:"border-2 border-blue-500 data-[state=checked]:border-blue-500 data-[state=checked]:bg-blue-500"})})})]})})]}),e.jsx(I,{})]})})]}),e.jsx("div",{className:"lg:col-span-3",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-2 transition-colors hover:bg-gray-100",style:{height:"156px",backgroundColor:k&&k!=="#000000"?k:void 0},onClick:()=>F(!0),children:C?e.jsx("img",{src:C,alt:"Preview",className:"h-full w-full rounded-lg object-cover"}):k&&k!=="#000000"?e.jsx("div",{className:"h-full w-full rounded-lg",style:{backgroundColor:k}}):e.jsxs(e.Fragment,{children:[e.jsx(qe,{className:"mb-1 h-6 w-6 text-gray-400"}),e.jsx("span",{className:"text-center text-xs text-gray-500",children:"Chọn ảnh"})]})}),(C||k&&k!=="#000000")&&e.jsx("button",{type:"button",onClick:()=>{C&&h&&h(),w("#000000"),i.setValue("item_color","")},className:"absolute -top-2 -right-2 rounded-full bg-gray-600 p-1 text-white transition-colors hover:bg-gray-700",children:e.jsx(ie,{className:"h-3 w-3"})}),e.jsx("input",{type:"file",accept:"image/*",onChange:p,className:"hidden",id:"image-upload"})]})})]}),e.jsx(y,{control:i.control,name:"item_id_barcode",render:({field:n})=>e.jsxs(N,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(b,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Mã barcode"}),e.jsx(T,{className:"flex-1",children:e.jsx(E,{placeholder:"Nếu bạn sử dụng tính năng scan QR thì POS hay tạo mã barcode",className:"w-full",maxLength:15,...n})})]}),e.jsx(I,{})]})}),e.jsx(y,{control:i.control,name:"is_eat_with",render:({field:n})=>e.jsx(N,{children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(b,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Món ăn kèm"}),e.jsx(T,{children:e.jsx(U,{checked:n.value===1,onCheckedChange:c=>n.onChange(c?1:0)})})]})})}),e.jsx(y,{control:i.control,name:"no_update_quantity_toping",render:({field:n})=>e.jsx(N,{children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(b,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Không cập nhật số lượng món ăn kèm"}),e.jsx(T,{children:e.jsx(U,{checked:n.value===1,onCheckedChange:c=>n.onChange(c?1:0)})})]})})}),e.jsx(y,{control:i.control,name:"item_type_uid",render:({field:n})=>e.jsxs(N,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(b,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Nhóm"}),e.jsx(T,{className:"flex-1",children:e.jsx(G,{options:r.map(c=>({value:c.id,label:c.item_type_name,disabled:c.active===0,status:c.active===0?"Deactive":void 0,statusClassName:c.active===0?"bg-red-100 text-red-700":void 0})),value:n.value,onValueChange:c=>n.onChange(c||void 0),placeholder:"Uncategory",searchPlaceholder:"Tìm kiếm...",emptyText:"Không tìm thấy nhóm.",className:"flex-1 text-blue-500"})})]}),e.jsx(I,{})]})}),e.jsx(y,{control:i.control,name:"item_class_uid",render:({field:n})=>e.jsxs(N,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(b,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Loại món"}),e.jsx(T,{className:"flex-1",children:e.jsx(G,{options:m.map(c=>({value:c.id,label:c.item_class_name,disabled:c.active===0,status:c.active===0?"Deactive":void 0,statusClassName:c.active===0?"bg-red-100 text-red-700":void 0})),value:n.value,onValueChange:c=>n.onChange(c||void 0),placeholder:"None",searchPlaceholder:"Tìm kiếm loại món...",emptyText:"Không tìm thấy loại món.",className:"flex-1 text-blue-500"})})]}),e.jsx(I,{})]})}),e.jsx(y,{control:i.control,name:"description",render:({field:n})=>e.jsxs(N,{children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx(b,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Mô tả"}),e.jsx(T,{className:"flex-1",children:e.jsx(as,{placeholder:"Nếu để trống thì tên món sẽ tự động làm mô tả món",className:"min-h-[80px] w-full",...n})})]}),e.jsx(I,{})]})}),e.jsx(y,{control:i.control,name:"city_uid",render:({field:n})=>e.jsxs(N,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(b,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:["Thành phố ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(T,{className:"flex-1",children:e.jsx(G,{options:d.map(c=>({value:c.id,label:c.city_name||c.id,disabled:c.active===0,status:c.active===0?"Deactive":void 0,statusClassName:c.active===0?"bg-red-100 text-red-700":void 0})),value:n.value,onValueChange:c=>n.onChange(c||""),placeholder:"Chọn thành phố",searchPlaceholder:"Tìm kiếm thành phố...",emptyText:"Không tìm thấy thành phố.",className:"flex-1 text-blue-500"})})]}),e.jsx(I,{})]})}),e.jsx(y,{control:i.control,name:"item_id_mapping",render:({field:n})=>e.jsxs(N,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(b,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"SKU"}),e.jsx(T,{className:"flex-1",children:e.jsx(E,{placeholder:"Nhập mã SKU",className:"w-full",maxLength:50,...n})})]}),e.jsx(I,{})]})}),e.jsx(y,{control:i.control,name:"unit_uid",render:({field:n})=>e.jsxs(N,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(b,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:["Đơn vị tính ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(T,{className:"flex-1",children:e.jsx(G,{options:g.map(c=>({value:c.id,label:c.unit_name||c.id})),value:n.value,onValueChange:c=>n.onChange(c||void 0),placeholder:"Món",searchPlaceholder:"Tìm kiếm đơn vị tính...",emptyText:"Không tìm thấy đơn vị tính.",className:"flex-1 text-blue-500"})})]}),e.jsx(I,{})]})}),e.jsx(y,{control:i.control,name:"unit_secondary_uid",render:({field:n})=>e.jsxs(N,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(b,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Đơn vị tính thứ 2"}),e.jsx(T,{className:"flex-1",children:e.jsx(G,{options:g.map(c=>({value:c.id,label:c.unit_name||c.id})),value:n.value,onValueChange:c=>n.onChange(c||void 0),placeholder:"Chọn đơn vị tính",searchPlaceholder:"Tìm kiếm đơn vị tính...",emptyText:"Không tìm thấy đơn vị tính.",className:"flex-1 text-blue-500"})})]}),e.jsx(I,{})]})}),e.jsx(y,{control:i.control,name:"ots_tax",render:({field:n})=>e.jsxs(N,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(b,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"VAT món ăn"}),e.jsx(T,{className:"flex-1",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(E,{type:"number",placeholder:"0",className:"w-full",value:n.value?Math.round(n.value*100):"",onChange:c=>{const B=c.target.value;n.onChange(B?Number(B)/100:0)}}),e.jsx("span",{children:"%"})]})})]}),e.jsx(I,{})]})}),e.jsx(y,{control:i.control,name:"time_cooking",render:({field:n})=>e.jsxs(N,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(b,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Thời gian chế biến (phút)"}),e.jsx(T,{className:"flex-1",children:e.jsx(E,{type:"number",placeholder:"0",className:"w-full",value:n.value?Math.round(n.value/6e4):"",onChange:c=>{const B=c.target.value;n.onChange(B?Number(B)*6e4:0)}})})]}),e.jsx(I,{})]})})]})]}),e.jsx(vs,{open:V,onOpenChange:F,onImageSelect:P,onColorSelect:S,onSelfOrderToggle:A,selectedColor:k,useSelfOrderImage:o,currentImageUrl:C||void 0})]})}const Cs=s.object({amount:s.coerce.number().min(0,"Số tiền phải lớn hơn hoặc bằng 0"),startDate:s.date({required_error:"Vui lòng chọn ngày bắt đầu"}),endDate:s.date({required_error:"Vui lòng chọn ngày kết thúc"}),selectedDays:s.array(s.number()).min(1,"Vui lòng chọn ít nhất 1 ngày"),selectedHours:s.array(s.number()).min(1,"Vui lòng chọn ít nhất 1 giờ")}).refine(i=>i.startDate&&i.endDate?i.endDate>=i.startDate:!0,{message:"Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu",path:["endDate"]}),Fe=i=>i.replace(/\D/g,"").replace(/\B(?=(\d{3})+(?!\d))/g,","),ks=i=>parseInt(i.replace(/,/g,""),10)||0,ws=[{value:1,label:"Thứ 2"},{value:2,label:"Thứ 3"},{value:4,label:"Thứ 4"},{value:8,label:"Thứ 5"},{value:16,label:"Thứ 6"},{value:32,label:"Thứ 7"},{value:64,label:"Chủ nhật"}],Ss=Array.from({length:24},(i,r)=>({value:r,label:`${r}h`}));function Ts({open:i,onOpenChange:r,onConfirm:m,sourceName:g,data:d}){const[p,C]=z.useState(""),h=me({resolver:he(Cs),defaultValues:{amount:0,startDate:new Date,endDate:new Date,selectedDays:[],selectedHours:[]}});z.useEffect(()=>{if(i&&d){const o=Number(d.amount??0);C(Fe(String(o))),h.setValue("amount",o);const j=d.startDate?new Date(d.startDate):new Date,P=d.endDate?new Date(d.endDate):new Date;h.setValue("startDate",j),h.setValue("endDate",P),Array.isArray(d.selectedDays)&&h.setValue("selectedDays",d.selectedDays),Array.isArray(d.selectedHours)&&h.setValue("selectedHours",d.selectedHours)}},[i,d,h]);const V=o=>{m==null||m(o),r(!1),h.reset(),C("")},F=o=>{r(o),o||(h.reset(),C(""))},k=o=>{const j=h.getValues("selectedDays"),P=j.includes(o)?j.filter(S=>S!==o):[...j,o];h.setValue("selectedDays",P)},w=o=>{const j=h.getValues("selectedHours"),P=j.includes(o)?j.filter(S=>S!==o):[...j,o];h.setValue("selectedHours",P)};return e.jsx(xe,{open:i,onOpenChange:F,children:e.jsxs(pe,{className:"max-w-2xl lg:max-w-2xl",children:[e.jsx(_e,{children:e.jsxs(ge,{children:["Cấu hình giá theo khung thời gian nguồn ",g]})}),e.jsx(ue,{...h,children:e.jsxs("form",{onSubmit:h.handleSubmit(V),className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-[120px_1fr] items-center gap-4",children:[e.jsx(b,{className:"text-sm font-medium text-gray-600",children:"Số tiền"}),e.jsx(y,{control:h.control,name:"amount",render:({field:o})=>e.jsxs(N,{children:[e.jsx(T,{children:e.jsx(E,{type:"text",placeholder:"0",value:p,onChange:j=>{const P=j.target.value,S=Fe(P);C(S);const A=ks(S);o.onChange(A)},onKeyDown:j=>{!/[0-9]/.test(j.key)&&!["Backspace","Delete","ArrowLeft","ArrowRight","Tab"].includes(j.key)&&j.preventDefault()}})}),e.jsx(I,{})]})})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(b,{className:"text-sm font-medium text-gray-600",children:"Ngày áp dụng"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(y,{control:h.control,name:"startDate",render:({field:o})=>e.jsxs(N,{children:[e.jsx(b,{className:"text-xs text-gray-500",children:"Ngày bắt đầu"}),e.jsx(T,{children:e.jsx(Ie,{date:o.value,onDateChange:o.onChange,placeholder:"Chọn ngày bắt đầu",className:"border-blue-200 bg-blue-50"})}),e.jsx(I,{})]})}),e.jsx(y,{control:h.control,name:"endDate",render:({field:o})=>e.jsxs(N,{children:[e.jsx(b,{className:"text-xs text-gray-500",children:"Ngày kết thúc"}),e.jsx(T,{children:e.jsx(Ie,{date:o.value,onDateChange:o.onChange,placeholder:"Chọn ngày kết thúc",className:"border-blue-200 bg-blue-50"})}),e.jsx(I,{})]})})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(b,{className:"text-sm font-medium text-gray-600",children:"Khung thời gian áp dụng"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(b,{className:"text-xs text-gray-500",children:"Chọn ngày"}),e.jsx(oe,{className:"h-3 w-3 text-gray-400"})]}),e.jsx("div",{className:"grid grid-cols-7 gap-2",children:ws.map(o=>{const j=h.watch("selectedDays").includes(o.value);return e.jsx(D,{type:"button",variant:j?"default":"outline",size:"sm",onClick:()=>k(o.value),className:`w-full ${j?"bg-blue-600 text-white hover:bg-blue-700":"border-blue-200 text-gray-700 hover:bg-blue-50"}`,children:o.label},o.value)})}),e.jsx(I,{})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(b,{className:"text-xs text-gray-500",children:"Chọn giờ"}),e.jsx(oe,{className:"h-3 w-3 text-gray-400"})]}),e.jsx("div",{className:"grid grid-cols-6 gap-2",children:Ss.map(o=>{const j=h.watch("selectedHours").includes(o.value);return e.jsx(D,{type:"button",variant:j?"default":"outline",size:"sm",onClick:()=>w(o.value),className:`w-full ${j?"bg-blue-600 text-white hover:bg-blue-700":"border-blue-200 text-gray-700 hover:bg-blue-50"}`,children:o.label},o.value)})}),e.jsx(I,{})]})]}),e.jsxs(ze,{className:"pt-4",children:[e.jsx(Pe,{asChild:!0,children:e.jsx(D,{type:"button",variant:"outline",children:"Hủy"})}),e.jsx(D,{type:"button",onClick:h.handleSubmit(V),children:"Xác nhận"})]})]})})]})})}const Is=s.object({source_id:s.string().min(1,"Vui lòng chọn nguồn đơn").optional(),price:s.coerce.number().min(0,"Số tiền phải lớn hơn hoặc bằng 0"),price_times:s.array(s.object({price:s.number(),from_date:s.number(),to_date:s.number(),time_sale_date_week:s.number(),time_sale_hour_day:s.number()})).optional()});function Oe({open:i,onOpenChange:r,onConfirm:m,sources:g,data:d}){const[p,C]=z.useState(null),[h,V]=z.useState(!1),[F,k]=z.useState([]),[w,o]=z.useState(null),[j,P]=z.useState(),S=me({resolver:he(Is),defaultValues:{source_id:"",price:0,price_times:[]}});z.useEffect(()=>{if(i&&d){S.setValue("source_id",d.source_id),S.setValue("price",d.price),S.setValue("price_times",d.price_times);const x=g.find(f=>f.sourceId===d.source_id);if(C(x||null),Array.isArray(d.price_times)&&d.price_times.length>0){const f=d.price_times.map(q=>({id:Date.now().toString()+Math.random(),price:q.price,startDate:new Date(q.from_date),endDate:new Date(q.to_date),selectedDays:q.time_sale_date_week?ps(q.time_sale_date_week):[],selectedHours:q.time_sale_hour_day?_s(q.time_sale_hour_day):[]}));k(f)}else k([])}},[i,d,S,g]);const A=x=>{const f={...x,source_name:p==null?void 0:p.sourceName,source_id:p==null?void 0:p.sourceId,is_source_exist_in_city:!0};m==null||m(f),r(!1),S.reset(),C(null)},n=x=>{x.preventDefault(),x.stopPropagation(),S.handleSubmit(A)(x)},c=x=>{r(x),x||(S.reset(),C(null),V(!1),k([]),S.setValue("price_times",[]),o(null),P(void 0))},B=x=>{const f={id:w||Date.now().toString(),price:Number(x.amount||0),startDate:x.startDate,endDate:x.endDate,selectedDays:x.selectedDays,selectedHours:x.selectedHours};k(q=>{let K;w?K=q.map(t=>t.id===w?f:t):K=[...q,f];const $=De(K);return S.setValue("price_times",$),K}),o(null)},X=x=>{k(f=>{const q=f.filter($=>$.id!==x),K=De(q);return S.setValue("price_times",K),q})};return e.jsxs(xe,{open:i,onOpenChange:c,children:[e.jsxs(pe,{className:"max-w-md lg:max-w-2xl",children:[e.jsx(_e,{children:e.jsx(ge,{children:"Cấu hình giá theo nguồn"})}),e.jsx(ue,{...S,children:e.jsxs("form",{onSubmit:n,className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-[120px_1fr] items-center gap-4",children:[e.jsx(b,{className:"text-sm font-medium text-gray-600",children:"Nguồn đơn"}),e.jsx(y,{control:S.control,name:"source_id",render:({field:x})=>e.jsxs(N,{children:[e.jsx(G,{options:g.map(f=>({value:f.sourceId,label:f.sourceName})),value:x.value,onValueChange:f=>{x.onChange(f);const q=g.find(K=>K.sourceId===f);C(q||null)},placeholder:"Chọn nguồn đơn",searchPlaceholder:"Tìm nguồn đơn...",emptyText:"Không có nguồn phù hợp.",className:"w-full"}),e.jsx(I,{})]})})]}),e.jsxs("div",{className:"grid grid-cols-[120px_1fr] items-center gap-4",children:[e.jsx(b,{className:"text-sm font-medium text-gray-600",children:"Số tiền"}),e.jsx(y,{control:S.control,name:"price",render:({field:x})=>e.jsxs(N,{children:[e.jsx(T,{children:e.jsx(E,{type:"text",placeholder:"0",value:x.value,onChange:f=>{x.onChange(f.target.value)},onKeyDown:f=>{!/[0-9]/.test(f.key)&&!["Backspace","Delete","ArrowLeft","ArrowRight","Tab"].includes(f.key)&&f.preventDefault()}})}),e.jsx(I,{})]})})]}),p&&e.jsxs("div",{className:"space-y-4 border-t pt-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Cấu hình giá theo khung thời gian"}),e.jsxs("div",{className:"text-sm leading-relaxed text-gray-600",children:["Giá theo nguồn ",e.jsx("span",{className:"font-semibold text-blue-600",children:p.sourceName})," sẽ được lấy theo số tiền"," ",e.jsxs("span",{className:"font-semibold text-blue-600",children:[S.getValues("price")||"0"," đ"]}),". Khi cấu hình giá theo khung thời gian số tiền sẽ hiển thị theo các khung thời gian cấu hình dưới đây"]}),e.jsx("div",{className:"flex justify-end",children:e.jsx(D,{type:"button",variant:"outline",className:"text-blue-600 hover:text-blue-700",onClick:()=>{o(null),P({amount:Number(S.getValues("price")||0)}),V(!0)},children:"Thêm cấu hình"})})]}),F.length===0?e.jsx("div",{className:"flex min-h-[120px] items-center justify-center rounded-lg border-2 border-dashed border-gray-200 bg-gray-50",children:e.jsxs("div",{className:"text-center text-gray-500",children:[e.jsx("p",{className:"text-sm",children:"Chưa có cấu hình khung thời gian nào"}),e.jsx("p",{className:"mt-1 text-xs",children:'Nhấn "Thêm cấu hình" để bắt đầu'})]})}):e.jsx("div",{className:"space-y-3",children:F.map(x=>{var f;return e.jsxs("div",{className:"flex cursor-pointer items-start gap-3 rounded-lg border border-gray-200 bg-white p-4",onClick:()=>{o(x.id),P({amount:Number(x.price||0),startDate:x.startDate,endDate:x.endDate,selectedDays:x.selectedDays,selectedHours:x.selectedHours}),V(!0)},children:[e.jsx("div",{className:"mt-1 h-2 w-2 rounded-full bg-gray-400"}),e.jsxs("div",{className:"flex-1 space-y-1",children:[e.jsxs("div",{className:"text-sm text-gray-900",children:["Từ ngày ",e.jsx("span",{className:"font-semibold",children:Ve(x.startDate)})," đến ngày"," ",e.jsx("span",{className:"font-semibold",children:Ve(x.endDate)})]}),e.jsxs("div",{className:"text-sm text-gray-900",children:["Giá: ",e.jsxs("span",{className:"font-semibold",children:[((f=x.price)==null?void 0:f.toLocaleString("vi-VN"))||"0"," ₫"]})]}),e.jsxs("div",{className:"text-sm text-gray-900",children:["Khung giờ ",e.jsx("span",{className:"font-semibold",children:xs(x.selectedHours)})," Thứ"," ",e.jsx("span",{className:"font-semibold",children:hs(x.selectedDays)})]})]}),e.jsx(D,{type:"button",variant:"ghost",size:"sm",onClick:()=>X(x.id),className:"h-8 w-8 p-0 text-gray-400 hover:text-gray-600",children:e.jsx(ie,{className:"h-4 w-4"})})]},x.id)})})]}),e.jsxs(ze,{className:"pt-4",children:[e.jsx(Pe,{asChild:!0,children:e.jsx(D,{type:"button",variant:"outline",children:"Hủy"})}),e.jsx(D,{type:"submit",children:"Xác nhận"})]})]})})]}),e.jsx(Ts,{open:h,onOpenChange:V,onConfirm:B,sourceName:(p==null?void 0:p.sourceName)||"",data:j})]})}function Vs({form:i}){var se,te;const r=de(),[m,g,d,p,C,h]=$e({control:i.control,name:["is_virtual_item","is_buffet_item","cross_price","price_by_source","exclude_items_buffet","up_size_buffet"]}),{selectedCustomizationUid:V,customizations:F,customizationDetails:k,items:w,sourcesData:o}=Be({form:i}),{showQuantityInputs:j,isCustomizationDetailsOpen:P,isPriceSourceDialogOpen:S,isBuffetConfigModalOpen:A,setIsCustomizationDetailsOpen:n,setIsPriceSourceDialogOpen:c,setIsBuffetConfigModalOpen:B,openPriceSourceDialog:X,openBuffetConfigModal:x,toggleQuantityInputs:f,handleRemovePriceSource:q,clearConfirmDelete:K,confirmDeleteIndex:$}=gs(),[t,O]=z.useState(null),[ne,Y]=z.useState(null),ae=a=>{const u=i.getValues("price_by_source")||[];if(t!==null){const _=[...u];_[t]={source_id:a.source_id,price:a.price,source_name:a.source_name,price_times:a.price_times,is_source_exist_in_city:a.is_source_exist_in_city},i.setValue("price_by_source",_),O(null),Y(null)}else{const _=[...u,{source_id:a.source_id,price:a.price,source_name:a.source_name,price_times:a.price_times,is_source_exist_in_city:a.is_source_exist_in_city}];i.setValue("price_by_source",_)}},ee=(a,u)=>{O(u),Y(a),c(!0)},le=a=>{i.setValue("exclude_items_buffet",a)};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Cấu hình sửa giá, nhập số lượng, bỏ món"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(b,{className:"text-right font-medium text-gray-700",children:"Cho phép sửa giá khi bán"}),e.jsx("div",{className:"col-span-2",children:e.jsx(y,{control:i.control,name:"enable_edit_price",render:({field:a})=>e.jsx(N,{children:e.jsx(T,{children:e.jsx(U,{checked:(a.value&2)===2,onCheckedChange:u=>{const _=u?a.value|2:a.value&-3;a.onChange(_)}})})})})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(b,{className:"text-right font-medium text-gray-700",children:"Yêu cầu nhập số lượng khi gọi món"}),e.jsx("div",{className:"col-span-2",children:e.jsx(y,{control:i.control,name:"enable_edit_price",render:({field:a})=>e.jsx(N,{children:e.jsx(T,{children:e.jsx(U,{checked:(a.value&4)===4,onCheckedChange:u=>{const _=u?a.value|4:a.value&-5;a.onChange(_)}})})})})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(b,{className:"text-left font-medium text-gray-700",children:"Cho phép bỏ món mà không cần quyền áp dụng"}),e.jsx("div",{className:"col-span-2",children:e.jsx(y,{control:i.control,name:"enable_edit_price",render:({field:a})=>e.jsx(N,{children:e.jsx(T,{children:e.jsx(U,{checked:(a.value&8)===8,onCheckedChange:u=>{const _=u?a.value|8:a.value&-9;a.onChange(_)}})})})})})]})]}),e.jsxs("div",{className:"space-y-4 border-t pt-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(b,{className:"text-right font-medium text-gray-700",children:"Cấu hình món ảo"}),e.jsx("div",{className:"col-span-2",children:e.jsx(y,{control:i.control,name:"is_virtual_item",render:({field:a})=>e.jsx(N,{children:e.jsx(T,{children:e.jsx(U,{checked:!!a.value,onCheckedChange:u=>a.onChange(u?1:0)})})})})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(b,{className:"text-right font-medium text-gray-700",children:"Cấu hình món ăn là vé buffet"}),e.jsx("div",{className:"col-span-2",children:e.jsx(y,{control:i.control,name:"is_buffet_item",render:({field:a})=>e.jsx(N,{children:e.jsx(T,{children:e.jsx(U,{checked:!!a.value,onCheckedChange:u=>a.onChange(u?1:0)})})})})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(b,{className:"text-right font-medium text-gray-700",children:"Công thức inQr cho máy pha trà"}),e.jsx("div",{className:"col-span-2",children:e.jsx(y,{control:i.control,name:"formula_qrcode",render:({field:a})=>e.jsxs(N,{children:[e.jsx(T,{children:e.jsx(E,{placeholder:"Nhập công thức InQR",...a})}),e.jsx(I,{})]})})})]}),g===1&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(b,{className:"text-right font-medium text-gray-700",children:"Danh sách món không đi kèm vé buffet"}),e.jsx("div",{className:"col-span-2",children:e.jsxs(D,{type:"button",variant:"outline",className:"w-full justify-start text-blue-600",onClick:x,children:[C.length," món"]})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(b,{className:"text-right font-medium text-gray-700",children:"Danh sách vé buffet được upsize"}),e.jsx("div",{className:"col-span-2",children:e.jsxs(D,{type:"button",variant:"outline",className:"w-full justify-start text-blue-600",onClick:()=>{},children:[h.length," món"]})})]})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Cấu hình món dịch vụ"}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(b,{className:"text-right font-medium text-gray-700",children:"Cấu hình món dịch vụ"}),e.jsx("div",{className:"col-span-2",children:e.jsx(y,{control:i.control,name:"is_service",render:({field:a})=>e.jsx(N,{children:e.jsx("div",{className:"flex items-center gap-4",children:e.jsx(T,{children:e.jsx(U,{checked:!!a.value,onCheckedChange:u=>a.onChange(!!u)})})})})})})]}),e.jsxs("div",{className:"space-y-4",children:[i.watch("is_service")&&e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Cấu hình giá thay đổi theo số lượng"}),e.jsx(Ee,{children:e.jsxs(Ke,{children:[e.jsx(Le,{asChild:!0,children:e.jsx(oe,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(Ue,{children:e.jsx("p",{children:"Nếu khai báo cấu hình đổi giá theo số lượng thì món sẽ ko tự động áp dụng giá và giảm giá theo thời gian khi chạy của món dịch vụ nữa, chỉ được chọn 1 trong 2"})})]})})]}),e.jsx(D,{type:"button",size:"sm",variant:j||d.length>0?"outline":"default",className:j||d.length>0?"":"bg-blue-500 hover:bg-blue-600",onClick:f,children:j||d.length>0?"Xoá":"Thêm"})]}),(j||d.length>0)&&e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(b,{className:"text-right font-medium text-gray-700",children:"Khai báo số lượng"}),e.jsx("div",{className:"col-span-2",children:e.jsx(y,{control:i.control,name:"quantity",render:({field:a})=>e.jsxs(N,{children:[e.jsx(T,{children:e.jsx(E,{type:"number",placeholder:"1",className:"bg-white",...a,value:a.value||"",onChange:u=>{const _=Number(u.target.value)||0;a.onChange(_);const v=i.getValues("price")||0;i.setValue("cross_price",[{quantity:_,price:v}])}})}),e.jsx(I,{})]})})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(b,{className:"text-right font-medium text-gray-700",children:"Khai báo giá khi vượt qua số lượng trên"}),e.jsx("div",{className:"col-span-2",children:e.jsx(y,{control:i.control,name:"price",render:({field:a})=>e.jsxs(N,{children:[e.jsx(T,{children:e.jsx(E,{placeholder:"0",className:"bg-white",value:a.value||"",onChange:u=>{const _=Number(u.target.value)||0;a.onChange(_);const v=i.getValues("quantity")||0;i.setValue("cross_price",[{quantity:v,price:_}])}})}),e.jsx(I,{})]})})})]})]})})]}),!m&&e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Cấu hình giá theo nguồn"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(b,{className:"font-medium text-gray-700",children:"Cấu hình giá theo nguồn"}),e.jsx(D,{type:"button",size:"sm",className:"bg-blue-500 hover:bg-blue-600",onClick:X,children:"Thêm nguồn"})]}),p.length>0&&e.jsx("div",{className:"space-y-2",children:p.map((a,u)=>e.jsxs("div",{className:"flex cursor-pointer items-center justify-between rounded-md border p-3 hover:bg-gray-50",onClick:()=>ee(a,u),children:[e.jsx("div",{children:e.jsxs("span",{className:"font-medium",children:[us(a.source_id,o)||a.sourceName," - Số tiền:"," ",a.price.toLocaleString()," ₫"]})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(D,{type:"button",variant:"ghost",size:"sm",onClick:_=>{_.stopPropagation(),ee(a,u)},className:"text-blue-600 hover:text-blue-700",children:"Sửa"}),e.jsx(D,{type:"button",variant:"ghost",size:"sm",onClick:_=>{_.stopPropagation(),q(u)},className:"text-gray-400 hover:text-gray-600",children:e.jsx(ie,{className:"h-4 w-4"})})]})]},u))})]})]}),i.watch("city_uid")&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Customization"}),e.jsx(D,{type:"button",size:"sm",className:"bg-blue-500 hover:bg-blue-600",onClick:()=>r({to:"/menu/customization/customization-in-city/detail"}),children:"Tạo customization"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(b,{className:"text-right font-medium text-gray-700",children:"Customization"}),e.jsx("div",{className:"col-span-2",children:e.jsx(y,{control:i.control,name:"customization_uid",render:({field:a})=>e.jsxs(N,{children:[e.jsx(G,{options:[{value:"none",label:"Không có"},...F.map(u=>({value:u.id,label:u.name}))],value:a.value??"none",onValueChange:u=>a.onChange(u==="none"?null:u),placeholder:"Chọn customization",searchPlaceholder:"Tìm kiếm customization...",emptyText:"Không tìm thấy customization.",className:"w-full"}),e.jsx(I,{})]})})})]}),V&&V!=="none"&&k&&e.jsx("div",{className:"space-y-4",children:e.jsxs(ls,{open:P,onOpenChange:n,children:[e.jsx(rs,{asChild:!0,children:e.jsxs(D,{type:"button",variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsx("span",{className:"text-blue-600",children:"Chi tiết Customize"}),P?e.jsx(ds,{className:"h-4 w-4 text-blue-600"}):e.jsx(ms,{className:"h-4 w-4 text-blue-600"})]})}),e.jsx(cs,{className:"space-y-4",children:(te=(se=k.data)==null?void 0:se.LstItem_Options)==null?void 0:te.map(a=>{const u=a.LstItem_Id.map(_=>{const v=w.find(L=>L.item_id===_);return{id:(v==null?void 0:v.id)||_,name:(v==null?void 0:v.item_name)||_,price:(v==null?void 0:v.ots_price)||0,code:_,active:(v==null?void 0:v.active)??1}});return e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:a.Name}),e.jsxs("span",{className:"ml-2 text-sm text-gray-500",children:["(Chọn từ ",a.Min_Permitted," đến ",a.Max_Permitted," món)"]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4",children:u.map(_=>{const v=_.active===1;return e.jsxs("div",{className:`rounded-md border p-3 text-center ${v?"bg-gray-50":"cursor-not-allowed bg-gray-100 opacity-50"}`,children:[e.jsx("p",{className:`text-sm font-medium ${v?"":"text-gray-400"}`,children:_.name}),e.jsxs("p",{className:"mt-1 text-xs text-gray-500",children:["(",_.code,")"]}),e.jsxs("p",{className:`mt-1 text-sm font-medium ${v?"text-green-600":"text-gray-400"}`,children:[_.price.toLocaleString("vi-VN",{minimumFractionDigits:0,maximumFractionDigits:0})," ","₫"]})]},_.id)})})]},a.id)})})]})})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(b,{className:"text-sm font-medium text-gray-700",children:"Khung thời gian bán"}),e.jsx(y,{control:i.control,name:"time_sale_date_week",render:({field:a})=>e.jsxs(N,{children:[e.jsx(b,{className:"text-sm text-gray-600",children:"Chọn ngày"}),e.jsx("div",{className:"grid grid-cols-7 gap-2",children:[{name:"Thứ 2",bit:4},{name:"Thứ 3",bit:8},{name:"Thứ 4",bit:16},{name:"Thứ 5",bit:32},{name:"Thứ 6",bit:64},{name:"Thứ 7",bit:128},{name:"Chủ nhật",bit:2}].map(({name:u,bit:_})=>{const v=_,L=(a.value&v)!==0;return e.jsx(D,{type:"button",variant:L?"default":"outline",size:"sm",className:`text-xs ${L?"bg-blue-600 text-white":"border-blue-600 text-blue-600"}`,onClick:()=>{const W=a.value||0,J=L?W&~v:W|v;a.onChange(J)},children:u},u)})}),e.jsx(I,{})]})}),e.jsx(y,{control:i.control,name:"time_sale_hour_day",render:({field:a})=>e.jsxs(N,{children:[e.jsx(b,{className:"text-sm text-gray-600",children:"Chọn giờ"}),e.jsx("div",{className:"grid grid-cols-10 gap-2",children:Array.from({length:24},(u,_)=>({hour:_,label:`${_}h`})).map(({hour:u,label:_})=>{const v=1<<u,L=(a.value&v)!==0;return e.jsx(D,{type:"button",variant:L?"default":"outline",size:"sm",className:`text-xs ${L?"bg-blue-600 text-white":"border-blue-600 text-blue-600"}`,onClick:()=>{const W=a.value||0,J=L?W&~v:W|v;a.onChange(J)},children:_},u)})}),e.jsx(I,{})]})})]}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(b,{className:"text-right font-medium text-gray-700",children:"Thứ tự hiển thị"}),e.jsx("div",{className:"col-span-2",children:e.jsx(y,{control:i.control,name:"sort",render:({field:a})=>e.jsxs(N,{children:[e.jsx(T,{children:e.jsx(E,{type:"number",placeholder:"Nhập số thứ tự hiển thị",...a,onChange:u=>a.onChange(Number(u.target.value))})}),e.jsx(I,{})]})})})]})})]})]}),e.jsx(Oe,{open:S,onOpenChange:c,onConfirm:ae,sources:o,data:ne}),e.jsx(Xe,{itemsBuffet:C||[],open:A,onOpenChange:B,onItemsChange:le,items:w}),e.jsx(os,{open:$!==null,onOpenChange:a=>!a&&K(),title:"Bạn có muốn bỏ cấu hình ?",desc:"",confirmText:"Xóa",cancelBtnText:"Hủy",handleConfirm:()=>{if($===null)return;const u=(i.getValues("price_by_source")||[]).filter((_,v)=>v!==$);i.setValue("price_by_source",u),K()}})]})}function Ds({form:i,itemTypes:r,itemClasses:m,units:g,cities:d,onImageChange:p,imagePreview:C,onImageRemove:h}){return e.jsxs("div",{className:"space-y-6",children:[e.jsx(Ns,{form:i,itemTypes:r,itemClasses:m,units:g,cities:d,onImageChange:p,imagePreview:C,onImageRemove:h}),e.jsx(Vs,{form:i})]})}const Fs=bs;function pt({currentRow:i,isCopyMode:r=!1}){var u,_,v,L,W,J,be,je,ye,fe,ve,Ne,Ce,ke,we,Se;const m=!!i&&!r,g=!!i&&r,d=de(),[p,C]=z.useState(!1),[h,V]=z.useState(null),[F,k]=z.useState(null),{company:w,selectedBrand:o,sourcesData:j,cities:P,itemTypes:S,units:A,itemClasses:n}=Be(),{createItemAsync:c,isPending:B}=We(),{updateItemAsync:X,isPending:x}=Ge(),{updateStatusAsync:f,isPending:q}=Ye(),{uploadImage:K,isUploading:$}=Qe(),t=i;z.useEffect(()=>{t!=null&&t.image_path&&!g&&V(t.image_path)},[t==null?void 0:t.image_path,g]);const O=me({resolver:he(Fs),defaultValues:{item_name:(t==null?void 0:t.item_name)||"",ots_price:Number(t==null?void 0:t.ots_price)||0,description:(t==null?void 0:t.description)||"",item_id_barcode:g?"":(t==null?void 0:t.item_id_barcode)||"",is_eat_with:Number(t==null?void 0:t.is_eat_with)||0,is_featured:Number(t==null?void 0:t.is_featured)||0,item_class_uid:(t==null?void 0:t.item_class_uid)||"",item_type_uid:(t==null?void 0:t.item_type_uid)||"",city_uid:(t==null?void 0:t.city_uid)||"",item_id:g?"":(t==null?void 0:t.item_id)||"",enable_custom_item_id:m&&t!=null&&t.item_id&&(t==null?void 0:t.item_id).trim()!==""?1:0,unit_uid:(t==null?void 0:t.unit_uid)||"",unit_secondary_uid:(t==null?void 0:t.unit_secondary_uid)||"",ots_tax:Number(t==null?void 0:t.ots_tax)||0,time_cooking:Number(t==null?void 0:t.time_cooking)||0,ta_price:Number(t==null?void 0:t.ta_price)||0,ta_tax:Number(t==null?void 0:t.ta_tax)||0,item_id_mapping:(t==null?void 0:t.item_id_mapping)||"",enable_edit_price:Number((u=t==null?void 0:t.extra_data)==null?void 0:u.enable_edit_price)||0,is_print_label:Number(t==null?void 0:t.is_print_label)||0,is_allow_discount:Number(t==null?void 0:t.is_allow_discount)||0,is_virtual_item:Number((_=t==null?void 0:t.extra_data)==null?void 0:_.is_virtual_item)||0,is_item_service:Number((v=t==null?void 0:t.extra_data)==null?void 0:v.is_item_service)||0,is_buffet_item:Number((L=t==null?void 0:t.extra_data)==null?void 0:L.is_buffet_item)||0,no_update_quantity_toping:Number((W=t==null?void 0:t.extra_data)==null?void 0:W.no_update_quantity_toping)||0,formula_qrcode:((J=t==null?void 0:t.extra_data)==null?void 0:J.formula_qrcode)||"",is_service:Number(t==null?void 0:t.is_service)||0,price_by_source:((be=t==null?void 0:t.extra_data)==null?void 0:be.price_by_source)||[],exclude_items_buffet:((je=t==null?void 0:t.extra_data)==null?void 0:je.exclude_items_buffet)||[],up_size_buffet:((ye=t==null?void 0:t.extra_data)==null?void 0:ye.up_size_buffet)||[],cross_price:((fe=t==null?void 0:t.extra_data)==null?void 0:fe.cross_price)||[],quantity:((Ce=(Ne=(ve=t==null?void 0:t.extra_data)==null?void 0:ve.cross_price)==null?void 0:Ne[0])==null?void 0:Ce.quantity)||0,price:((Se=(we=(ke=t==null?void 0:t.extra_data)==null?void 0:ke.cross_price)==null?void 0:we[0])==null?void 0:Se.price)||0,time_sale_date_week:Number(t==null?void 0:t.time_sale_date_week)||0,time_sale_hour_day:Number(t==null?void 0:t.time_sale_hour_day)||0,sort:Number(t==null?void 0:t.sort)||0,customization_uid:(t==null?void 0:t.customization_uid)||null,item_color:(t==null?void 0:t.item_color)||""}}),ne=l=>{var M;const H=(M=l.target.files)==null?void 0:M[0];if(H){k(H);const Z=new FileReader;Z.onload=re=>{var ce;V((ce=re.target)==null?void 0:ce.result)},Z.readAsDataURL(H)}},Y=async l=>{if(!(!(w!=null&&w.id)||!(o!=null&&o.id)))try{let H="",M="",Z=l.item_color||"";if(F){const Q=await K(F);Q&&(H=Q.image_path,M=Q.image_path_thumb,Z="")}else h&&!g&&(H=(t==null?void 0:t.image_path)||"",M=(t==null?void 0:t.image_path_thumb)||"");const re={price_by_source:(l.price_by_source||[]).map(Q=>({price:Number(Q.price??Q.amount??0),source_id:Q.source_id||"",price_times:Q.price_times||[],is_source_exist_in_city:Q.is_source_exist_in_city??!0})),is_virtual_item:l.is_virtual_item,is_item_service:l.is_item_service,no_update_quantity_toping:l.no_update_quantity_toping,enable_edit_price:l.enable_edit_price,is_buffet_item:l.is_buffet_item,exclude_items_buffet:l.exclude_items_buffet||[],up_size_buffet:l.up_size_buffet||[],cross_price:l.cross_price||[],formula_qrcode:l.formula_qrcode||""},Ae={item_id:m?l.enable_custom_item_id&&l.item_id?l.item_id:(t==null?void 0:t.item_id)||(i==null?void 0:i.item_id)||"":g?l.enable_custom_item_id&&l.item_id&&l.item_id.trim()!==""?l.item_id:R():l.enable_custom_item_id&&l.item_id?l.item_id:R(),item_name:l.item_name,description:l.description||"",ots_price:l.ots_price,ots_tax:l.ots_tax,ta_price:l.ta_price,ta_tax:l.ta_tax,time_sale_hour_day:l.time_sale_hour_day,time_sale_date_week:l.time_sale_date_week,allow_take_away:l.allow_take_away,is_eat_with:l.is_eat_with?1:0,image_path:H,image_path_thumb:M,item_color:Z,list_order:l.list_order,is_service:l.is_service?1:0,is_material:l.is_material,active:1,user_id:(t==null?void 0:t.user_id)||"",is_foreign:0,quantity_default:l.quantity_default,price_change:l.price_change,currency_type_id:(t==null?void 0:t.currency_type_id)||"",point:l.point,is_gift:l.is_gift,is_fc:l.is_fc,show_on_web:l.show_on_web,show_price_on_web:l.show_price_on_web,cost_price:l.cost_price,is_print_label:l.is_print_label?1:0,quantity_limit:l.quantity_limit,is_kit:l.is_kit,process_index:l.process_index,quantity_per_day:l.quantity_per_day,is_parent:l.is_parent,is_sub:l.is_sub,effective_date:l.effective_date,expire_date:l.expire_date,time_cooking:l.time_cooking,item_id_barcode:l.item_id_barcode||"",is_allow_discount:l.is_allow_discount?1:0,item_id_eat_with:(t==null?void 0:t.item_id_eat_with)||"",item_id_mapping:l.item_id_mapping||"",unit_uid:l.unit_uid,unit_secondary_uid:l.unit_secondary_uid||"",item_class_uid:l.item_class_uid||"",item_type_uid:l.item_type_uid,city_uid:l.city_uid,sort:l.sort,company_uid:w.id,brand_uid:o.id,sort_online:l.sort_online,customization_uid:l.customization_uid||null,extra_data:re},Te={...m?{id:(i==null?void 0:i.id)||""}:{},...Ae};m&&(i!=null&&i.id)?(await X(Te),d({to:"/menu/items/items-in-city"})):(await c(Te),d({to:"/menu/items/items-in-city"}))}catch(H){console.error("Error submitting form:",H)}},ae=async()=>{if(!(!(i!=null&&i.id)||!(w!=null&&w.id)||!(o!=null&&o.id)))try{await f({id:i.id,active:0}),d({to:"/menu/items/items-in-city"})}catch{}},ee=async()=>{if(!(!(i!=null&&i.id)||!(w!=null&&w.id)||!(o!=null&&o.id)))try{await f({id:i.id,active:1}),d({to:"/menu/items/items-in-city"})}catch{}},le=async()=>{if(!m){const H=O.getValues("enable_custom_item_id"),M=O.getValues("item_id");!H&&(!M||String(M).trim()==="")&&O.setValue("item_id",R(),{shouldDirty:!0})}await O.trigger()&&O.handleSubmit(Y)()},se=async()=>{if(!m){const H=O.getValues("enable_custom_item_id"),M=O.getValues("item_id");!H&&(!M||String(M).trim()==="")&&O.setValue("item_id",R(),{shouldDirty:!0})}await O.trigger()&&O.handleSubmit(Y)()},te=l=>{const M=[...O.getValues("price_by_source")||[],{source_name:l.source_name,source_id:l.source_id,price:l.price,price_times:l.price_times||[],is_source_exist_in_city:l.is_source_exist_in_city??!0}];O.setValue("price_by_source",M),C(!1)},a=B||x||q||$;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(js,{isUpdate:m,isCopy:g,currentRow:i,isLoading:a,onSave:le,onSaveAndSync:se,onDeactive:m?ae:void 0,onActive:m?ee:void 0,isDeactivating:q,isActivating:q}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"bg-white",children:e.jsx(ue,{...O,children:e.jsx("form",{onSubmit:O.handleSubmit(Y),className:"space-y-6",children:e.jsx(Ds,{form:O,itemTypes:S,itemClasses:n,units:A,cities:P,imageFile:F,onImageChange:ne,imagePreview:h,onImageRemove:()=>{V(null),k(null)}})})})})})]}),e.jsx(Oe,{open:p,onOpenChange:C,onConfirm:te,sources:j})]})}export{pt as I};
