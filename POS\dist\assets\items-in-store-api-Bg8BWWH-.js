import{utils as _,write as y}from"./xlsx-DkH2s96g.js";import{b as d}from"./pos-api-D5WM5mnz.js";const w=t=>{if(!t)return null;if(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(t))return t;try{const e=localStorage.getItem("pos_cities_data");if(e){const n=JSON.parse(e).find(o=>o.city_name===t);return(n==null?void 0:n.id)||null}}catch{return null}return null},s=new Map,u=new Map,f=10*60*1e3;function c(t){return typeof t=="object"&&t!==null&&"response"in t}function h(t){const a=_.json_to_sheet(t),e=_.book_new();_.book_append_sheet(e,a,"Items");const i=y(e,{bookType:"xlsx",type:"array"});return new Blob([i],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"})}const g={getItemsInStore:async t=>{const a=t.active!==void 0?t.active.toString():"undefined",e=t.skip_limit?"true":"false",i=`${t.company_uid}-${t.brand_uid}-${t.page||1}-${t.store_uid||"all"}-${t.item_type_uid||"all"}-${t.time_sale_date_week||""}-${a}-${t.reverse||0}-${t.search||""}-${t.limit||50}-${e}-${t.apply_with_store??-1}`,n=s.get(i);if(n&&Date.now()-n.timestamp<f)return n.data;const o=u.get(i);if(o)return o;const l=(async()=>{try{const r=new URLSearchParams;r.append("company_uid",t.company_uid),r.append("brand_uid",t.brand_uid),t.page&&r.append("page",t.page.toString()),t.item_type_uid&&r.append("item_type_uid",t.item_type_uid),t.store_uid&&r.append("store_uid",t.store_uid),t.time_sale_date_week&&r.append("time_sale_date_week",t.time_sale_date_week),t.reverse!==void 0&&r.append("reverse",t.reverse.toString()),t.search&&r.append("search",t.search),t.active!==void 0&&r.append("active",t.active.toString()),t.limit&&r.append("limit",t.limit.toString()),t.skip_limit&&r.append("skip_limit","true"),t.apply_with_store!==void 0&&r.append("apply_with_store",t.apply_with_store.toString());const p=await d.get(`/mdata/v1/items?${r.toString()}`,{headers:{Accept:"application/json, text/plain, */*","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"}});if(!p.data||typeof p.data!="object")throw new Error("Invalid response format from items in store API");const m=p.data;return s.set(i,{data:m,timestamp:Date.now()}),m}finally{u.delete(i)}})();return u.set(i,l),l},deleteItemInStore:async t=>{var a;try{const e=new URLSearchParams;e.append("company_uid",t.company_uid),e.append("brand_uid",t.brand_uid),e.append("id",t.id),await d.delete(`/mdata/v1/item?${e.toString()}`),s.clear()}catch(e){throw c(e)&&((a=e.response)==null?void 0:a.status)===404?new Error("Item not found."):e}},deleteMultipleItemsInStore:async t=>{var a;try{const e=new URLSearchParams;e.append("company_uid",t.company_uid),e.append("brand_uid",t.brand_uid),e.append("list_item_uid",t.list_item_uid.join(",")),await d.delete(`/mdata/v1/items?${e.toString()}`),s.clear()}catch(e){throw c(e)&&((a=e.response)==null?void 0:a.status)===404?new Error("Items not found."):e}},downloadTemplate:async t=>{var o;const a=t.city_uid&&t.city_uid!=="all"?w(t.city_uid):null,e=new URLSearchParams({skip_limit:"true",company_uid:t.company_uid,brand_uid:t.brand_uid,...a&&{city_uid:a},...t.item_type_uid&&t.item_type_uid!=="all"&&{item_type_uid:t.item_type_uid},...t.active&&t.active!=="all"&&{active:t.active}}),i=await d.get(`/mdata/v1/items?${e}`),n=Array.isArray((o=i.data)==null?void 0:o.data)?i.data.data:[];return h(n)},createItemInStore:async t=>{var a,e;try{const i=await d.post("/mdata/v1/item",t);return s.clear(),i.data.data||i.data}catch(i){throw c(i)&&((a=i.response)==null?void 0:a.status)===400?new Error(((e=i.response.data)==null?void 0:e.message)||"Invalid data provided."):i}},updateItemInStore:async t=>{var a,e;try{const i=await d.put("/mdata/v1/item",t,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return s.clear(),i.data.data||i.data}catch(i){throw c(i)&&((a=i.response)==null?void 0:a.status)===400?new Error(((e=i.response.data)==null?void 0:e.message)||"Invalid data provided."):i}},getItemByListId:async t=>{var a,e;try{const i=new URLSearchParams({skip_limit:"true",company_uid:t.company_uid,brand_uid:t.brand_uid,is_all:"true",list_item_id:t.list_item_id}),n=await d.get(`/mdata/v1/items?${i}`,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4}),o=Array.isArray((a=n.data)==null?void 0:a.data)?n.data.data:[];if(!o.length)throw new Error("Item not found");return{data:o[0]}}catch(i){throw c(i)&&((e=i.response)==null?void 0:e.status)===404?new Error("Item not found."):i}},getItemById:async t=>{var a;try{const e=new URLSearchParams;e.append("id",t.id),t.company_uid&&e.append("company_uid",t.company_uid),t.brand_uid&&e.append("brand_uid",t.brand_uid);const i=await d.get(`/mdata/v1/item?${e.toString()}`);if(!i.data||typeof i.data!="object")throw new Error("Invalid response format from item detail API");return i.data}catch(e){throw c(e)&&((a=e.response)==null?void 0:a.status)===404?new Error("Item not found."):e}},importItems:async t=>(await d.post("/mdata/v1/items/import",{company_uid:t.company_uid,brand_uid:t.brand_uid,items:t.items})).data,updateItemStatus:async t=>{var a,e;try{const n={...(await g.getItemById({id:t.id})).data,active:t.active,company_uid:t.company_uid,brand_uid:t.brand_uid},o=await d.put("/mdata/v1/item",n,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});return s.clear(),o.data.data||o.data}catch(i){throw c(i)&&((a=i.response)==null?void 0:a.status)===400?new Error(((e=i.response.data)==null?void 0:e.message)||"Invalid data provided."):i}},bulkUpdateItemsInStore:async t=>{const a=await d.put("/mdata/v1/items",t);return a.data.data||a.data},bulkCreateItemsInStore:async t=>{const a=await d.post("/mdata/v1/items",t);return a.data.data||a.data},cloneMenu:async t=>{const a=await d.post("/mdata/v1/clone-menu",t);return a.data.data||a.data},clearCache:()=>{s.clear(),u.clear()},getCacheStats:()=>({cacheSize:s.size,pendingRequests:u.size})};export{g as i};
