import{h as P,i as K,e as M,r as o,z as d,j as e,B as g,a4 as h}from"./index-C21OP4ex.js";import{u as A,F as V,a as u,b as x,c as b,d as v,e as p}from"./form-usWdQ_Nt.js";import{s as B}from"./zod-B4gLZVLM.js";import{u as $}from"./useCanGoBack-CxRJAjVz.js";import{g as z}from"./error-utils-CxWrwrxK.js";import"./pos-api-D5WM5mnz.js";import{u as G}from"./use-devices-Cvy813N4.js";import{u as X}from"./use-stores-BQdEFBhG.js";import"./vietqr-api-ruJT0-tj.js";import"./user-CgNoQQSj.js";import"./crm-api-BUMUQ8t4.js";import"./date-range-picker-B1pgj5D_.js";import{I as U}from"./input-4sMIt001.js";import{S,a as C,b as I,c as D,d as w}from"./select-B8Pw9rS-.js";import{X as Y}from"./calendar-BiBi2kQF.js";import"./useQuery-BNGphiae.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bh5DVQPI.js";import"./query-keys-3lmd-xp6.js";import"./stores-api-BIve2jSO.js";import"./createLucideIcon-CL0CQOA1.js";import"./chevron-right-BAjIoZMb.js";import"./react-icons.esm-DB-kGUq7.js";import"./popover-BtedB187.js";import"./index-Ct3V_iCU.js";import"./isSameMonth-C8JQo-AN.js";import"./index-Bh-UeytL.js";import"./index-DuT2Ibxp.js";import"./check-DcHT8QEO.js";const q=[{value:"POS",label:"POS"},{value:"POS_MINI",label:"POS MINI"},{value:"PDA",label:"PDA"},{value:"KDS",label:"KDS"},{value:"KDS_ORDER_CONTROL",label:"KDS ORDER CONTROL"},{value:"KDS_MAKER",label:"KDS MAKER"},{value:"SELF_ORDER",label:"SELF ORDER"}],H=d.object({deviceName:d.string().min(1,"Tên thiết bị là bắt buộc"),storeId:d.string().min(1,"Điểm bán hàng là bắt buộc"),deviceType:d.string().min(1,"Loại thiết bị là bắt buộc")});function J(){const m=P(),y=K(),E=$(),l=M({strict:!1}),[j,f]=o.useState(!1),[n,R]=o.useState(null),[c,T]=o.useState(null),{data:a,isLoading:_}=X(),O=G(),r=A({resolver:B(H),defaultValues:{deviceName:"",storeId:l.store_uid||"",deviceType:"POS"}});o.useEffect(()=>{l.store_uid&&l.store_uid!==r.getValues("storeId")&&r.setValue("storeId",l.store_uid)},[l.store_uid,r]);const N=async s=>{f(!0);try{const t=a==null?void 0:a.find(L=>L.id===s.storeId);if(!t)throw new Error("Store not found");const i=await O.mutateAsync({deviceName:s.deviceName,storeId:s.storeId,deviceType:s.deviceType,brandUid:t.brandId});i&&"device_code"in i?(R(i.device_code),T(i),h.success(`Thiết bị "${s.deviceName}" đã được tạo thành công!`)):(h.success(`Thiết bị "${s.deviceName}" đã được tạo thành công!`),m({to:"/devices/list"}))}catch(t){const i=z(t);h.error(i)}finally{f(!1)}},F=()=>{if(E){y.history.back();return}},k=()=>{if(c&&typeof c=="object"&&"id"in c&&"storeId"in c){const{id:s,storeId:t}=c;m({to:"/devices/detail/$id",params:{id:s},search:{store_uid:t}})}else m({to:"/devices/list"})};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"mb-4 flex items-start justify-between",children:[e.jsx(g,{variant:"ghost",size:"sm",onClick:F,className:"flex items-center",children:e.jsx(Y,{className:"h-4 w-4"})}),e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"mb-2 text-3xl font-medium",children:"Tạo thiết bị"}),e.jsx("div",{className:"text-muted-foreground text-sm",children:"Cài đặt thiết bị > Xuất mã thiết bị"})]}),e.jsx(g,{type:n?"button":"submit",disabled:j,className:"min-w-[100px]",onClick:n?k:r.handleSubmit(N),children:n?"Cấu hình thiết bị":j?"Đang tạo...":"Lưu"})]})}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"p-6",children:n?e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"mb-4 text-gray-600",children:"Sử dụng mã thiết bị này nhập vào thiết bị của bạn để có thể khởi chạy ứng dụng!"}),e.jsx("div",{className:"mx-auto max-w-md rounded-lg border-2 border-blue-200 bg-blue-50 p-6",children:e.jsx("div",{className:"text-center",children:e.jsx("div",{className:"rounded bg-white p-4 font-mono text-2xl font-bold text-blue-600 shadow-sm",children:n})})})]}):e.jsxs(e.Fragment,{children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Chi tiết"}),e.jsx(V,{...r,children:e.jsxs("form",{onSubmit:r.handleSubmit(N),className:"space-y-4",children:[e.jsx(u,{control:r.control,name:"deviceName",render:({field:s})=>e.jsx(x,{children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(b,{className:"w-40 flex-shrink-0 text-sm font-medium",children:"Tên thiết bị *"}),e.jsxs("div",{className:"flex-1",children:[e.jsx(v,{children:e.jsx(U,{placeholder:"Nhập tên thiết bị",...s,className:"w-full"})}),e.jsx(p,{})]})]})})}),e.jsx(u,{control:r.control,name:"storeId",render:({field:s})=>{var t;return e.jsx(x,{children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(b,{className:"w-40 flex-shrink-0 text-sm font-medium",children:"Điểm bán hàng *"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs(S,{onValueChange:s.onChange,value:s.value,disabled:_,children:[e.jsx(v,{children:e.jsx(C,{className:"w-full",children:e.jsx(I,{placeholder:"Chọn điểm áp dụng"})})}),e.jsx(D,{children:(t=a==null?void 0:a.filter(i=>i.isActive))==null?void 0:t.map(i=>e.jsx(w,{value:i.id,children:i.name},i.id))})]}),e.jsx(p,{})]})]})})}}),e.jsx(u,{control:r.control,name:"deviceType",render:({field:s})=>e.jsx(x,{children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(b,{className:"w-40 flex-shrink-0 text-sm font-medium",children:"Loại thiết bị *"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs(S,{onValueChange:s.onChange,value:s.value,children:[e.jsx(v,{children:e.jsx(C,{className:"w-full",children:e.jsx(I,{placeholder:"Chọn loại thiết bị"})})}),e.jsx(D,{children:q.map(t=>e.jsx(w,{value:t.value,children:t.label},t.value))})]}),e.jsx(p,{})]})]})})})]})})]})})})]})}const we=J;export{we as component};
