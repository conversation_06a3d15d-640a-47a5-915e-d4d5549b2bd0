import{z as l,j as e,L as x,B as h}from"./index-C21OP4ex.js";import{C as u}from"./content-section-Cad-ERh8.js";import{u as j,F as f,a as r,b as a,c as n,d as o,e as p,g as t}from"./form-usWdQ_Nt.js";import{s as b}from"./zod-B4gLZVLM.js";import{C as y}from"./checkbox-DUpnJ1Rx.js";import{R as g,a as m}from"./radio-group-DJk2nTDf.js";import{S as c}from"./switch-Drz2_0hu.js";import"./separator-ZoxOB1XH.js";import"./index-DuT2Ibxp.js";import"./check-DcHT8QEO.js";import"./createLucideIcon-CL0CQOA1.js";import"./index-UJ-79IIJ.js";import"./index-Bh-UeytL.js";const N=l.object({type:l.enum(["all","mentions","none"],{required_error:"You need to select a notification type."}),mobile:l.boolean().default(!1).optional(),communication_emails:l.boolean().default(!1).optional(),social_emails:l.boolean().default(!1).optional(),marketing_emails:l.boolean().default(!1).optional(),security_emails:l.boolean()}),v={communication_emails:!1,marketing_emails:!1,social_emails:!0,security_emails:!0};function C(){const i=j({resolver:b(N),defaultValues:v});function d(s){}return e.jsx(f,{...i,children:e.jsxs("form",{onSubmit:i.handleSubmit(d),className:"space-y-8",children:[e.jsx(r,{control:i.control,name:"type",render:({field:s})=>e.jsxs(a,{className:"relative space-y-3",children:[e.jsx(n,{children:"Notify me about..."}),e.jsx(o,{children:e.jsxs(g,{onValueChange:s.onChange,defaultValue:s.value,className:"flex flex-col space-y-1",children:[e.jsxs(a,{className:"flex items-center space-y-0 space-x-3",children:[e.jsx(o,{children:e.jsx(m,{value:"all"})}),e.jsx(n,{className:"font-normal",children:"All new messages"})]}),e.jsxs(a,{className:"flex items-center space-y-0 space-x-3",children:[e.jsx(o,{children:e.jsx(m,{value:"mentions"})}),e.jsx(n,{className:"font-normal",children:"Direct messages and mentions"})]}),e.jsxs(a,{className:"flex items-center space-y-0 space-x-3",children:[e.jsx(o,{children:e.jsx(m,{value:"none"})}),e.jsx(n,{className:"font-normal",children:"Nothing"})]})]})}),e.jsx(p,{})]})}),e.jsxs("div",{className:"relative",children:[e.jsx("h3",{className:"mb-4 text-lg font-medium",children:"Email Notifications"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(r,{control:i.control,name:"communication_emails",render:({field:s})=>e.jsxs(a,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(n,{className:"text-base",children:"Communication emails"}),e.jsx(t,{children:"Receive emails about your account activity."})]}),e.jsx(o,{children:e.jsx(c,{checked:s.value,onCheckedChange:s.onChange})})]})}),e.jsx(r,{control:i.control,name:"marketing_emails",render:({field:s})=>e.jsxs(a,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(n,{className:"text-base",children:"Marketing emails"}),e.jsx(t,{children:"Receive emails about new products, features, and more."})]}),e.jsx(o,{children:e.jsx(c,{checked:s.value,onCheckedChange:s.onChange})})]})}),e.jsx(r,{control:i.control,name:"social_emails",render:({field:s})=>e.jsxs(a,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(n,{className:"text-base",children:"Social emails"}),e.jsx(t,{children:"Receive emails for friend requests, follows, and more."})]}),e.jsx(o,{children:e.jsx(c,{checked:s.value,onCheckedChange:s.onChange})})]})}),e.jsx(r,{control:i.control,name:"security_emails",render:({field:s})=>e.jsxs(a,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(n,{className:"text-base",children:"Security emails"}),e.jsx(t,{children:"Receive emails about your account activity and security."})]}),e.jsx(o,{children:e.jsx(c,{checked:s.value,onCheckedChange:s.onChange,disabled:!0,"aria-readonly":!0})})]})})]})]}),e.jsx(r,{control:i.control,name:"mobile",render:({field:s})=>e.jsxs(a,{className:"relative flex flex-row items-start space-y-0 space-x-3",children:[e.jsx(o,{children:e.jsx(y,{checked:s.value,onCheckedChange:s.onChange})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(n,{children:"Use different settings for my mobile devices"}),e.jsxs(t,{children:["You can manage your mobile notifications in the"," ",e.jsx(x,{to:"/settings",className:"underline decoration-dashed underline-offset-4 hover:decoration-solid",children:"mobile settings"})," ","page."]})]})]})}),e.jsx(h,{type:"submit",children:"Update notifications"})]})})}function k(){return e.jsx(u,{title:"Notifications",desc:"Configure how you receive notifications.",children:e.jsx(C,{})})}const M=k;export{M as component};
