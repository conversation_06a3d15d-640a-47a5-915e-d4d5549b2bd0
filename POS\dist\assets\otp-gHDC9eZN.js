import{r as e,j as o,c as J,h as Ae,z as ve,B as De,L as Ne}from"./index-C21OP4ex.js";import{C as Be,a as Ie,b as <PERSON>,c as <PERSON>,d as _e,f as He}from"./card-Ctkrqupu.js";import{A as Ge}from"./auth-layout-GxVxJN4C.js";import{u as $e,F as ze,a as Ve,b as qe,c as Ze,d as Je,e as Ke}from"./form-usWdQ_Nt.js";import{s as Qe}from"./zod-B4gLZVLM.js";import{M as Ue}from"./minus-4BfcJ1sI.js";import"./createLucideIcon-CL0CQOA1.js";var Xe=Object.defineProperty,Ye=Object.defineProperties,et=Object.getOwnPropertyDescriptors,U=Object.getOwnPropertySymbols,be=Object.prototype.hasOwnProperty,je=Object.prototype.propertyIsEnumerable,ge=(n,r,l)=>r in n?Xe(n,r,{enumerable:!0,configurable:!0,writable:!0,value:l}):n[r]=l,tt=(n,r)=>{for(var l in r||(r={}))be.call(r,l)&&ge(n,l,r[l]);if(U)for(var l of U(r))je.call(r,l)&&ge(n,l,r[l]);return n},nt=(n,r)=>Ye(n,et(r)),rt=(n,r)=>{var l={};for(var i in n)be.call(n,i)&&r.indexOf(i)<0&&(l[i]=n[i]);if(n!=null&&U)for(var i of U(n))r.indexOf(i)<0&&je.call(n,i)&&(l[i]=n[i]);return l};function at(n){let r=setTimeout(n,0),l=setTimeout(n,10),i=setTimeout(n,50);return[r,l,i]}function ot(n){let r=e.useRef();return e.useEffect(()=>{r.current=n}),r.current}var lt=18,we=40,it=`${we}px`,st=["[data-lastpass-icon-root]","com-1password-button","[data-dashlanecreated]",'[style$="2147483647 !important;"]'].join(",");function ut({containerRef:n,inputRef:r,pushPasswordManagerStrategy:l,isFocused:i}){let[h,s]=e.useState(!1),[S,y]=e.useState(!1),[C,_]=e.useState(!1),H=e.useMemo(()=>l==="none"?!1:(l==="increase-width"||l==="experimental-no-flickering")&&h&&S,[h,S,l]),N=e.useCallback(()=>{let v=n.current,M=r.current;if(!v||!M||C||l==="none")return;let b=v,E=b.getBoundingClientRect().left+b.offsetWidth,B=b.getBoundingClientRect().top+b.offsetHeight/2,c=E-lt,G=B;document.querySelectorAll(st).length===0&&document.elementFromPoint(c,G)===v||(s(!0),_(!0))},[n,r,C,l]);return e.useEffect(()=>{let v=n.current;if(!v||l==="none")return;function M(){let E=window.innerWidth-v.getBoundingClientRect().right;y(E>=we)}M();let b=setInterval(M,1e3);return()=>{clearInterval(b)}},[n,l]),e.useEffect(()=>{let v=i||document.activeElement===r.current;if(l==="none"||!v)return;let M=setTimeout(N,0),b=setTimeout(N,2e3),E=setTimeout(N,5e3),B=setTimeout(()=>{_(!0)},6e3);return()=>{clearTimeout(M),clearTimeout(b),clearTimeout(E),clearTimeout(B)}},[r,i,l,N]),{hasPWMBadge:h,willPushPWMBadge:H,PWM_BADGE_SPACE_WIDTH:it}}var Se=e.createContext({}),ye=e.forwardRef((n,r)=>{var l=n,{value:i,onChange:h,maxLength:s,textAlign:S="left",pattern:y,placeholder:C,inputMode:_="numeric",onComplete:H,pushPasswordManagerStrategy:N="increase-width",pasteTransformer:v,containerClassName:M,noScriptCSSFallback:b=ct,render:E,children:B}=l,c=rt(l,["value","onChange","maxLength","textAlign","pattern","placeholder","inputMode","onComplete","pushPasswordManagerStrategy","pasteTransformer","containerClassName","noScriptCSSFallback","render","children"]),G,ae,oe,le,ie;let[Ce,Ee]=e.useState(typeof c.defaultValue=="string"?c.defaultValue:""),d=i??Ce,R=ot(d),$=e.useCallback(t=>{h==null||h(t),Ee(t)},[h]),w=e.useMemo(()=>y?typeof y=="string"?new RegExp(y):y:null,[y]),p=e.useRef(null),X=e.useRef(null),Y=e.useRef({value:d,onChange:$,isIOS:typeof window<"u"&&((ae=(G=window==null?void 0:window.CSS)==null?void 0:G.supports)==null?void 0:ae.call(G,"-webkit-touch-callout","none"))}),K=e.useRef({prev:[(oe=p.current)==null?void 0:oe.selectionStart,(le=p.current)==null?void 0:le.selectionEnd,(ie=p.current)==null?void 0:ie.selectionDirection]});e.useImperativeHandle(r,()=>p.current,[]),e.useEffect(()=>{let t=p.current,a=X.current;if(!t||!a)return;Y.current.value!==t.value&&Y.current.onChange(t.value),K.current.prev=[t.selectionStart,t.selectionEnd,t.selectionDirection];function m(){if(document.activeElement!==t){V(null),q(null);return}let u=t.selectionStart,f=t.selectionEnd,Q=t.selectionDirection,j=t.maxLength,O=t.value,P=K.current.prev,k=-1,T=-1,A;if(O.length!==0&&u!==null&&f!==null){let Re=u===f,Fe=u===O.length&&O.length<j;if(Re&&!Fe){let D=u;if(D===0)k=0,T=1,A="forward";else if(D===j)k=D-1,T=D,A="backward";else if(j>1&&O.length>1){let ne=0;if(P[0]!==null&&P[1]!==null){A=D<P[1]?"backward":"forward";let Oe=P[0]===P[1]&&P[0]<j;A==="backward"&&!Oe&&(ne=-1)}k=ne+D,T=ne+D+1}}k!==-1&&T!==-1&&k!==T&&p.current.setSelectionRange(k,T,A)}let fe=k!==-1?k:u,he=T!==-1?T:f,Te=A??Q;V(fe),q(he),K.current.prev=[fe,he,Te]}if(document.addEventListener("selectionchange",m,{capture:!0}),m(),document.activeElement===t&&ee(!0),!document.getElementById("input-otp-style")){let u=document.createElement("style");if(u.id="input-otp-style",document.head.appendChild(u),u.sheet){let f="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";Z(u.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),Z(u.sheet,`[data-input-otp]:autofill { ${f} }`),Z(u.sheet,`[data-input-otp]:-webkit-autofill { ${f} }`),Z(u.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),Z(u.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let g=()=>{a&&a.style.setProperty("--root-height",`${t.clientHeight}px`)};g();let x=new ResizeObserver(g);return x.observe(t),()=>{document.removeEventListener("selectionchange",m,{capture:!0}),x.disconnect()}},[]);let[se,ue]=e.useState(!1),[z,ee]=e.useState(!1),[F,V]=e.useState(null),[I,q]=e.useState(null);e.useEffect(()=>{at(()=>{var t,a,m,g;(t=p.current)==null||t.dispatchEvent(new Event("input"));let x=(a=p.current)==null?void 0:a.selectionStart,u=(m=p.current)==null?void 0:m.selectionEnd,f=(g=p.current)==null?void 0:g.selectionDirection;x!==null&&u!==null&&(V(x),q(u),K.current.prev=[x,u,f])})},[d,z]),e.useEffect(()=>{R!==void 0&&d!==R&&R.length<s&&d.length===s&&(H==null||H(d))},[s,H,R,d]);let W=ut({containerRef:X,inputRef:p,pushPasswordManagerStrategy:N,isFocused:z}),ce=e.useCallback(t=>{let a=t.currentTarget.value.slice(0,s);if(a.length>0&&w&&!w.test(a)){t.preventDefault();return}typeof R=="string"&&a.length<R.length&&document.dispatchEvent(new Event("selectionchange")),$(a)},[s,$,R,w]),de=e.useCallback(()=>{var t;if(p.current){let a=Math.min(p.current.value.length,s-1),m=p.current.value.length;(t=p.current)==null||t.setSelectionRange(a,m),V(a),q(m)}ee(!0)},[s]),pe=e.useCallback(t=>{var a,m;let g=p.current;if(!v&&(!Y.current.isIOS||!t.clipboardData||!g))return;let x=t.clipboardData.getData("text/plain"),u=v?v(x):x;t.preventDefault();let f=(a=p.current)==null?void 0:a.selectionStart,Q=(m=p.current)==null?void 0:m.selectionEnd,j=(f!==Q?d.slice(0,f)+u+d.slice(Q):d.slice(0,f)+u+d.slice(f)).slice(0,s);if(j.length>0&&w&&!w.test(j))return;g.value=j,$(j);let O=Math.min(j.length,s-1),P=j.length;g.setSelectionRange(O,P),V(O),q(P)},[s,$,w,d]),Pe=e.useMemo(()=>({position:"relative",cursor:c.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[c.disabled]),me=e.useMemo(()=>({position:"absolute",inset:0,width:W.willPushPWMBadge?`calc(100% + ${W.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:W.willPushPWMBadge?`inset(0 ${W.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:S,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[W.PWM_BADGE_SPACE_WIDTH,W.willPushPWMBadge,S]),Me=e.useMemo(()=>e.createElement("input",nt(tt({autoComplete:c.autoComplete||"one-time-code"},c),{"data-input-otp":!0,"data-input-otp-placeholder-shown":d.length===0||void 0,"data-input-otp-mss":F,"data-input-otp-mse":I,inputMode:_,pattern:w==null?void 0:w.source,"aria-placeholder":C,style:me,maxLength:s,value:d,ref:p,onPaste:t=>{var a;pe(t),(a=c.onPaste)==null||a.call(c,t)},onChange:ce,onMouseOver:t=>{var a;ue(!0),(a=c.onMouseOver)==null||a.call(c,t)},onMouseLeave:t=>{var a;ue(!1),(a=c.onMouseLeave)==null||a.call(c,t)},onFocus:t=>{var a;de(),(a=c.onFocus)==null||a.call(c,t)},onBlur:t=>{var a;ee(!1),(a=c.onBlur)==null||a.call(c,t)}})),[ce,de,pe,_,me,s,I,F,c,w==null?void 0:w.source,d]),te=e.useMemo(()=>({slots:Array.from({length:s}).map((t,a)=>{var m;let g=z&&F!==null&&I!==null&&(F===I&&a===F||a>=F&&a<I),x=d[a]!==void 0?d[a]:null,u=d[0]!==void 0?null:(m=C==null?void 0:C[a])!=null?m:null;return{char:x,placeholderChar:u,isActive:g,hasFakeCaret:g&&x===null}}),isFocused:z,isHovering:!c.disabled&&se}),[z,se,s,I,F,c.disabled,d]),ke=e.useMemo(()=>E?E(te):e.createElement(Se.Provider,{value:te},B),[B,te,E]);return e.createElement(e.Fragment,null,b!==null&&e.createElement("noscript",null,e.createElement("style",null,b)),e.createElement("div",{ref:X,"data-input-otp-container":!0,style:Pe,className:M},ke,e.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},Me)))});ye.displayName="Input";function Z(n,r){try{n.insertRule(r)}catch{console.error("input-otp could not insert CSS rule:",r)}}var ct=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`;function dt({className:n,containerClassName:r,...l}){return o.jsx(ye,{"data-slot":"input-otp",containerClassName:J("flex items-center gap-2 has-disabled:opacity-50",r),className:J("disabled:cursor-not-allowed",n),...l})}function re({className:n,...r}){return o.jsx("div",{"data-slot":"input-otp-group",className:J("flex items-center",n),...r})}function L({index:n,className:r,...l}){const i=e.useContext(Se),{char:h,hasFakeCaret:s,isActive:S}=(i==null?void 0:i.slots[n])??{};return o.jsxs("div",{"data-slot":"input-otp-slot","data-active":S,className:J("data-[active=true]:border-ring data-[active=true]:ring-ring/50 data-[active=true]:aria-invalid:ring-destructive/20 dark:data-[active=true]:aria-invalid:ring-destructive/40 aria-invalid:border-destructive data-[active=true]:aria-invalid:border-destructive dark:bg-input/30 border-input relative flex h-9 w-9 items-center justify-center border-y border-r text-sm shadow-xs transition-all outline-none first:rounded-l-md first:border-l last:rounded-r-md data-[active=true]:z-10 data-[active=true]:ring-[3px]",r),...l,children:[h,s&&o.jsx("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:o.jsx("div",{className:"animate-caret-blink bg-foreground h-4 w-px duration-1000"})})]})}function xe({...n}){return o.jsx("div",{"data-slot":"input-otp-separator",role:"separator",...n,children:o.jsx(Ue,{})})}const pt=ve.object({otp:ve.string().min(1,{message:"Please enter your otp code."})});function mt({className:n,...r}){const l=Ae(),[i,h]=e.useState(!1),s=$e({resolver:Qe(pt),defaultValues:{otp:""}}),S=s.watch("otp");function y(C){h(!0),setTimeout(()=>{h(!1),l({to:"/"})},1e3)}return o.jsx(ze,{...s,children:o.jsxs("form",{onSubmit:s.handleSubmit(y),className:J("grid gap-2",n),...r,children:[o.jsx(Ve,{control:s.control,name:"otp",render:({field:C})=>o.jsxs(qe,{children:[o.jsx(Ze,{className:"sr-only",children:"One-Time Password"}),o.jsx(Je,{children:o.jsxs(dt,{maxLength:6,...C,containerClassName:'justify-between sm:[&>[data-slot="input-otp-group"]>div]:w-12',children:[o.jsxs(re,{children:[o.jsx(L,{index:0}),o.jsx(L,{index:1})]}),o.jsx(xe,{}),o.jsxs(re,{children:[o.jsx(L,{index:2}),o.jsx(L,{index:3})]}),o.jsx(xe,{}),o.jsxs(re,{children:[o.jsx(L,{index:4}),o.jsx(L,{index:5})]})]})}),o.jsx(Ke,{})]})}),o.jsx(De,{className:"mt-2",disabled:S.length<6||i,children:"Verify"})]})})}function ft(){return o.jsx(Ge,{children:o.jsxs(Be,{className:"gap-4",children:[o.jsxs(Ie,{children:[o.jsx(We,{className:"text-base tracking-tight",children:"Two-factor Authentication"}),o.jsxs(Le,{children:["Please enter the authentication code. ",o.jsx("br",{})," We have sent the authentication code to your email."]})]}),o.jsx(_e,{children:o.jsx(mt,{})}),o.jsx(He,{children:o.jsxs("p",{className:"text-muted-foreground px-8 text-center text-sm",children:["Haven't received it?"," ",o.jsx(Ne,{to:"/sign-in",className:"hover:text-primary underline underline-offset-4",children:"Resend a new code."}),"."]})})]})})}const St=ft;export{St as component};
