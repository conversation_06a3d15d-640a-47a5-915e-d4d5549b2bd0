import{r as p,Q as A,A as k,j as s,U as $,C as H,D as L,I as O,P as _,F as P,V as j,E,H as z,J as G,K,W as U,X as V,c as W}from"./index-C21OP4ex.js";import{h as J,R as Q,u as X,F as Z}from"./index-Ct3V_iCU.js";var m="Popover",[w,fe]=H(m,[A]),g=A(),[q,d]=w(m),b=e=>{const{__scopePopover:n,children:o,open:a,defaultOpen:t,onOpenChange:r,modal:c=!1}=e,i=g(n),u=p.useRef(null),[l,h]=p.useState(!1),[x=!1,f]=k({prop:a,defaultProp:t,onChange:r});return s.jsx($,{...i,children:s.jsx(q,{scope:n,contentId:L(),triggerRef:u,open:x,onOpenChange:f,onOpenToggle:p.useCallback(()=>f(C=>!C),[f]),hasCustomAnchor:l,onCustomAnchorAdd:p.useCallback(()=>h(!0),[]),onCustomAnchorRemove:p.useCallback(()=>h(!1),[]),modal:c,children:o})})};b.displayName=m;var F="PopoverAnchor",B=p.forwardRef((e,n)=>{const{__scopePopover:o,...a}=e,t=d(F,o),r=g(o),{onCustomAnchorAdd:c,onCustomAnchorRemove:i}=t;return p.useEffect(()=>(c(),()=>i()),[c,i]),s.jsx(j,{...r,...a,ref:n})});B.displayName=F;var N="PopoverTrigger",S=p.forwardRef((e,n)=>{const{__scopePopover:o,...a}=e,t=d(N,o),r=g(o),c=O(n,t.triggerRef),i=s.jsx(_.button,{type:"button","aria-haspopup":"dialog","aria-expanded":t.open,"aria-controls":t.contentId,"data-state":T(t.open),...a,ref:c,onClick:P(e.onClick,t.onOpenToggle)});return t.hasCustomAnchor?i:s.jsx(j,{asChild:!0,...r,children:i})});S.displayName=N;var R="PopoverPortal",[Y,ee]=w(R,{forceMount:void 0}),y=e=>{const{__scopePopover:n,forceMount:o,children:a,container:t}=e,r=d(R,n);return s.jsx(Y,{scope:n,forceMount:o,children:s.jsx(E,{present:o||r.open,children:s.jsx(z,{asChild:!0,container:t,children:a})})})};y.displayName=R;var v="PopoverContent",D=p.forwardRef((e,n)=>{const o=ee(v,e.__scopePopover),{forceMount:a=o.forceMount,...t}=e,r=d(v,e.__scopePopover);return s.jsx(E,{present:a||r.open,children:r.modal?s.jsx(te,{...t,ref:n}):s.jsx(re,{...t,ref:n})})});D.displayName=v;var oe=G("PopoverContent.RemoveScroll"),te=p.forwardRef((e,n)=>{const o=d(v,e.__scopePopover),a=p.useRef(null),t=O(n,a),r=p.useRef(!1);return p.useEffect(()=>{const c=a.current;if(c)return J(c)},[]),s.jsx(Q,{as:oe,allowPinchZoom:!0,children:s.jsx(M,{...e,ref:t,trapFocus:o.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:P(e.onCloseAutoFocus,c=>{var i;c.preventDefault(),r.current||(i=o.triggerRef.current)==null||i.focus()}),onPointerDownOutside:P(e.onPointerDownOutside,c=>{const i=c.detail.originalEvent,u=i.button===0&&i.ctrlKey===!0,l=i.button===2||u;r.current=l},{checkForDefaultPrevented:!1}),onFocusOutside:P(e.onFocusOutside,c=>c.preventDefault(),{checkForDefaultPrevented:!1})})})}),re=p.forwardRef((e,n)=>{const o=d(v,e.__scopePopover),a=p.useRef(!1),t=p.useRef(!1);return s.jsx(M,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:r=>{var c,i;(c=e.onCloseAutoFocus)==null||c.call(e,r),r.defaultPrevented||(a.current||(i=o.triggerRef.current)==null||i.focus(),r.preventDefault()),a.current=!1,t.current=!1},onInteractOutside:r=>{var u,l;(u=e.onInteractOutside)==null||u.call(e,r),r.defaultPrevented||(a.current=!0,r.detail.originalEvent.type==="pointerdown"&&(t.current=!0));const c=r.target;((l=o.triggerRef.current)==null?void 0:l.contains(c))&&r.preventDefault(),r.detail.originalEvent.type==="focusin"&&t.current&&r.preventDefault()}})}),M=p.forwardRef((e,n)=>{const{__scopePopover:o,trapFocus:a,onOpenAutoFocus:t,onCloseAutoFocus:r,disableOutsidePointerEvents:c,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:l,onInteractOutside:h,...x}=e,f=d(v,o),C=g(o);return X(),s.jsx(Z,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:t,onUnmountAutoFocus:r,children:s.jsx(K,{asChild:!0,disableOutsidePointerEvents:c,onInteractOutside:h,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:l,onDismiss:()=>f.onOpenChange(!1),children:s.jsx(U,{"data-state":T(f.open),role:"dialog",id:f.contentId,...C,...x,ref:n,style:{...x.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),I="PopoverClose",ne=p.forwardRef((e,n)=>{const{__scopePopover:o,...a}=e,t=d(I,o);return s.jsx(_.button,{type:"button",...a,ref:n,onClick:P(e.onClick,()=>t.onOpenChange(!1))})});ne.displayName=I;var ae="PopoverArrow",se=p.forwardRef((e,n)=>{const{__scopePopover:o,...a}=e,t=g(o);return s.jsx(V,{...t,...a,ref:n})});se.displayName=ae;function T(e){return e?"open":"closed"}var ce=b,ie=S,pe=y,ue=D;function ve({...e}){return s.jsx(ce,{"data-slot":"popover",...e})}function Pe({...e}){return s.jsx(ie,{"data-slot":"popover-trigger",...e})}function ge({className:e,align:n="center",sideOffset:o=4,...a}){return s.jsx(pe,{children:s.jsx(ue,{"data-slot":"popover-content",align:n,sideOffset:o,className:W("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...a})})}export{ve as P,Pe as a,ge as b};
