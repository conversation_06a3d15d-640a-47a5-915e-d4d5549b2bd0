const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-BPLFXWP_.js","assets/exceljs.min-Da2DFgpf.js","assets/index-Bnt3OGV2.js","assets/index-CVkaVZVo.css"])))=>i.map(i=>d[i]);
import{r as p,a as ve,R as Q,j as e,ay as De,B as U,c as Te,o as Xe,p as Ye,q as Qe}from"./index-Bnt3OGV2.js";import{v as J,D as ze}from"./date-range-picker-CVvofQC0.js";import{L as Me}from"./form-wT1R35uI.js";import"./user-oq7iQk7S.js";import{a as Ge,b as Ae}from"./pos-api-BwpRFGce.js";import"./vietqr-api-DAENYiJ_.js";import"./crm-api-Dd9UhSCJ.js";import{u as Ie}from"./useQuery-DSrD7NAp.js";import{Q as Je}from"./query-keys-3lmd-xp6.js";import{u as be}from"./use-pos-data-BCiRRpbt.js";import{f as Z}from"./isSameMonth-C8JQo-AN.js";import{u as Ze}from"./use-pos-cities-data-DJpJVYAb.js";import{C as Ue}from"./combobox-Dfida1wD.js";import{B as ie}from"./badge-u6qfWPSj.js";import{C as ce}from"./checkbox-BiVztVsP.js";import{C as et,a as tt,b as st}from"./collapsible-CfXqqCTe.js";import{P as nt,a as at,b as rt}from"./popover-C2mvzdeD.js";import{C as Ne,f as ot}from"./select-Czd7KcZQ.js";import{D as Oe}from"./download-hLMxLFI2.js";import{C as de,a as me,b as he,c as ue,d as xe}from"./chart-B8vaQEo3.js";import{C as te,a as ne,b as ae,d as re}from"./card-aL168XsH.js";import{L as ge}from"./LineChart-CpvsY9ZR.js";import{C as fe}from"./CartesianGrid-DYRy0CpJ.js";import{X as pe,Y as _e}from"./generateCategoricalChart-DQyxv-Uv.js";import{L as je}from"./Line-BtcRHoj7.js";import{T as Le,a as Pe,b as ee,c as E,d as Ee,e as O}from"./table-C602nYEy.js";import{D as Se,a as Ce,b as Fe,c as He,e as it}from"./dialog-hQ-PVOWr.js";import{I as Ke}from"./input-CiKEYbig.js";import{u as ct}from"./useQueries-BF-E3UeB.js";import{C as lt}from"./clock-g78PetTT.js";import{u as dt}from"./use-promotions-CKNmei6e.js";import{S as mt}from"./scroll-area-BeVbW7LP.js";import"./calendar-CzR6WBaB.js";import"./createLucideIcon-CNa_hh6B.js";import"./index-BT7Z3RDV.js";import"./chevron-right-sZt3EK3r.js";import"./react-icons.esm-B7rNr9e-.js";import"./utils-km2FGkQ4.js";import"./use-auth-Bxzq8gtF.js";import"./useMutation-d67-fNFq.js";import"./command-ByfqjQDn.js";import"./search-BVQVKwPC.js";import"./chevrons-up-down-BYsm4o8o.js";import"./check-apx2eTVC.js";import"./index-C2T2k_Lh.js";import"./index-Bl1CGAiZ.js";import"./index-Chjiymov.js";const qe=async r=>(await Ge.get("/v1/reports/sale-summary/promotions",{params:r})).data,ht=Object.freeze(Object.defineProperty({__proto__:null,getPromotionRevenue:qe},Symbol.toStringTag,{value:"Module"})),Re=({startDate:r,endDate:u,selectedStoreIds:_,companyUid:C,brandUid:m,enabled:y=!0})=>{var N,d;const S=[Je.PROMOTIONS,"sale-summary",C,m,r,u,_.sort().join(",")],D=Ie({queryKey:S,queryFn:async()=>{if(!_.length)return{data:[],message:"No stores selected",track_id:""};const g={brand_uid:m,company_uid:C,start_date:r,end_date:u,list_store_uid:_.join(","),store_open_at:0,by_days:1};return qe(g)},enabled:y&&_.length>0,staleTime:5*60*1e3,gcTime:10*60*1e3,retry:2,refetchOnWindowFocus:!1});return{data:((N=D.data)==null?void 0:N.data)||[],isLoading:D.isLoading,isError:D.isError,error:D.error,refetch:D.refetch,hasData:(((d=D.data)==null?void 0:d.data)||[]).length>0}},Be=p.createContext(void 0),ut=({children:r})=>{var se;const[u,_]=p.useState(()=>{const l=new Date;return{from:l,to:l}}),[C,m]=p.useState(0),[y,S]=p.useState([]),[I,D]=p.useState([]),[N,d]=p.useState([]),[g,L]=p.useState([]),{company:w,activeBrands:v}=be(),R=(w==null?void 0:w.id)||"",$=((se=v[0])==null?void 0:se.id)||"",{currentBrandStores:A}=ve(),q=A.map(l=>l.id||l.store_id),a=(()=>{const l=[];return y.forEach(x=>{A.filter(P=>P.city_uid===x).forEach(P=>{const X=P.id||P.store_id;X&&!l.includes(X)&&l.push(X)})}),I.forEach(x=>{A.find(P=>(P.id||P.store_id)===x)&&!l.includes(x)&&l.push(x)}),l})(),c=a.length>0?a:q,T=(()=>{const l=[];return N.forEach(x=>{A.filter(P=>P.city_uid===x).forEach(P=>{const X=P.id||P.store_id;X&&!l.includes(X)&&l.push(X)})}),g.forEach(x=>{A.find(P=>(P.id||P.store_id)===x)&&!l.includes(x)&&l.push(x)}),l})(),{startDate:M,endDate:k}=p.useMemo(()=>{const l=new Date(u.from);l.setHours(0,0,0,0);const x=new Date(u.to);return x.setHours(23,59,59,999),{startDate:l.getTime(),endDate:x.getTime()}},[u.from,u.to,C]),B=()=>m(l=>l+1),W=l=>{const x=typeof l=="function"?l(y):l,f=[...I];x.length===0&&f.length===0?(S([]),D([]),d([]),L([]),m(X=>X+1)):S(x)},s=l=>{const x=typeof l=="function"?l(I):l;[...y].length===0&&x.length===0?(S([]),D([]),d([]),L([]),m(X=>X+1)):D(x)},{data:t,isLoading:n}=Re({startDate:M,endDate:k,selectedStoreIds:c,companyUid:R,brandUid:$,enabled:!0}),{data:o,isLoading:i}=Re({startDate:M,endDate:k,selectedStoreIds:T,companyUid:R,brandUid:$,enabled:T.length>0}),h=l=>{try{const x=new Date(l);if(!isNaN(x.getTime()))return Z(x,"dd/MM",{locale:J});if(/^\d{2}\/\d{2}$/.test(l))return l;const f=l.match(/^(\d{4})-(\d{2})-(\d{2})$/);return f?`${f[3]}/${f[2]}`:l}catch{return l}},F=Q.useMemo(()=>{const l=new Map;return(t||[]).forEach(x=>{(x.list_data||[]).forEach(f=>{const P=l.get(f.date)||{total_amount:0,total_bill:0,discount_amount:0};l.set(f.date,{total_amount:P.total_amount+(f.revenue_net??f.total_amount??0),total_bill:P.total_bill+(f.total_bill??0),discount_amount:P.discount_amount+(f.discount_amount??0)})})}),Array.from(l.entries()).sort(([x],[f])=>x<f?-1:x>f?1:0).map(([x,f])=>({date_label:h(x),total_amount:f.total_amount,total_bill:f.total_bill,discount_amount:f.discount_amount}))},[t]),K=Q.useMemo(()=>{const l=new Map;return(o||[]).forEach(x=>{(x.list_data||[]).forEach(f=>{const P=l.get(f.date)||{total_amount:0,total_bill:0,discount_amount:0};l.set(f.date,{total_amount:P.total_amount+(f.revenue_net??f.total_amount??0),total_bill:P.total_bill+(f.total_bill??0),discount_amount:P.discount_amount+(f.discount_amount??0)})})}),Array.from(l.entries()).sort(([x],[f])=>x<f?-1:x>f?1:0).map(([x,f])=>({date_label:h(x),total_amount:f.total_amount,total_bill:f.total_bill,discount_amount:f.discount_amount}))},[o]),Y=Q.useMemo(()=>(t||[]).map(l=>({promotion_id:l.promotion_id||"",promotion_name:l.promotion_name,total_bill:l.total_bill||0,revenue_net:l.revenue_net||0,revenue_gross:l.revenue_gross||0,commission_amount:l.commission_amount||0,discount_amount:l.discount_amount||0,deduct_tax_amount:l.deduct_tax_amount||0})),[t]),z={dateRange:u,setDateRange:_,selectedCities:y,selectedStores:I,setSelectedCities:W,setSelectedStores:s,compareCities:N,compareStores:g,setCompareCities:d,setCompareStores:L,selectedStoreIds:a,finalStoreIds:c,compareStoreIds:T,startDate:M,endDate:k,data:F,compareData:K,rawData:t,compareRawData:o,isLoading:n,isCompareLoading:i,tableRows:Y,handleUpdateDateRange:B,handleExport:async l=>{try{const x=l==="selected"?c:q,{getPromotionRevenue:f}=await De(async()=>{const{getPromotionRevenue:G}=await Promise.resolve().then(()=>ht);return{getPromotionRevenue:G}},void 0),X=(await f({brand_uid:$,company_uid:R,start_date:M,end_date:k,list_store_uid:x.join(","),store_open_at:0,by_days:1})).data||[];if(X.length===0){console.warn("No data to export");return}const Ve=x.map(G=>{var we;return(we=A.find(ke=>(ke.id||ke.store_id)===G))==null?void 0:we.store_name}).filter(G=>!!G).slice(0,3),{exportPromotionRevenueToExcel:We}=await De(async()=>{const{exportPromotionRevenueToExcel:G}=await import("./index-BPLFXWP_.js");return{exportPromotionRevenueToExcel:G}},__vite__mapDeps([0,1,2,3]));await We(X,{dateRange:u,selectedStoreNames:Ve,filename:"top-promotions.xlsx"})}catch(x){console.error("Export failed:",x)}}};return e.jsx(Be.Provider,{value:z,children:r})},le=()=>{const r=p.useContext(Be);if(!r)throw new Error("usePromotionContext must be used within PromotionProvider");return r},$e=({selectedCities:r,selectedStores:u,onCitiesChange:_,onStoresChange:C,className:m,excludeCities:y=[],excludeStores:S=[]})=>{const[I,D]=p.useState(!1),[N,d]=p.useState(new Set),[g,L]=p.useState("all"),[w,v]=p.useState(""),{cities:R}=Ze(),{currentBrandStores:$}=ve(),A=p.useMemo(()=>R.filter(t=>!y.includes(t.id)),[R,y]),q=p.useMemo(()=>$.filter(t=>!S.includes(t.id||t.store_id)).filter(t=>g==="all"?!0:g==="chain"?t.is_franchise===0:t.is_franchise===1),[$,S,g]),j=t=>q.filter(n=>n.city_uid===t),a=t=>{const n=new Set(N);n.has(t)?n.delete(t):n.add(t),d(n)},c=(t,n)=>{if(n)_([...r,t]);else{_(r.filter(h=>h!==t));const i=j(t).map(h=>h.id||h.store_id);C(u.filter(h=>!i.includes(h)))}},b=(t,n)=>{const o=$.find(h=>(h.id||h.store_id)===t),i=o==null?void 0:o.city_uid;if(n){const h=[...u,t];if(C(h),i){const K=j(i).map(H=>H.id||H.store_id);if(K.every(H=>h.includes(H)||r.includes(i))){const H=h.filter(z=>!K.includes(z));C(H),_([...r,i])}}}else if(i&&r.includes(i)){const K=j(i).map(Y=>Y.id||Y.store_id).filter(Y=>Y!==t);_(r.filter(Y=>Y!==i)),C([...u,...K])}else C(u.filter(h=>h!==t))},T=t=>r.includes(t),M=t=>{const o=j(t).map(h=>h.id||h.store_id),i=u.filter(h=>o.includes(h));return i.length>0&&i.length<o.length},k=t=>u.includes(t),B=p.useMemo(()=>{const t=r.reduce((o,i)=>{const h=j(i);return o+h.length},0),n=u.filter(o=>{const i=$.find(F=>(F.id||F.store_id)===o);if(!i)return!0;const h=i.city_uid;return!r.includes(h)}).length;return t+n},[r,u,$,j]),W=B>0?`Đã chọn ${B} cửa hàng`:"Chọn cửa hàng",s=p.useMemo(()=>{const t=w.trim().toLowerCase();return A.some(n=>{const o=n.id||n.city_id,i=j(o);return t?i.some(h=>String(h.store_name||"").toLowerCase().includes(t)):i.length>0})},[A,q,w]);return e.jsxs(nt,{open:I,onOpenChange:D,children:[e.jsx(at,{asChild:!0,children:e.jsxs(U,{variant:"outline",role:"combobox","aria-expanded":I,className:Te("w-[320px] justify-between",m),children:[e.jsx("span",{className:"truncate",children:W}),e.jsx(Ne,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),e.jsx(rt,{className:"w-80 p-0",align:"start",children:e.jsx("div",{className:"max-h-96 overflow-y-auto",children:e.jsxs("div",{className:"p-2",children:[e.jsx("div",{className:"mb-2 flex flex-col items-start justify-center gap-2",children:e.jsx(Ue,{options:[{value:"all",label:"Tất cả cửa hàng"},{value:"chain",label:"Chuỗi"},{value:"franchise",label:"Nhượng quyền"}],value:g,onValueChange:t=>L(t||"all"),placeholder:"Loại cửa hàng",searchPlaceholder:"Tìm kiếm...",className:"w-full"})}),!s&&e.jsx("div",{className:"text-muted-foreground flex h-40 items-center justify-center text-lg font-semibold",children:"Không có dữ liệu"}),s&&A.filter(t=>{const n=t.id||t.city_id,o=j(n),i=w.trim().toLowerCase();return i?o.some(h=>String(h.store_name||"").toLowerCase().includes(i)):o.length>0}).map(t=>{const n=t.id||t.city_id,o=w.trim().toLowerCase(),i=j(n),h=o?i.filter(H=>String(H.store_name||"").toLowerCase().includes(o)):i,F=o?!0:N.has(n),K=T(n),Y=M(n);return e.jsx("div",{className:"mb-2",children:e.jsxs(et,{open:F,onOpenChange:()=>a(n),children:[e.jsxs("div",{className:"flex items-center space-x-2 rounded p-2 hover:bg-gray-50",children:[e.jsx(ce,{checked:K,ref:H=>{if(H&&Y&&!K){const z=H.querySelector('input[type="checkbox"]');z&&(z.indeterminate=!0)}},onCheckedChange:H=>c(n,H)}),e.jsxs(tt,{className:"flex flex-1 items-center space-x-2 text-left",children:[e.jsx("span",{className:"font-medium",children:t.city_name}),e.jsxs("span",{className:"text-muted-foreground text-sm",children:["(",h.length," cửa hàng)"]}),e.jsx(Ne,{className:Te("ml-auto h-4 w-4 transition-transform",F&&"rotate-180 transform")})]})]}),e.jsx(st,{children:F&&e.jsx("div",{className:"ml-6 space-y-1",children:h.map(H=>{const z=H.id||H.store_id,se=k(z)||K;return e.jsxs("div",{className:"flex items-center space-x-2 rounded p-2 hover:bg-gray-50",children:[e.jsx(ce,{checked:se,onCheckedChange:l=>b(z,l)}),e.jsx("span",{className:"text-sm",children:H.store_name}),typeof H.is_franchise<"u"&&(H.is_franchise===1?e.jsx(ie,{className:"ml-2",children:"Nhượng quyền"}):e.jsx(ie,{className:"ml-2",children:"Chuỗi"}))]},z)})})})]})},n)})]})})})]})},xt=()=>{const{dateRange:r,setDateRange:u,selectedCities:_,selectedStores:C,setSelectedCities:m,setSelectedStores:y,compareCities:S,compareStores:I,setCompareCities:D,setCompareStores:N,handleUpdateDateRange:d,handleExport:g}=le(),L=_.length>0||C.length>0;return e.jsxs("div",{className:"flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between",children:[e.jsxs("div",{className:"flex flex-col gap-4 md:flex-row md:gap-6",children:[e.jsx(ze,{initialDateFrom:r.from,initialDateTo:r.to,onUpdate:({range:w})=>{w!=null&&w.from&&(w!=null&&w.to)&&(u({from:w.from,to:w.to}),d())},align:"start",locale:"vi-VN"}),e.jsx($e,{selectedCities:_,selectedStores:C,onCitiesChange:m,onStoresChange:y,className:"h-[40px] min-w-[200px]"}),L&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"text-muted-foreground flex items-center text-sm",children:"so với"}),e.jsx($e,{selectedCities:S,selectedStores:I,onCitiesChange:D,onStoresChange:N,className:"h-[40px] min-w-[200px]",excludeCities:_,excludeStores:C})]})]}),e.jsxs(U,{variant:"outline",size:"sm",onClick:()=>g("selected"),children:[e.jsx(Oe,{className:"mr-2 h-4 w-4"}),"Xuất báo cáo"]})]})},ye=r=>r>=1e6?Math.round(r/1e6)+"M":r>=1e3?Math.round(r/1e3)+"K":new Intl.NumberFormat("vi-VN").format(r),gt=()=>{const{rawData:r,compareRawData:u,isLoading:_,isCompareLoading:C,dateRange:m,compareCities:y,compareStores:S}=le(),I=(y==null?void 0:y.length)>0||(S==null?void 0:S.length)>0;if(_)return e.jsxs(te,{children:[e.jsx(ne,{children:e.jsx(ae,{children:"Doanh thu bao gồm: Phí ship, VAT"})}),e.jsx(re,{children:e.jsx("div",{className:"flex h-64 items-center justify-center",children:e.jsx("div",{className:"text-muted-foreground",children:"Đang tải dữ liệu..."})})})]});const D=(a,c)=>a.getFullYear()===c.getFullYear()&&a.getMonth()===c.getMonth()&&a.getDate()===c.getDate(),N=p.useMemo(()=>{const a=new Date(m.from),c=new Date(m.to);return a.setHours(0,0,0,0),c.setHours(0,0,0,0),D(a,c)},[m.from,m.to]),d=p.useMemo(()=>{if(!N)return!1;const a=new Date(m.from),c=new Date;return a.setHours(0,0,0,0),c.setHours(0,0,0,0),D(a,c)},[m.from,N]),g=p.useMemo(()=>{if(!m)return{data:[],series:[]};const a=(r||[]).map(s=>({id:String(s.promotion_id),name:String(s.promotion_name||s.promotion_id)})),c=s=>{const t=new Date(s);if(!Number.isNaN(t.getTime()))return Z(t,"dd/MM",{locale:J});const n=String(s).match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/);return n?`${n[3]}/${n[2]}`:String(s)},b=new Map;(r||[]).forEach(s=>{const t=String(s.promotion_id),n=new Map;(s.list_data||[]).forEach(o=>{const i=c(String(o.date)),h=Number(o.revenue_net??o.total_amount??0);n.set(i,(n.get(i)||0)+h)}),b.set(t,n)});const T=new Date(m.from),M=new Date(m.to);T.setHours(0,0,0,0),M.setHours(0,0,0,0);const k=[];for(let s=new Date(T);s<=M;s.setDate(s.getDate()+1)){const t=Z(s,"dd/MM",{locale:J}),n={name:t};a.forEach((o,i)=>{var K;const h=`p_${o.id}_${i}`,F=((K=b.get(o.id))==null?void 0:K.get(t))||0;n[h]=F}),k.push(n)}if(k.length===1&&!N){const s=new Date(m.from);s.setDate(s.getDate()-1),k.unshift({...k[0],name:Z(s,"dd/MM",{locale:J})})}const B=s=>`var(--chart-${s%12+1})`,W=a.map((s,t)=>({key:`p_${s.id}_${t}`,name:s.name,color:B(t)}));return{data:k,series:W}},[r,m,N]),L=p.useMemo(()=>{if(!m||!u)return{data:[],series:[]};const a=(u||[]).map(s=>({id:String(s.promotion_id),name:String(s.promotion_name||s.promotion_id)})),c=s=>{const t=new Date(s);if(!Number.isNaN(t.getTime()))return Z(t,"dd/MM",{locale:J});const n=String(s).match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/);return n?`${n[3]}/${n[2]}`:String(s)},b=new Map;(u||[]).forEach(s=>{const t=String(s.promotion_id),n=new Map;(s.list_data||[]).forEach(o=>{const i=c(String(o.date)),h=Number(o.revenue_net??o.total_amount??0);n.set(i,(n.get(i)||0)+h)}),b.set(t,n)});const T=new Date(m.from),M=new Date(m.to);T.setHours(0,0,0,0),M.setHours(0,0,0,0);const k=[];for(let s=new Date(T);s<=M;s.setDate(s.getDate()+1)){const t=Z(s,"dd/MM",{locale:J}),n={name:t};a.forEach((o,i)=>{var K;const h=`p_${o.id}_${i}`,F=((K=b.get(o.id))==null?void 0:K.get(t))||0;n[h]=F}),k.push(n)}if(k.length===1&&!N){const s=new Date(m.from);s.setDate(s.getDate()-1),k.unshift({...k[0],name:Z(s,"dd/MM",{locale:J})})}const B=s=>`var(--chart-${s%12+1})`,W=a.map((s,t)=>({key:`p_${s.id}_${t}`,name:s.name,color:B(t)}));return{data:k,series:W}},[u,m,N]),w=p.useMemo(()=>{const a=g.data;return a.length===0?!0:a.every(c=>Object.keys(c).every(b=>b==="name"||(Number(c[b])||0)===0))},[g]),v=p.useMemo(()=>{const a=L.data;return a.length===0?!0:a.every(c=>Object.keys(c).every(b=>b==="name"||(Number(c[b])||0)===0))},[L]),R=p.useMemo(()=>w?[{name:"01/01",revenue:0,detail:{total_amount:0,total_bill:0,discount_amount:0}},{name:"02/01",revenue:0,detail:{total_amount:0,total_bill:0,discount_amount:0}}]:g.data,[w,g]),$=p.useMemo(()=>v?[{name:"01/01",revenue:0,detail:{total_amount:0,total_bill:0,discount_amount:0}},{name:"02/01",revenue:0,detail:{total_amount:0,total_bill:0,discount_amount:0}}]:L.data,[v,L]),A=p.useMemo(()=>{const a=R.map(M=>M.name);if(a.length<=10)return a;const b=Math.max(1,Math.ceil(a.length/8)),T=a.filter((M,k)=>k%b===0);return T[T.length-1]!==a[a.length-1]&&T.push(a[a.length-1]),T},[R]),q=p.useMemo(()=>{const a=$.map(M=>M.name);if(a.length<=10)return a;const b=Math.max(1,Math.ceil(a.length/8)),T=a.filter((M,k)=>k%b===0);return T[T.length-1]!==a[a.length-1]&&T.push(a[a.length-1]),T},[$]),j=p.useMemo(()=>{const a={};return g.series.forEach(c=>a[c.key]={label:c.name,color:c.color}),a},[g]);return I?e.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs(te,{children:[e.jsx(ne,{children:e.jsx(ae,{children:"Doanh thu bao gồm: Phí ship, VAT"})}),e.jsx(re,{children:e.jsx(de,{config:j,className:"h-[300px] w-full",children:e.jsxs(ge,{accessibilityLayer:!0,data:R,margin:{left:12,right:12},children:[e.jsx(fe,{vertical:!1}),e.jsx(pe,{dataKey:"name",tickLine:!1,axisLine:!1,tickMargin:8,interval:0,ticks:A}),e.jsx(_e,{tickLine:!1,axisLine:!1,tickMargin:8,tickFormatter:ye,domain:w?[0,5]:[0,a=>Math.ceil(Number(a)*1.2)],ticks:w?[0,1,2,3,4,5]:void 0}),e.jsx(me,{cursor:!1,content:e.jsx(he,{indicator:"line"})}),g.series.map(a=>e.jsx(je,{dataKey:a.key,type:"linear",stroke:d?"transparent":`var(--color-${a.key})`,strokeWidth:2,dot:d?{r:4,stroke:`var(--color-${a.key})`,fill:`var(--color-${a.key})`}:!1},a.key)),e.jsx(ue,{verticalAlign:"bottom",content:e.jsx(xe,{})})]})})})]}),e.jsxs(te,{children:[e.jsx(ne,{children:e.jsx(ae,{children:"Doanh thu bao gồm: Phí ship, VAT"})}),e.jsx(re,{children:C?e.jsx("div",{className:"flex h-[300px] items-center justify-center",children:e.jsx("div",{className:"text-muted-foreground",children:"Đang tải dữ liệu so sánh..."})}):e.jsx(de,{config:j,className:"h-[300px] w-full",children:e.jsxs(ge,{accessibilityLayer:!0,data:$,margin:{left:12,right:12},children:[e.jsx(fe,{vertical:!1}),e.jsx(pe,{dataKey:"name",tickLine:!1,axisLine:!1,tickMargin:8,interval:0,ticks:q}),e.jsx(_e,{tickLine:!1,axisLine:!1,tickMargin:8,tickFormatter:ye,domain:v?[0,5]:[0,a=>Math.ceil(Number(a)*1.2)],ticks:v?[0,1,2,3,4,5]:void 0}),e.jsx(me,{cursor:!1,content:e.jsx(he,{indicator:"line"})}),g.series.map(a=>e.jsx(je,{dataKey:a.key,type:"linear",stroke:d?"transparent":`var(--color-${a.key})`,strokeWidth:2,dot:d?{r:4,stroke:`var(--color-${a.key})`,fill:`var(--color-${a.key})`}:!1},a.key)),e.jsx(ue,{verticalAlign:"bottom",content:e.jsx(xe,{})})]})})})]})]}):e.jsxs(te,{children:[e.jsx(ne,{children:e.jsx(ae,{children:"Doanh thu bao gồm: Phí ship, VAT"})}),e.jsx(re,{children:e.jsx(de,{config:j,className:"h-[300px] w-full",children:e.jsxs(ge,{accessibilityLayer:!0,data:R,margin:{left:12,right:12},children:[e.jsx(fe,{vertical:!1}),e.jsx(pe,{dataKey:"name",tickLine:!1,axisLine:!1,tickMargin:8,interval:0,ticks:A}),e.jsx(_e,{tickLine:!1,axisLine:!1,tickMargin:8,tickFormatter:ye,domain:w?[0,5]:[0,a=>Math.ceil(Number(a)*1.2)],ticks:w?[0,1,2,3,4,5]:void 0}),e.jsx(me,{cursor:!1,content:e.jsx(he,{indicator:"line"})}),g.series.map(a=>e.jsx(je,{dataKey:a.key,type:"linear",stroke:d?"transparent":`var(--color-${a.key})`,strokeWidth:2,dot:d?{r:4,stroke:`var(--color-${a.key})`,fill:`var(--color-${a.key})`}:!1},a.key)),e.jsx(ue,{verticalAlign:"bottom",content:e.jsx(xe,{})})]})})})]})},ft=({companyUid:r,brandUid:u,startDate:_,endDate:C,promotionId:m,storeUids:y,fallbackStartDate:S,fallbackEndDate:I,shouldFetchApi:D})=>{var q;const N=_||S,d=C||I,g=p.useMemo(()=>!D||!r||!u||!m||!(y!=null&&y.length)?[]:y.map(j=>({queryKey:["invoice-source-detail",{company_uid:r,brand_uid:u,start_date:N,end_date:d,promotion_id:m,list_store_uid:j,page:1,results_per_page:20}],queryFn:async()=>{const a=new URLSearchParams({company_uid:r,brand_uid:u,start_date:String(N),end_date:String(d),promotion_id:String(m),list_store_uid:j,page:"1",results_per_page:"20",by_days:"1",is_sales:"1"});return(await Ae.get(`/v3/pos-cms/report/sale_by_promotion?${a.toString()}`)).data},enabled:D&&!!r&&!!u&&!!m,staleTime:5*60*1e3,gcTime:10*60*1e3,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1})),[D,r,u,m,y,N,d]),L=ct({queries:g}),w=p.useMemo(()=>{const j=[];return L.forEach(a=>{var c;(c=a.data)!=null&&c.data&&j.push(...a.data.data)}),j},[L.map(j=>j.data)]),v=L.some(j=>j.isLoading),R=((q=L.find(j=>j.error))==null?void 0:q.error)||null,$=w,A=p.useMemo(()=>($||[]).map(c=>{var W,s,t,n,o;const b=(c.sale_detail||[]).map(i=>({name:i.item_name||i.name||"N/A",item_name:i.item_name||i.name||"N/A",quantity:i.quantity||0,price:i.price||0,toppings:i.toppings||[],change_data:i.change_data||{sale_detail:[]}})),T=((s=(W=c.sale_payment_method)==null?void 0:W[0])==null?void 0:s.payment_method_name)||"N/A",M=((n=(t=c.sale_payment_method)==null?void 0:t[0])==null?void 0:n.trace_no)||"",k=((o=c.extra_data)==null?void 0:o.tran_no_partner)||c.tran_no||"N/A",B=i=>i?new Date(i).toLocaleString("vi-VN",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}):new Date().toISOString();return{id:c.id||`invoice-${Math.random()}`,tran_id:c.tran_id||"",invoiceCode:c.tran_no||"N/A",partnerInvoiceNumber:k,branchName:c.table_name||"N/A",sourceType:c.source_deli||"N/A",amount:c.total_amount||0,dateTime:B(c.tran_date||0),items:b,paymentMethod:T,paymentReference:M,employeeName:c.employee_name||"Ca 1 Sekai Bà Hạt",tranDate:c.tran_date}}),[$]);return{invoices:A,isLoading:v,error:R,hasMorePages:!1,currentPage:1,totalInvoices:A.length}},pt=(r,u=!0)=>Ie({queryKey:["sale-change-log",r],queryFn:async()=>{const _=new URLSearchParams({company_uid:r.company_uid,brand_uid:r.brand_uid,store_uid:r.store_uid,tran_id:r.tran_id,start_date:r.start_date.toString(),end_date:r.end_date.toString()});return(await Ae.get(`/v3/pos-cms/sale-change-log?${_.toString()}`)).data},enabled:u&&!!r.tran_id,staleTime:5*60*1e3,gcTime:10*60*1e3,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1});function _t({open:r,onOpenChange:u,invoice:_}){var w;if(!_)return null;const{selectedStoreIds:C,dateRange:m}=le(),{company:y,activeBrands:S}=be(),I=p.useMemo(()=>{const v=new Date(m.from);return v.setHours(0,0,0,0),v.getTime()},[m.from]),D=p.useMemo(()=>{const v=new Date(m.to);return v.setHours(23,59,59,999),v.getTime()},[m.to]),{data:N,isLoading:d,error:g}=pt({company_uid:(y==null?void 0:y.id)||"",brand_uid:((w=S==null?void 0:S[0])==null?void 0:w.id)||"",store_uid:(C==null?void 0:C[0])||"",tran_id:_.tran_id,start_date:I,end_date:D},r&&!!_.tran_id),L=v=>v?new Date(v).toLocaleTimeString("vi-VN",{hour:"2-digit",minute:"2-digit"}):"N/A";return e.jsx(Se,{open:r,onOpenChange:u,children:e.jsxs(Ce,{className:"sm:max-w-2xl",children:[e.jsx(Fe,{children:e.jsx(He,{className:"flex items-center justify-center",children:e.jsx("span",{children:"Nhật ký order"})})}),e.jsx("div",{className:"space-y-4",children:d?e.jsx("div",{className:"py-4 text-center text-sm text-gray-500",children:"Đang tải nhật ký..."}):g?e.jsxs("div",{className:"py-4 text-center text-sm text-red-500",children:["Lỗi khi tải nhật ký: ",g.message]}):N!=null&&N.data&&N.data.length>0?N.data.map((v,R)=>{var $,A,q,j,a,c;return e.jsxs("div",{className:"space-y-4 rounded-lg border p-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-lg font-bold text-gray-900",children:["#",(($=v.tran_id)==null?void 0:$.slice(-5))||"N/A"," - ",((A=v.change_data)==null?void 0:A.tran_no)||"N/A"]}),e.jsxs("div",{className:"text-sm text-gray-500",children:[new Date(v.created_at*1e3).toLocaleDateString("vi-VN")," ",L(((q=v.change_data)==null?void 0:q.tran_date)||0)]})]}),e.jsx("div",{className:"text-sm text-gray-600",children:v.table_name||"N/A"}),e.jsxs("div",{className:"text-sm text-gray-600",children:["TN: ",((j=v.change_data)==null?void 0:j.employee_name)||"N/A"," - STT: ",((a=v.change_data)==null?void 0:a.order_no)||"N/A"]})]}),((c=v.change_data)==null?void 0:c.sale_detail)&&v.change_data.sale_detail.length>0&&e.jsx("div",{className:"space-y-3",children:v.change_data.sale_detail.map((b,T)=>{var M;return e.jsxs("div",{className:"border-b border-gray-200 pb-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx("span",{className:"text-gray-900",children:b.item_name||"N/A"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(lt,{className:"mr-2 h-4 w-4 text-gray-400"}),e.jsx("span",{className:"text-sm text-gray-500",children:L(((M=v.change_data)==null?void 0:M.tran_date)||0)})]})]}),e.jsx("div",{className:"text-right",children:e.jsx("span",{className:"text-gray-900",children:b.quantity})})]}),b.toppings&&b.toppings.length>0&&e.jsx("div",{className:"mt-2 text-xs text-gray-500",children:b.toppings.map((k,B)=>e.jsxs("span",{children:[k.item_name||"N/A"," ",k.quantity||1,B<b.toppings.length-1?", ":""]},B))})]},T)})})]},R)}):e.jsx("div",{className:"py-4 text-center text-sm text-gray-500 italic",children:"Không có nhật ký thay đổi"})})]})})}const oe=r=>new Intl.NumberFormat("vi-VN").format(r)+" ₫";function jt({open:r,onOpenChange:u,promotionName:_,promotionId:C,companyUid:m,brandUid:y,startDate:S,endDate:I,storeUids:D}){const[N,d]=p.useState(""),[g,L]=p.useState(new Set),[w,v]=p.useState(!1),[R,$]=p.useState(null),A=r&&!!C,q=p.useMemo(()=>{const s=new Date;return s.setHours(0,0,0,0),s.getTime()},[]),j=p.useMemo(()=>{const s=new Date;return s.setHours(23,59,59,999),s.getTime()},[]),{invoices:a,isLoading:c,error:b,totalInvoices:T}=ft({companyUid:m,brandUid:y,startDate:S,endDate:I,promotionId:C,storeUids:D,fallbackStartDate:q,fallbackEndDate:j,shouldFetchApi:A}),M=p.useMemo(()=>a.filter(s=>{const t=N.toLowerCase();return s.invoiceCode.toLowerCase().includes(t)||s.partnerInvoiceNumber.toLowerCase().includes(t)}),[a,N]),k=()=>{},B=s=>{L(t=>{const n=new Set(t);return n.has(s)?n.delete(s):n.add(s),n})},W=({invoice:s})=>{var o;const t=g.has(s.id),n=()=>{B(s.id)};return e.jsxs("div",{className:"cursor-pointer rounded-lg border p-4 hover:bg-gray-50",onClick:n,children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"mb-1 flex items-center gap-2",children:[e.jsxs("span",{className:"font-bold",children:["#",((o=s.paymentReference)==null?void 0:o.slice(-5))||"N/A"]}),e.jsx("span",{className:"text-sm text-gray-500",children:"-"}),e.jsxs("span",{className:"font-bold",children:["SỐ HĐ: ",s.invoiceCode]})]}),e.jsxs("div",{className:"text-sm text-gray-600",children:["Số hoá đơn đối tác: ",s.partnerInvoiceNumber]}),e.jsxs("div",{className:"text-sm text-gray-600",children:[s.branchName," - Kênh ",s.sourceType," (",s.partnerInvoiceNumber,") -"," ",oe(s.amount)]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"text-sm text-gray-600",children:s.dateTime}),e.jsx("div",{className:"flex h-6 w-6 items-center justify-center",children:t?e.jsx(ot,{className:"h-4 w-4"}):e.jsx(Ne,{className:"h-4 w-4"})})]}),e.jsx(U,{variant:"link",className:"h-auto p-0 text-sm text-blue-600",onClick:i=>{i.stopPropagation(),$(s),v(!0)},children:"Xem nhật ký order"})]})]}),t&&e.jsxs("div",{className:"mt-4 space-y-3 border-t pt-4",children:[s.items&&s.items.length>0?e.jsx("div",{className:"space-y-2",children:s.items.map((i,h)=>e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsxs("span",{className:"flex-1",children:[e.jsxs("span",{className:"text-gray-600",children:["(x",i.quantity,")"]})," ",i.name]}),e.jsx("span",{className:"font-mono",children:oe(i.price)})]},h))}):e.jsx("div",{className:"text-sm text-gray-500 italic",children:"Không có thông tin chi tiết món hàng"}),e.jsxs("div",{className:"space-y-2 border-t pt-3",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Thành tiền:"}),e.jsx("span",{className:"font-mono",children:oe(s.amount)})]}),e.jsxs("div",{className:"flex justify-between text-base font-semibold",children:[e.jsx("span",{children:"Tổng tiền:"}),e.jsx("span",{className:"font-mono",children:oe(s.amount)})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:"Phương thức thanh toán:"}),e.jsxs("span",{className:"text-right",children:[s.paymentMethod,s.paymentReference&&e.jsxs("div",{className:"text-xs text-gray-500",children:["(",s.paymentReference,")"]})]})]})]})]})]})};return e.jsxs(Se,{open:r,onOpenChange:u,children:[e.jsxs(Ce,{className:"sm:max-w-2xl",children:[e.jsx(Fe,{children:e.jsx(He,{className:"flex items-center justify-center",children:e.jsxs("span",{children:["Hoá đơn áp dụng khuyến mãi ",_]})})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex gap-4",children:e.jsx("div",{className:"flex-1",children:e.jsx(Ke,{placeholder:"Tìm kiếm theo mã hoá đơn hoặc số hoá đơn đối tác",value:N,onChange:s=>d(s.target.value)})})}),e.jsx("div",{className:"max-h-96 overflow-y-auto",children:c&&T===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:"Đang tải dữ liệu..."}):b?e.jsxs("div",{className:"py-8 text-center text-red-500",children:[e.jsx("div",{className:"font-medium",children:"Có lỗi xảy ra khi tải dữ liệu:"}),e.jsxs("details",{className:"mt-2 text-xs",children:[e.jsx("summary",{className:"cursor-pointer",children:"Chi tiết lỗi"}),e.jsx("pre",{className:"mt-1 max-h-32 overflow-auto text-left",children:JSON.stringify(b,null,2)})]})]}):M.length===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:e.jsx("div",{className:"font-medium",children:"Không tìm thấy hóa đơn nào"})}):e.jsx("div",{className:"space-y-3",children:M.map(s=>e.jsx(W,{invoice:s},s.id))})}),e.jsx("div",{className:"flex justify-end border-t pt-4",children:e.jsxs(U,{onClick:k,className:"flex items-center gap-2",children:[e.jsx(Oe,{className:"h-4 w-4"}),"Xuất hoá đơn"]})})]})]}),e.jsx(_t,{open:w,onOpenChange:v,invoice:R})]})}const yt=({open:r,onOpenChange:u,selectedIds:_,onSelectedChange:C})=>{const[m,y]=Q.useState(""),{promotions:S,isLoading:I}=dt({enabled:r,searchTerm:m}),D=(d,g)=>{C(g?[..._,d]:_.filter(L=>L!==d))},N=Q.useMemo(()=>{const d=m.trim().toLowerCase();return d?S.filter(g=>g.name.toLowerCase().includes(d)):S},[S,m]);return e.jsx(Se,{open:r,onOpenChange:u,children:e.jsxs(Ce,{className:"max-h-[90vh] w-[800px] max-w-4xl lg:max-w-4xl",children:[e.jsxs("div",{className:"space-y-6 p-2",children:[e.jsx(Ke,{placeholder:"Tìm kiếm",value:m,onChange:d=>y(d.target.value)}),e.jsx(mt,{className:"h-[60vh] w-full",children:e.jsxs("div",{className:"space-y-4",children:[_.length>0&&e.jsxs("div",{children:[e.jsx("div",{className:"mb-3 flex items-center gap-2 rounded bg-green-50 p-3 text-base font-medium text-green-600",children:e.jsxs("span",{children:["✓ Đã chọn ",_.length]})}),e.jsx("div",{className:"space-y-3",children:N.filter(d=>_.includes(d.code)).map(d=>e.jsxs("div",{className:"flex items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(ce,{id:d.code,checked:!0,onCheckedChange:g=>D(d.code,!!g),className:"h-5 w-5"}),e.jsx(Me,{htmlFor:d.code,className:"flex-1 cursor-pointer text-base",children:d.name})]},d.code))})]}),e.jsxs("div",{children:[e.jsx("div",{className:"mb-3 rounded bg-gray-50 p-3 text-base font-medium text-gray-600",children:e.jsxs("span",{children:["Còn lại ",N.filter(d=>!_.includes(d.code)).length]})}),e.jsx("div",{className:"space-y-3",children:I?e.jsx("div",{className:"text-muted-foreground p-4 text-center",children:"Đang tải..."}):N.filter(d=>!_.includes(d.code)).map(d=>e.jsxs("div",{className:"flex items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(ce,{id:d.code,checked:!1,onCheckedChange:g=>D(d.code,!!g),className:"h-5 w-5"}),e.jsx(Me,{htmlFor:d.code,className:"flex-1 cursor-pointer text-base",children:d.name})]},d.code))})]})]})})]}),e.jsxs(it,{children:[e.jsx(U,{variant:"outline",onClick:()=>u(!1),children:"Hủy"}),e.jsx(U,{onClick:()=>u(!1),children:"Xong"})]})]})})},V=r=>new Intl.NumberFormat("vi-VN").format(r),Nt=()=>{var s;const{tableRows:r,isLoading:u,compareCities:_,compareStores:C,compareData:m,selectedStoreIds:y,compareStoreIds:S,finalStoreIds:I,startDate:D,endDate:N}=le(),{currentBrandStores:d}=ve(),{company:g,activeBrands:L}=be(),[w,v]=Q.useState(!1),[R,$]=Q.useState([]),[A,q]=Q.useState(!1),[j,a]=Q.useState(null),c=Q.useMemo(()=>R.length===0?r:r.filter(t=>R.includes(t.promotion_id)),[r,R]),b=(_==null?void 0:_.length)>0||(C==null?void 0:C.length)>0,T=t=>{a(t),q(!0)},M=Q.useMemo(()=>{if(!m||!b)return[];const t=new Map;return m.forEach(n=>{n.list_data&&n.list_data.forEach(o=>{const i=o.promotion_id||o.promotion_name;i&&t.set(i,{promotion_id:i,promotion_name:o.promotion_name||n.promotion_name,total_bill:o.total_bill||0,revenue_net:o.revenue_net||0,revenue_gross:o.revenue_gross||0,commission_amount:o.commission_amount||0,discount_amount:o.discount_amount||0,deduct_tax_amount:o.deduct_tax_amount||0})})}),c.map(n=>t.get(n.promotion_id)||t.get(n.promotion_name)||{promotion_id:n.promotion_id,promotion_name:n.promotion_name,total_bill:0,revenue_net:0,revenue_gross:0,commission_amount:0,discount_amount:0,deduct_tax_amount:0})},[m,b,c]);if(u)return e.jsx(te,{children:e.jsx("div",{className:"text-muted-foreground flex h-32 items-center justify-center",children:"Đang tải dữ liệu..."})});const k=()=>{if(y.length===0)return"Tất cả cửa hàng";if(y.length===1){const t=d.find(n=>n.id===y[0]||n.store_id===y[0]);return t?t.store_name:"Cửa hàng đã chọn"}else return`${y.length} cửa hàng`},B=t=>{var h;const n=t.map(F=>d.find(K=>(K.id||K.store_id)===F)).filter(Boolean),o=((h=n[0])==null?void 0:h.store_name)||"Tất cả cửa hàng",i=Math.max(0,n.length-1);return e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsxs(Xe,{children:[e.jsx(Ye,{asChild:!0,children:e.jsx(ie,{variant:"outline",className:"cursor-default",children:o})}),n.length>0&&e.jsx(Qe,{side:"bottom",children:e.jsx("div",{className:"max-h-60 max-w-[260px] overflow-auto pr-1",children:n.map(F=>e.jsx("div",{className:"whitespace-nowrap",children:F.store_name},F.id||F.store_id))})})]}),i>0&&e.jsxs(ie,{variant:"secondary",children:["+",i]})]})},W=()=>{if(!b||S.length===0)return"";if(S.length===1){const t=d.find(n=>n.id===S[0]||n.store_id===S[0]);return t?t.store_name:"Cửa hàng so sánh"}else return`${S.length} cửa hàng`};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mb-3 flex items-center gap-2",children:e.jsx(U,{size:"sm",onClick:()=>v(!0),children:"Chọn CTKM"})}),e.jsx(yt,{open:w,onOpenChange:v,selectedIds:R,onSelectedChange:$}),b?e.jsxs("div",{className:"rounded-md border",children:[e.jsxs(Le,{children:[e.jsxs(Pe,{children:[e.jsxs(ee,{children:[e.jsx(E,{className:"w-10",rowSpan:2,children:"#"}),e.jsx(E,{rowSpan:2,children:"CTKM"}),e.jsx(E,{className:"border-r-2 border-b-2 text-center",colSpan:6,children:y.length>0?B(y):k()}),e.jsx(E,{className:"border-b-2 text-center",colSpan:6,children:S.length>0?B(S):W()})]}),e.jsxs(ee,{children:[e.jsx(E,{className:"text-right",children:"Tổng hoá đơn"}),e.jsx(E,{className:"text-right",children:"Tổng doanh thu (net)"}),e.jsx(E,{className:"text-right",children:"Tổng doanh thu (gross)"}),e.jsx(E,{className:"text-right",children:"Hoa hồng"}),e.jsx(E,{className:"text-right",children:"Tổng giảm giá"}),e.jsx(E,{className:"border-r-2 text-right",children:"Thuế khấu trừ"}),e.jsx(E,{className:"border-l-2 text-right",children:"Tổng hoá đơn"}),e.jsx(E,{className:"text-right",children:"Tổng doanh thu (net)"}),e.jsx(E,{className:"text-right",children:"Tổng doanh thu (gross)"}),e.jsx(E,{className:"text-right",children:"Hoa hồng"}),e.jsx(E,{className:"text-right",children:"Tổng giảm giá"}),e.jsx(E,{className:"text-right",children:"Thuế khấu trừ"})]})]}),e.jsx(Ee,{children:c.map((t,n)=>{const o=M[n];return e.jsxs(ee,{className:"hover:bg-muted/50 cursor-pointer",onClick:()=>T(t),children:[e.jsx(O,{children:n+1}),e.jsx(O,{children:t.promotion_name}),e.jsx(O,{className:"text-right font-mono",children:V(t.total_bill)}),e.jsxs(O,{className:"text-right font-mono",children:[V(t.revenue_net)," đ"]}),e.jsxs(O,{className:"text-right font-mono",children:[V(t.revenue_gross)," đ"]}),e.jsxs(O,{className:"text-right font-mono",children:[V(t.commission_amount)," đ"]}),e.jsxs(O,{className:"text-right font-mono",children:[V(t.discount_amount)," đ"]}),e.jsxs(O,{className:"border-r-2 text-right font-mono",children:[V(t.deduct_tax_amount)," đ"]}),e.jsx(O,{className:"border-l-2 text-right font-mono",children:V(o.total_bill)}),e.jsxs(O,{className:"text-right font-mono",children:[V(o.revenue_net)," đ"]}),e.jsxs(O,{className:"text-right font-mono",children:[V(o.revenue_gross)," đ"]}),e.jsxs(O,{className:"text-right font-mono",children:[V(o.commission_amount)," đ"]}),e.jsxs(O,{className:"text-right font-mono",children:[V(o.discount_amount)," đ"]}),e.jsxs(O,{className:"text-right font-mono",children:[V(o.deduct_tax_amount)," đ"]})]},t.promotion_name+n)})})]}),c.length===0&&e.jsx("div",{className:"text-muted-foreground flex h-24 items-center justify-center",children:"Không có dữ liệu"})]}):e.jsxs("div",{className:"rounded-md border",children:[e.jsxs(Le,{children:[e.jsx(Pe,{children:e.jsxs(ee,{children:[e.jsx(E,{className:"w-10",children:"#"}),e.jsx(E,{children:"CTKM"}),e.jsx(E,{className:"text-right",children:"Tổng hoá đơn"}),e.jsx(E,{className:"text-right",children:"Tổng doanh thu (net)"}),e.jsx(E,{className:"text-right",children:"Tổng doanh thu (gross)"}),e.jsx(E,{className:"text-right",children:"Hoa hồng"}),e.jsx(E,{className:"text-right",children:"Tổng giảm giá"}),e.jsx(E,{className:"text-right",children:"Thuế khấu trừ"})]})}),e.jsx(Ee,{children:c.map((t,n)=>e.jsxs(ee,{className:"hover:bg-muted/50 cursor-pointer",onClick:()=>T(t),children:[e.jsx(O,{children:n+1}),e.jsx(O,{children:t.promotion_name}),e.jsx(O,{className:"text-right font-mono",children:V(t.total_bill)}),e.jsxs(O,{className:"text-right font-mono",children:[V(t.revenue_net)," đ"]}),e.jsxs(O,{className:"text-right font-mono",children:[V(t.revenue_gross)," đ"]}),e.jsxs(O,{className:"text-right font-mono",children:[V(t.commission_amount)," đ"]}),e.jsxs(O,{className:"text-right font-mono",children:[V(t.discount_amount)," đ"]}),e.jsxs(O,{className:"text-right font-mono",children:[V(t.deduct_tax_amount)," đ"]})]},t.promotion_name+n))})]}),c.length===0&&e.jsx("div",{className:"text-muted-foreground flex h-24 items-center justify-center",children:"Không có dữ liệu"})]}),e.jsx(jt,{open:A,onOpenChange:q,promotionId:j==null?void 0:j.promotion_id,promotionName:j==null?void 0:j.promotion_name,companyUid:g==null?void 0:g.id,brandUid:(s=L[0])==null?void 0:s.id,startDate:D,endDate:N,storeUids:I})]})},vt=()=>e.jsx(ut,{children:e.jsxs("div",{className:"space-y-6 p-4",children:[e.jsx(xt,{}),e.jsx(gt,{}),e.jsx(Nt,{})]})}),fs=vt;export{fs as component};
