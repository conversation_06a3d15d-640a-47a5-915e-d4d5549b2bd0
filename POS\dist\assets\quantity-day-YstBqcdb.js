import{u as ge,a3 as ke,a4 as D,r as T,j as e,R as oe,B as I,z as V}from"./index-C21OP4ex.js";import{b as re}from"./pos-api-D5WM5mnz.js";import{u as Ie}from"./use-stores-BQdEFBhG.js";import"./vietqr-api-ruJT0-tj.js";import{u as Ke}from"./use-items-zU7JIOkv.js";import{u as lt}from"./useQuery-BNGphiae.js";import{u as qe}from"./useMutation-Bh5DVQPI.js";import{Q as xe}from"./query-keys-3lmd-xp6.js";import"./user-CgNoQQSj.js";import"./crm-api-BUMUQ8t4.js";import{H as ot}from"./header-DNPEfjkR.js";import{M as ct}from"./main-DRnqW_wu.js";import{P as dt}from"./profile-dropdown-DlMxjxHH.js";import{S as ht,T as mt}from"./search-5KGATEvM.js";import{C as ce}from"./checkbox-DUpnJ1Rx.js";import{D as X}from"./data-table-column-header-7_8s6zRv.js";import{D as ut,a as gt,b as xt,c as Le,e as pt}from"./dropdown-menu-DINyPmco.js";import{u as ft}from"./use-dialog-state-BYP8UC3r.js";import{E as yt}from"./ellipsis-vTf6H_Di.js";import{S as jt}from"./square-pen-DEJxaifi.js";import{T as Ge}from"./trash-2-C_5rhUMO.js";import{f as Ye}from"./isSameMonth-C8JQo-AN.js";import{C as _t}from"./confirm-dialog-AXI5ANMc.js";import{u as wt,F as Nt,a as H,b as R,c as L,d as Y,e as W,L as Dt}from"./form-usWdQ_Nt.js";import{s as vt}from"./zod-B4gLZVLM.js";import{D as Be}from"./date-picker-8CCUFJO0.js";import{D as be,a as Se,b as Ce,c as Te,e as bt,f as St}from"./dialog-DXjwjGKV.js";import{I as Me}from"./input-4sMIt001.js";import{S as de,a as he,b as me,c as ue,d as J}from"./select-B8Pw9rS-.js";import{M as Ct}from"./multi-select-DQY8oleQ.js";import{utils as O,read as Tt}from"./xlsx-DkH2s96g.js";import{T as Ve,a as Ee,b as $,c as C,d as Qe,e as v}from"./table-BIu4Pah2.js";import{i as Ft}from"./items-in-store-api-Bg8BWWH-.js";import{d as kt}from"./excel-util-ipSMJz6Z.js";import{D as It}from"./download-dYw-Dq4T.js";import{U as Fe}from"./upload-CZfvv05H.js";import{S as p}from"./skeleton-xUyFo20h.js";import{u as qt,g as Mt,a as Vt,b as Et,c as Qt,d as At,e as Pt,f as Oe}from"./index-B_FCwlUM.js";import{D as $t}from"./data-table-pagination-BKENav2P.js";import{P as Ut}from"./plus-C1IEs-Ov.js";import{X as Ht}from"./calendar-BiBi2kQF.js";import"./stores-api-BIve2jSO.js";import"./item-api-C8PXkgMG.js";import"./utils-km2FGkQ4.js";import"./separator-ZoxOB1XH.js";import"./date-range-picker-B1pgj5D_.js";import"./createLucideIcon-CL0CQOA1.js";import"./chevron-right-BAjIoZMb.js";import"./react-icons.esm-DB-kGUq7.js";import"./popover-BtedB187.js";import"./index-Ct3V_iCU.js";import"./avatar-C98m2_SM.js";import"./search-context-DtMZc3QX.js";import"./command-BnLmWlRk.js";import"./search-DHRhj6_i.js";import"./createReactComponent-zh6rKAzG.js";import"./scroll-area-DKiYF9x5.js";import"./index-Bh-UeytL.js";import"./IconChevronRight-Bwbz4HuV.js";import"./IconSearch-CyZD7dtp.js";import"./index-DuT2Ibxp.js";import"./check-DcHT8QEO.js";import"./index-UJ-79IIJ.js";import"./alert-dialog-CzdUByb-.js";import"./calendar-BszJEazX.js";import"./badge-gtDUxDTX.js";import"./circle-x-CqN3rxdF.js";const B=new Map,le=new Map,Rt=5*60*1e3,Z={getQuantityDays:async t=>{const s=`${t.company_uid}-${t.brand_uid}-${t.store_uid||"all"}-${t.list_item_id||"all"}-${t.page||1}-${t.limit||50}-${t.from_date||""}-${t.to_date||""}-${t.active??1}`,a=B.get(s);if(a&&Date.now()-a.timestamp<Rt)return a.data;const n=le.get(s);if(n)return n;const i=(async()=>{var l,u,h,y,g,j,d;try{const m=new URLSearchParams({company_uid:t.company_uid,brand_uid:t.brand_uid,page:(t.page||1).toString()});t.list_item_id&&m.set("list_item_id",t.list_item_id),t.store_uid&&m.set("store_uid",t.store_uid),t.limit!==void 0&&m.set("limit",t.limit.toString()),t.from_date!==void 0&&m.set("from_date",t.from_date.toString()),t.to_date!==void 0&&m.set("to_date",t.to_date.toString()),t.active!==void 0&&m.set("active",t.active.toString());const b=await re.get(`/mdata/v1/quantity-days?${m.toString()}`,{headers:{Accept:"application/json, text/plain, */*","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4});if(!b.data||typeof b.data!="object")throw new Error("Invalid response format from quantity days API");const x=b.data;return B.set(s,{data:x,timestamp:Date.now()}),x}catch(m){throw m.code==="ECONNABORTED"||(l=m.message)!=null&&l.includes("timeout")?new Error("Request timeout - server is taking too long to respond. Please try again."):((u=m.response)==null?void 0:u.status)===504?new Error("Gateway timeout (504) - server is overloaded. Please try again later."):((h=m.response)==null?void 0:h.status)===503?new Error("Service unavailable (503) - server is temporarily down. Please try again later."):((y=m.response)==null?void 0:y.status)>=500?new Error(`Server error (${m.response.status}) - please try again later.`):((g=m.response)==null?void 0:g.status)===429?new Error("Too many requests - please wait a moment before trying again."):((j=m.response)==null?void 0:j.status)===401?new Error("Unauthorized - please check your authentication."):((d=m.response)==null?void 0:d.status)===403?new Error("Forbidden - you do not have permission to access this resource."):m}finally{le.delete(s)}})();return le.set(s,i),i},createQuantityDay:async t=>{var s,a;try{const n=[{...t,require_update:t.require_update==="true"?null:t.require_update,from_date:t.from_date,to_date:t.to_date}],i=await re.post("/mdata/v1/quantity-days",n,{headers:{"Content-Type":"application/json","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"}});return B.clear(),i.data.data||i.data}catch(n){throw((s=n.response)==null?void 0:s.status)===400?new Error(((a=n.response.data)==null?void 0:a.message)||"Invalid data provided."):n}},updateQuantityDay:async t=>{var s,a,n;try{const i=await re.put("/mdata/v1/quantity-days",t,{headers:{"Content-Type":"application/json","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"}});return B.clear(),i.data.data||i.data}catch(i){throw((s=i.response)==null?void 0:s.status)===404?new Error("Quantity day configuration not found."):((a=i.response)==null?void 0:a.status)===400?new Error(((n=i.response.data)==null?void 0:n.message)||"Invalid data provided."):i}},deleteQuantityDay:async t=>{var s;try{await re.delete("/mdata/v1/quantity-days",{headers:{"Content-Type":"application/json","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},data:t,timeout:3e4}),B.clear()}catch(a){throw((s=a.response)==null?void 0:s.status)===404?new Error("Quantity day configuration not found."):a}},clearCache:()=>{B.clear(),le.clear()}},Lt=t=>{if(t===0)return"Tất cả các ngày trong tuần";const s=["Chủ nhật","Thứ 2","Thứ 3","Thứ 4","Thứ 5","Thứ 6","Thứ 7"],a=[2,4,8,16,32,64,128],n=[];for(let i=0;i<7;i++)t&a[i]&&n.push(s[i]);return n.join(", ")},Yt=(t={})=>{const{params:s={},enabled:a=!0}=t,{company:n,brands:i}=ge(g=>g.auth),l=i==null?void 0:i[0],h={...{company_uid:(n==null?void 0:n.id)||"",brand_uid:(l==null?void 0:l.id)||"",page:1},...s},y=!!(n!=null&&n.id&&(l!=null&&l.id));return lt({queryKey:[xe.QUANTITY_DAYS_LIST,h],queryFn:async()=>(await Z.getQuantityDays(h)).data||[],enabled:a&&y,staleTime:5*60*1e3,refetchInterval:10*60*1e3})},Xe=()=>{const t=ke(),{mutate:s,isPending:a}=qe({mutationFn:n=>Z.createQuantityDay(n),onSuccess:()=>{t.invalidateQueries({queryKey:[xe.QUANTITY_DAYS_LIST]}),D.success("Tạo cấu hình số lượng thành công!")},onError:n=>{D.error(`Lỗi khi tạo cấu hình: ${n.message}`)}});return{createQuantityDay:s,isCreating:a}},Bt=()=>{const t=ke(),{mutate:s,isPending:a}=qe({mutationFn:n=>Z.updateQuantityDay(n),onSuccess:()=>{t.invalidateQueries({queryKey:[xe.QUANTITY_DAYS_LIST]}),D.success("Cập nhật cấu hình số lượng thành công!")},onError:n=>{D.error(`Lỗi khi cập nhật cấu hình: ${n.message}`)}});return{updateQuantityDay:s,isUpdating:a}},Ot=()=>{const t=ke(),{company:s,brands:a}=ge(u=>u.auth),n=a==null?void 0:a[0],{mutate:i,isPending:l}=qe({mutationFn:u=>{if(Array.isArray(u))return Z.deleteQuantityDay({list_id:u});if(!(s!=null&&s.id)||!(n!=null&&n.id))throw new Error("Missing company or brand information");return Z.deleteQuantityDay({company_uid:s.id,brand_uid:n.id,id:u})},onSuccess:(u,h)=>{t.invalidateQueries({queryKey:[xe.QUANTITY_DAYS_LIST]});const y=Array.isArray(h)?h.length:1;D.success(`Xóa ${y} cấu hình số lượng thành công!`)},onError:u=>{D.error(`Lỗi khi xóa cấu hình: ${u.message}`)}});return{deleteQuantityDay:i,isDeleting:l}},zt=(t={})=>{var n;const s=Yt(t),a=((n=s.data)==null?void 0:n.map(i=>({id:i.id,quantity:i.quantity_per_day,fromDate:new Date(i.from_date),toDate:new Date(i.to_date),time:Lt(i.time_sale_date_week),appliedItems:i.item_list.join(", "),storeUid:i.store_uid,storeName:"",isActive:!i.deleted,createdAt:new Date(i.created_at),updatedAt:new Date(i.updated_at),originalData:i})))||[];return{...s,data:a}},We=oe.createContext(null);function Kt({children:t}){const[s,a]=ft(null),[n,i]=T.useState(null);return e.jsx(We,{value:{open:s,setOpen:a,currentRow:n,setCurrentRow:i},children:t})}const Ae=()=>{const t=oe.useContext(We);if(!t)throw new Error("useQuantityDay has to be used within <QuantityDayContext>");return t};function Gt({row:t}){const s=t.original,{setOpen:a,setCurrentRow:n}=Ae(),i=()=>{n(s),a("update")},l=()=>{n(s),a("delete")};return e.jsxs(ut,{children:[e.jsx(gt,{asChild:!0,children:e.jsxs(I,{variant:"ghost",className:"data-[state=open]:bg-muted flex h-8 w-8 p-0",children:[e.jsx(yt,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Open menu"})]})}),e.jsxs(xt,{align:"end",className:"w-[160px]",children:[e.jsxs(Le,{onClick:i,children:[e.jsx(jt,{className:"mr-2 h-4 w-4"}),"Chỉnh sửa"]}),e.jsx(pt,{}),e.jsxs(Le,{onClick:l,className:"text-destructive",children:[e.jsx(Ge,{className:"mr-2 h-4 w-4"}),"Xóa"]})]})]})}const Xt=[{id:"select",header:({table:t})=>e.jsx(ce,{checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:s=>t.toggleAllPageRowsSelected(!!s),"aria-label":"Select all",className:"translate-y-[2px]"}),cell:({row:t})=>e.jsx(ce,{checked:t.getIsSelected(),onCheckedChange:s=>t.toggleSelected(!!s),"aria-label":"Select row",className:"translate-y-[2px]"}),enableSorting:!1,enableHiding:!1,size:50},{id:"index",header:"#",cell:({row:t})=>e.jsx("div",{className:"w-[50px]",children:t.index+1}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"quantity",header:({column:t})=>e.jsx(X,{column:t,title:"Số lượng"}),cell:({row:t})=>e.jsx("div",{className:"text-sm font-medium",children:t.getValue("quantity")}),enableSorting:!1,enableHiding:!0},{accessorKey:"fromDate",header:({column:t})=>e.jsx(X,{column:t,title:"Từ ngày"}),cell:({row:t})=>{const s=t.getValue("fromDate");return e.jsx("div",{className:"text-sm",children:Ye(s,"dd/MM/yyyy")})},enableSorting:!1,enableHiding:!0},{accessorKey:"toDate",header:({column:t})=>e.jsx(X,{column:t,title:"Đến ngày"}),cell:({row:t})=>{const s=t.getValue("toDate");return e.jsx("div",{className:"text-sm",children:Ye(s,"dd/MM/yyyy")})},enableSorting:!1,enableHiding:!0},{accessorKey:"time",header:({column:t})=>e.jsx(X,{column:t,title:"Thời gian"}),cell:({row:t})=>e.jsx("div",{className:"text-sm",children:t.getValue("time")}),enableSorting:!1,enableHiding:!0},{accessorKey:"appliedItems",header:({column:t})=>e.jsx(X,{column:t,title:"Món áp dụng"}),cell:({row:t})=>e.jsx("div",{className:"max-w-[200px] truncate text-sm",title:t.getValue("appliedItems"),children:t.getValue("appliedItems")}),enableSorting:!1,enableHiding:!0},{id:"actions",cell:({row:t})=>e.jsx(Gt,{row:t})}];function Wt(){const{setOpen:t}=Ae();return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(I,{size:"sm",className:"h-8",onClick:()=>t("create"),children:[e.jsx(Ut,{className:"mr-2 h-4 w-4"}),"Thêm mới"]}),e.jsxs(I,{size:"sm",className:"h-8",variant:"outline",onClick:()=>t("import-from-file"),children:[e.jsx(Fe,{className:"mr-2 h-4 w-4"}),"Thêm món từ file"]})]})}function Jt({table:t,storesData:s=[],selectedStoreUid:a="all",onStoreChange:n,selectedItemUid:i="all",onItemChange:l,itemsData:u=[]}){var y;const h=t.getState().columnFilters.length>0;return e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[e.jsx(Me,{placeholder:"Tìm kiếm ...",value:((y=t.getColumn("appliedItems"))==null?void 0:y.getFilterValue())??"",onChange:g=>{var j;return(j=t.getColumn("appliedItems"))==null?void 0:j.setFilterValue(g.target.value)},className:"h-8 w-[150px] lg:w-[250px]"}),e.jsxs(de,{value:a,onValueChange:n,children:[e.jsx(he,{className:"h-8 w-[180px]",children:e.jsx(me,{placeholder:"Chọn cửa hàng"})}),e.jsx(ue,{children:s.map(g=>e.jsx(J,{value:g.id,children:g.name},g.id))})]}),e.jsxs(de,{value:i,onValueChange:l,children:[e.jsx(he,{className:"h-8 w-[200px]",children:e.jsx(me,{placeholder:"Chọn món"})}),e.jsxs(ue,{children:[e.jsx(J,{value:"all",children:"Tất cả món"}),u==null?void 0:u.map(g=>e.jsx(J,{value:g.id,children:g.item_name},g.id))]})]}),h&&e.jsxs(I,{variant:"ghost",onClick:()=>t.resetColumnFilters(),className:"h-8 px-2 lg:px-3",children:["Reset",e.jsx(Ht,{className:"ml-2 h-4 w-4"})]})]})})}const Zt=V.object({store_uid:V.string().min(1,"Vui lòng chọn cửa hàng."),item_list:V.array(V.string()).min(1,"Vui lòng chọn ít nhất một món."),from_date:V.date({required_error:"Vui lòng chọn ngày bắt đầu."}),to_date:V.date({required_error:"Vui lòng chọn ngày kết thúc."}),quantity_per_day:V.number().min(1,"Số lượng phải lớn hơn 0."),time_sale_date_week:V.number(),require_update_pos:V.boolean()});function ze({open:t,onOpenChange:s,currentRow:a}){var q;const{data:n=[],isLoading:i}=Ie(),{company:l,brands:u}=ge(c=>c.auth),h=u==null?void 0:u[0],{createQuantityDay:y}=Xe(),{updateQuantityDay:g}=Bt(),j=!!a,d=wt({resolver:vt(Zt),defaultValues:{store_uid:(a==null?void 0:a.storeUid)||"",from_date:(a==null?void 0:a.fromDate)||new Date,to_date:(a==null?void 0:a.toDate)||new Date,quantity_per_day:(a==null?void 0:a.quantity)||1,time_sale_date_week:0,item_list:((q=a==null?void 0:a.originalData)==null?void 0:q.item_list)||[],require_update_pos:!1}}),m=d.watch("store_uid"),{data:b=[],isLoading:x}=Ke({params:{list_store_uid:m||void 0},enabled:!!m});oe.useEffect(()=>{n.length>0&&!j&&!m&&d.setValue("store_uid",n[0].id)},[n,j,m,d]),oe.useEffect(()=>{m&&!j&&d.setValue("item_list",[])},[m,d,j]);const E=async c=>{var S,ee,z;if(!(l!=null&&l.id)||!(h!=null&&h.id)){D.error("Thiếu thông tin công ty hoặc thương hiệu");return}const _={require_update:Date.now().toString(),store_uid:c.store_uid,time_sale_date_week:c.time_sale_date_week,quantity_per_day:c.quantity_per_day.toString(),item_list:c.item_list,from_date:c.from_date.getTime(),to_date:c.to_date.getTime(),company_uid:l.id,brand_uid:h.id};if(j&&a){const pe={require_update:Date.now().toString(),store_uid:c.store_uid,time_sale_date_week:c.time_sale_date_week,quantity_per_day:c.quantity_per_day,item_list:c.item_list,from_date:c.from_date.getTime(),to_date:c.to_date.getTime(),brand_uid:h.id,company_uid:l.id,deleted:!1,created_by:((S=a.originalData)==null?void 0:S.created_by)||"system",created_at:((ee=a.originalData)==null?void 0:ee.created_at)||new Date().toISOString(),updated_at:new Date().toISOString(),__v:((z=a.originalData)==null?void 0:z.__v)||0,id:a.id};g(pe)}else y(_);s(!1),d.reset()};return e.jsx(be,{open:t,onOpenChange:c=>{s(c),d.reset()},children:e.jsxs(Se,{className:"top-[50%] w-full max-w-4xl translate-y-[-50%]",children:[e.jsx(Ce,{children:e.jsx(Te,{children:j?"Chỉnh sửa cấu hình":"Tạo số món theo khoảng thời gian"})}),e.jsx(Nt,{...d,children:e.jsxs("form",{onSubmit:d.handleSubmit(E),className:"space-y-4",children:[e.jsx(H,{control:d.control,name:"store_uid",render:({field:c})=>e.jsxs(R,{children:[e.jsx(L,{children:"Cửa hàng *"}),e.jsx(Y,{children:e.jsxs(de,{value:c.value,onValueChange:c.onChange,disabled:i,children:[e.jsx(he,{children:e.jsx(me,{placeholder:i?"Đang tải...":"Chọn cửa hàng"})}),e.jsx(ue,{children:n.map(_=>e.jsx(J,{value:_.id,children:_.name},_.id))})]})}),e.jsx(W,{})]})}),e.jsx(H,{control:d.control,name:"item_list",render:({field:c})=>e.jsxs(R,{children:[e.jsx(L,{children:"Áp dụng cho món"}),e.jsx(Y,{children:e.jsx(Ct,{options:b.map(_=>({label:_.item_name,value:_.item_id})),onValueChange:c.onChange,defaultValue:c.value,placeholder:x?"Đang tải...":"0 món được chọn",variant:"default",animation:.2,maxCount:3,disabled:x})}),e.jsx(W,{})]})}),e.jsx(H,{control:d.control,name:"quantity_per_day",render:({field:c})=>e.jsxs(R,{children:[e.jsx(L,{children:"Số lượng"}),e.jsx(Y,{children:e.jsx(Me,{type:"number",min:"1",placeholder:"Nhập số lượng...",...c,onChange:_=>c.onChange(parseInt(_.target.value)||0)})}),e.jsx(W,{})]})}),e.jsx(H,{control:d.control,name:"require_update_pos",render:({field:c})=>e.jsxs(R,{className:"flex flex-row items-start space-y-0 space-x-3",children:[e.jsx(Y,{children:e.jsx(ce,{checked:c.value,onCheckedChange:c.onChange})}),e.jsx("div",{className:"space-y-1 leading-none",children:e.jsx(L,{children:"Yêu cầu máy POS thực hiện cập nhật lại số lượng trong khai báo ngay sau cập nhập"})})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(H,{control:d.control,name:"from_date",render:({field:c})=>e.jsxs(R,{children:[e.jsx(L,{children:"Ngày bắt đầu"}),e.jsx(Y,{children:e.jsx(Be,{date:c.value,onDateChange:c.onChange,placeholder:"19/07/2025"})}),e.jsx(W,{})]})}),e.jsx(H,{control:d.control,name:"to_date",render:({field:c})=>e.jsxs(R,{children:[e.jsx(L,{children:"Ngày kết thúc"}),e.jsx(Y,{children:e.jsx(Be,{date:c.value,onDateChange:c.onChange,placeholder:"19/07/2025"})}),e.jsx(W,{})]})})]}),e.jsxs(bt,{children:[e.jsx(St,{asChild:!0,children:e.jsx(I,{variant:"outline",type:"button",children:"Hủy"})}),e.jsx(I,{type:"submit",children:"Lưu"})]})]})})]})})}const es=()=>{const t=[["Món áp dụng","Số lượng","Ngày bắt đầu","Ngày kết thúc","Chọn ngày"]],s=O.aoa_to_sheet(t);s["!cols"]=[{width:25},{width:12},{width:18},{width:18},{width:15}];const a={fill:{fgColor:{rgb:"4F81BD"}},font:{color:{rgb:"FFFFFF"},bold:!0},alignment:{horizontal:"center",vertical:"center"}};["A1","B1","C1","D1","E1"].forEach(x=>{s[x]||(s[x]={t:"s",v:""}),s[x].s=a});const i=[["Đây là sheet mẫu để tham khảo. Vui lòng quay lại sheet 1 để nhập dữ liệu."],[""],["Món áp dụng","Số lượng","Ngày bắt đầu","Ngày kết thúc","Chọn ngày"],["ITEM-DF47,ITEM-OTA2,ITEM-2ML3","12","01/02/2023","01/03/2023","38"],["ITEM_AK47,ITEM-RNEA,ITEM-NJK2","20","01/03/2023","01/04/2023","64"],[""],["BẢNG THAM CHIẾU NGÀY","","BẢNG THAM CHIẾU GIỜ"],["Thời gian","Giá trị","Thời gian","Giá trị"],["Chủ nhật","2","0h","1"],["Thứ 2","4","1h","2"],["Thứ 3","8","2h","4"],["Thứ 4","16","3h","8"],["Thứ 5","32","4h","16"],["Thứ 6","64","5h","32"],["Thứ 7","128","6h","64"],["Ví dụ: CN, T2, T5 = 2 + 4 + 32","38","7h","128"],["","","8h","256"],["","","9h","512"],["","","10h","1024"],["","","11h","2048"],["","","12h","4096"],["","","13h","8192"],["","","14h","16384"],["","","15h","32768"],["","","16h","65536"],["","","17h","131072"],["","","18h","262144"],["","","19h","524288"],["","","20h","1048576"],["","","21h","2097152"],["","","22h","4194304"],["","","23h","8388608"],["","","Ví dụ: 0h, 1h, 3h = 1 + 2 + 8","11"]],l=O.aoa_to_sheet(i);l["!cols"]=[{width:35},{width:12},{width:25},{width:12}];const u={fill:{fgColor:{rgb:"4F81BD"}},font:{color:{rgb:"FFFFFF"},bold:!0},alignment:{horizontal:"center",vertical:"center"}},h={fill:{fgColor:{rgb:"D9E2F3"}},font:{bold:!0},alignment:{horizontal:"center",vertical:"center"}},y={fill:{fgColor:{rgb:"4F81BD"}},font:{color:{rgb:"FFFFFF"},bold:!0},alignment:{horizontal:"center",vertical:"center"}},g={fill:{fgColor:{rgb:"E7F3FF"}},alignment:{horizontal:"center",vertical:"center"}};l.A1||(l.A1={t:"s",v:""}),l.A1.s=u,["A3","B3","C3","D3","E3"].forEach(x=>{l[x]||(l[x]={t:"s",v:""}),l[x].s=h}),["A4","B4","C4","D4","E4","A5","B5","C5","D5","E5"].forEach(x=>{l[x]||(l[x]={t:"s",v:""}),l[x].s=g}),["A7","C7","A8","B8","C8","D8"].forEach(x=>{l[x]||(l[x]={t:"s",v:""}),l[x].s=y});const b=O.book_new();O.book_append_sheet(b,s,"Template"),O.book_append_sheet(b,l,"Example"),b.Props={Title:"Template cấu hình số lượng món trong ngày",Subject:"POS System - Quantity Day Configuration",Author:"POS System",CreatedDate:new Date},kt(b,"template_cau_hinh_so_luong_mon")};function ts({open:t,onOpenChange:s}){const{data:a=[]}=Ie(),{company:n,brands:i}=ge(r=>r.auth),l=i==null?void 0:i[0],{createQuantityDay:u}=Xe(),[h,y]=T.useState(""),[g,j]=T.useState(null),[d,m]=T.useState([]),[b,x]=T.useState(!1),[E,q]=T.useState(!1),[c,_]=T.useState(!1),S=T.useRef(null),ee=r=>{m(o=>o.filter(w=>w.id!==r))},z=r=>{_(r),!r&&d.length>0&&s(!0)},pe=()=>{try{es(),D.success("Đã tải file mẫu thành công")}catch(r){console.error("Error creating template:",r),D.error("Lỗi khi tạo file mẫu")}},Je=r=>{var f;const o=(f=r.target.files)==null?void 0:f[0];if(!o)return;if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(o.type)&&!o.name.match(/\.(xlsx|xls)$/i)){D.error("File phải có định dạng Excel (.xlsx hoặc .xls)");return}j(o),et(o)},te=r=>{if(!r)return null;const o=r.toString().trim();if(/^\d+$/.test(o))try{return new Date((parseInt(o)-25569)*86400*1e3)}catch{return null}const w=o.split("/");if(w.length!==3)return null;const f=parseInt(w[0]),N=parseInt(w[1]),F=parseInt(w[2]);if(isNaN(f)||isNaN(N)||isNaN(F)||f<1||f>31||N<1||N>12||F<1900||F>2100)return null;const M=new Date(F,N-1,f);return M.getFullYear()===F&&M.getMonth()===N-1&&M.getDate()===f?M:null},Pe=r=>{if(!r)return"";const o=r.toString().trim();if(/^\d+$/.test(o))try{const w=new Date((parseInt(o)-25569)*86400*1e3);return`${w.getDate()}/${w.getMonth()+1}/${w.getFullYear()}`}catch{return o}return o},Ze=r=>{if(r===0)return"Tất cả ngày trong tuần";const o=["Chủ nhật","Thứ 2","Thứ 3","Thứ 4","Thứ 5","Thứ 6","Thứ 7"],w=[2,4,8,16,32,64,128],f=[];for(let N=0;N<7;N++)r&w[N]&&f.push(o[N]);return f.join(", ")},fe=r=>{if(!r||r.trim()==="")return{isValid:!0,bitFlag:0};const o=parseInt(r.trim());if(isNaN(o))return{isValid:!1,bitFlag:0,errorMessage:"Thời gian không hợp lệ"};if(o<0||o>254)return{isValid:!1,bitFlag:0,errorMessage:"Thời gian không hợp lệ"};if(o>0){if((o&254)!==o)return{isValid:!1,bitFlag:0,errorMessage:"Thời gian không hợp lệ"};const f=[2,4,8,16,32,64,128];let N=0;for(const F of f)o&F&&(N|=F);if(N!==o)return{isValid:!1,bitFlag:0,errorMessage:"Thời gian không hợp lệ"}}return{isValid:!0,bitFlag:o}},et=async r=>{const o=new FileReader;o.onload=async w=>{var f,N,F,M,ye,se;try{const A=new Uint8Array((f=w.target)==null?void 0:f.result),ae=Tt(A,{type:"array"}),tt=ae.SheetNames[0],st=ae.Sheets[tt],ne=O.sheet_to_json(st,{header:1,defval:"",blankrows:!1});if(!ne||ne.length<2){D.error("File không có dữ liệu hoặc chỉ có header");return}const je=["Món áp dụng","Số lượng","Ngày bắt đầu","Ngày kết thúc","Chọn ngày"],Ue=ne[0];for(let k=0;k<je.length;k++)if(!Ue[k]||Ue[k].toString().trim()!==je[k]){D.error(`Cột ${k+1} phải có tiêu đề "${je[k]}"`);return}const He=ne.slice(1),_e=[];for(let k=0;k<He.length;k++){const K=He[k],at=k+2;let Q=!0,P="";const ie=((N=K[0])==null?void 0:N.toString().trim())||"",we=parseInt(((F=K[1])==null?void 0:F.toString())||"0"),Ne=((M=K[2])==null?void 0:M.toString().trim())||"",De=((ye=K[3])==null?void 0:ye.toString().trim())||"",ve=((se=K[4])==null?void 0:se.toString().trim())||"";if(!ie)Q=!1,P="Món áp dụng không được để trống";else if(isNaN(we)||we<=0)Q=!1,P="Số lượng phải là số nguyên dương";else if(!te(Ne))Q=!1,P="Ngày bắt đầu không hợp lệ (DD/MM/YYYY)";else if(!te(De))Q=!1,P="Ngày kết thúc không hợp lệ (DD/MM/YYYY)";else{const U=fe(ve);U.isValid||(Q=!1,P=U.errorMessage||"Thời gian không hợp lệ")}if(Q&&h&&ie)try{const U=ie.split(",").map(G=>G.trim()),it=await Ft.getItemsInStore({store_uid:h,skip_limit:!0,company_uid:(n==null?void 0:n.id)||"",brand_uid:(l==null?void 0:l.id)||""}),rt=new Set((it.data||[]).map(G=>G.item_id)),Re=U.filter(G=>!rt.has(G));Re.length>0&&(Q=!1,P=`Món không hợp lệ: ${Re.join(", ")}`)}catch(U){console.error("Error validating items:",U)}const nt=fe(ve).bitFlag;_e.push({id:`row-${at}`,appliedItems:ie,quantity:we,startDate:Pe(Ne),endDate:Pe(De),startDateRaw:Ne,endDateRaw:De,selectedDays:Ze(nt),selectedDaysRaw:ve,isValid:Q,errorMessage:P})}m(_e),_(!0),s(!1),D.success(`Đã đọc ${_e.length} dòng dữ liệu từ file`)}catch(A){console.error("Error parsing Excel file:",A),D.error(`Lỗi khi đọc file Excel: ${A instanceof Error?A.message:"Unknown error"}`)}},o.readAsArrayBuffer(r)},$e=async()=>{if(!h){D.error("Vui lòng chọn cửa hàng");return}const r=d.filter(o=>o.isValid);if(r.length===0){D.error("Không có dữ liệu hợp lệ để import");return}q(!0);try{let o=0,w=0;for(const f of r)try{const N=f.appliedItems.split(",").map(ae=>ae.trim()),F=te(f.startDateRaw),M=te(f.endDateRaw);if(!F||!M){console.error("Failed to parse dates:",{startDateRaw:f.startDateRaw,endDateRaw:f.endDateRaw}),w++;continue}const se=fe(f.selectedDaysRaw).bitFlag,A={require_update:b?"true":"false",store_uid:h,time_sale_date_week:se,quantity_per_day:f.quantity,item_list:N,from_date:F.getTime(),to_date:M.getTime(),company_uid:(n==null?void 0:n.id)||"",brand_uid:(l==null?void 0:l.id)||""};u(A),o++}catch(N){console.error("Error creating quantity day:",N),w++}o>0&&D.success(`Đã thêm thành công ${o} cấu hình`),w>0&&D.error(`${w} cấu hình bị lỗi`),o>0&&(y(""),j(null),m([]),_(!1),x(!1))}catch(o){console.error("Import error:",o),D.error("Lỗi khi import dữ liệu")}finally{q(!1)}};return e.jsxs(e.Fragment,{children:[e.jsx(be,{open:t,onOpenChange:s,children:e.jsxs(Se,{className:"w-[90vw] max-w-4xl",children:[e.jsx(Ce,{children:e.jsx(Te,{children:"Cấu hình số lượng món trong ngày"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"bg-muted/50 rounded-lg p-4",children:[e.jsx("h3",{className:"mb-3 font-medium",children:"Bước 1. Chọn cửa hàng"}),e.jsxs(de,{value:h,onValueChange:y,children:[e.jsx(he,{children:e.jsx(me,{placeholder:"Chọn cửa hàng"})}),e.jsx(ue,{children:a.map(r=>e.jsx(J,{value:r.id,children:r.name},r.id))})]})]})}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"bg-muted/50 rounded-lg p-4",children:[e.jsx("h3",{className:"mb-3 font-medium",children:"Bước 2. Tải file mẫu"}),e.jsxs(I,{variant:"outline",onClick:pe,className:"w-full",children:[e.jsx(It,{className:"mr-2 h-4 w-4"}),"Tải xuống"]})]})}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"bg-muted/50 rounded-lg p-4",children:[e.jsx("h3",{className:"mb-3 font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsx("p",{className:"text-muted-foreground mb-3 text-sm",children:"Thêm hoặc sửa thông tin món dựa vào sheet mẫu."}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(ce,{id:"require-update",checked:b,disabled:!h,onCheckedChange:r=>x(r)}),e.jsx(Dt,{htmlFor:"require-update",className:`text-sm ${h?"":"text-muted-foreground"}`,children:"Yêu cầu POS cập nhật lại số lượng trong khai báo ngày lập tức"})]})]})}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"bg-muted/50 rounded-lg p-4",children:[e.jsx("h3",{className:"mb-3 font-medium",children:"Bước 4. Tải file lên"}),e.jsx("p",{className:"text-muted-foreground mb-3 text-sm",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(Me,{ref:S,type:"file",accept:".xlsx,.xls",onChange:Je,className:"hidden"}),e.jsxs(I,{variant:"outline",onClick:()=>{var r;return(r=S.current)==null?void 0:r.click()},className:"w-full",children:[e.jsx(Fe,{className:"mr-2 h-4 w-4"}),g?g.name:"Chọn file"]}),d.length>0&&e.jsxs("div",{className:"mt-4",children:[e.jsxs("p",{className:"text-muted-foreground mb-3 text-sm",children:["Đã đọc ",d.length," dòng dữ liệu (",d.filter(r=>r.isValid).length," hợp lệ, ",d.filter(r=>!r.isValid).length," lỗi)"]}),e.jsxs(I,{onClick:$e,disabled:!h||E||!d.some(r=>r.isValid),className:"w-full",children:[E?"Đang xử lý...":"Lưu",e.jsx(Fe,{className:"ml-2 h-4 w-4"})]})]})]})]})})]})]})}),e.jsx(be,{open:c,onOpenChange:z,children:e.jsxs(Se,{className:"max-h-[95vh] w-[98vw] max-w-[98vw] overflow-hidden",children:[e.jsx(Ce,{children:e.jsx(Te,{children:"Preview dữ liệu"})}),e.jsx("div",{className:"max-h-[80vh] overflow-auto",children:e.jsxs(Ve,{children:[e.jsx(Ee,{children:e.jsxs($,{children:[e.jsx(C,{className:"w-12"}),e.jsx(C,{className:"min-w-[200px]",children:"Món áp dụng"}),e.jsx(C,{className:"w-24 text-center",children:"Số lượng"}),e.jsx(C,{className:"w-32 text-center",children:"Ngày bắt đầu"}),e.jsx(C,{className:"w-32 text-center",children:"Ngày kết thúc"}),e.jsx(C,{className:"min-w-[150px]",children:"Chọn ngày"})]})}),e.jsx(Qe,{children:d.map(r=>e.jsxs($,{className:r.isValid?"":"border-red-200 bg-red-50",children:[e.jsx(v,{children:e.jsx(I,{variant:"ghost",size:"sm",onClick:()=>ee(r.id),className:"h-6 w-6 p-0",children:e.jsx(Ge,{className:"h-3 w-3"})})}),e.jsx(v,{children:e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:r.appliedItems}),!r.isValid&&r.errorMessage&&e.jsx("div",{className:"mt-1 text-xs text-red-600",children:r.errorMessage})]})}),e.jsx(v,{className:"text-center",children:r.quantity}),e.jsx(v,{className:"text-center",children:r.startDate}),e.jsx(v,{className:"text-center",children:r.endDate}),e.jsx(v,{children:r.selectedDays})]},r.id))})]})}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsxs("div",{className:"text-muted-foreground text-sm",children:[d.filter(r=>r.isValid).length," hợp lệ, ",d.filter(r=>!r.isValid).length," ","lỗi"]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(I,{variant:"outline",onClick:()=>z(!1),children:"Đóng"}),e.jsx(I,{onClick:$e,disabled:!h||E||!d.some(r=>r.isValid),children:E?"Đang xử lý...":"Lưu"})]})]})]})})]})}function ss(){const{open:t,setOpen:s,currentRow:a,setCurrentRow:n}=Ae(),{deleteQuantityDay:i}=Ot();return e.jsxs(e.Fragment,{children:[e.jsx(ze,{open:t==="create",onOpenChange:()=>s("create")},"quantity-day-create"),e.jsx(ts,{open:t==="import-from-file",onOpenChange:()=>s("import-from-file")},"quantity-day-import-from-file"),a&&e.jsxs(e.Fragment,{children:[e.jsx(ze,{open:t==="update",onOpenChange:()=>{s("update"),setTimeout(()=>{n(null)},500)},currentRow:a},`quantity-day-update-${a.id}`),e.jsx(_t,{destructive:!0,open:t==="delete",onOpenChange:()=>{s("delete"),setTimeout(()=>{n(null)},500)},handleConfirm:async()=>{s(null),setTimeout(()=>{n(null)},500),i(a.id)},className:"max-w-md",title:"Bạn có muốn xoá cấu hình này?",desc:e.jsx(e.Fragment,{children:"Hành động không thể hoàn tác."}),confirmText:"Xoá"},"quantity-day-delete")]})]})}function as(){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[e.jsx(p,{className:"h-8 w-[250px]"}),e.jsx(p,{className:"h-8 w-[180px]"})]}),e.jsx(p,{className:"h-8 w-[70px]"})]}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(Ve,{children:[e.jsx(Ee,{children:e.jsxs($,{children:[e.jsx(C,{className:"w-[50px]",children:e.jsx(p,{className:"h-4 w-4"})}),e.jsx(C,{className:"w-[50px]",children:e.jsx(p,{className:"h-4 w-6"})}),e.jsx(C,{children:e.jsx(p,{className:"h-4 w-20"})}),e.jsx(C,{children:e.jsx(p,{className:"h-4 w-16"})}),e.jsx(C,{children:e.jsx(p,{className:"h-4 w-20"})}),e.jsx(C,{children:e.jsx(p,{className:"h-4 w-20"})}),e.jsx(C,{children:e.jsx(p,{className:"h-4 w-24"})}),e.jsx(C,{className:"w-[50px]"})]})}),e.jsx(Qe,{children:Array.from({length:5}).map((t,s)=>e.jsxs($,{children:[e.jsx(v,{children:e.jsx(p,{className:"h-4 w-4"})}),e.jsx(v,{children:e.jsx(p,{className:"h-4 w-6"})}),e.jsx(v,{children:e.jsx(p,{className:"h-4 w-12"})}),e.jsx(v,{children:e.jsx(p,{className:"h-4 w-20"})}),e.jsx(v,{children:e.jsx(p,{className:"h-4 w-20"})}),e.jsx(v,{children:e.jsx(p,{className:"h-4 w-16"})}),e.jsx(v,{children:e.jsx(p,{className:"h-4 w-32"})}),e.jsx(v,{children:e.jsx(p,{className:"h-8 w-8"})})]},s))})]})}),e.jsxs("div",{className:"flex items-center justify-between px-2",children:[e.jsx(p,{className:"h-4 w-32"}),e.jsxs("div",{className:"flex items-center space-x-6 lg:space-x-8",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(p,{className:"h-4 w-24"}),e.jsx(p,{className:"h-8 w-16"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(p,{className:"h-8 w-8"}),e.jsx(p,{className:"h-8 w-8"}),e.jsx(p,{className:"h-8 w-8"}),e.jsx(p,{className:"h-8 w-8"})]})]})]})]})}function ns({columns:t,data:s,storesData:a=[],selectedStoreUid:n="all",onStoreChange:i,selectedItemUid:l="all",onItemChange:u,itemsData:h=[]}){var c;const[y,g]=T.useState({}),[j,d]=T.useState({}),[m,b]=T.useState([]),[x,E]=T.useState([]),q=qt({data:s,columns:t,state:{sorting:x,columnVisibility:j,rowSelection:y,columnFilters:m},enableRowSelection:!0,onRowSelectionChange:g,onSortingChange:E,onColumnFiltersChange:b,onColumnVisibilityChange:d,getCoreRowModel:Pt(),getFilteredRowModel:At(),getPaginationRowModel:Qt(),getSortedRowModel:Et(),getFacetedRowModel:Vt(),getFacetedUniqueValues:Mt()});return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between space-y-2 gap-x-4",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Cấu hình số lượng món trong ngày"}),e.jsx("p",{className:"text-muted-foreground text-sm",children:"Việc thay đổi số lượng món trong ngày với những món đã được cấu hình trước đó có thể ngày hôm sau mới đước áp dụng!"})]}),e.jsx(Wt,{})]}),e.jsx(Jt,{table:q,storesData:a,selectedStoreUid:n,onStoreChange:i,selectedItemUid:l,onItemChange:u,itemsData:h}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(Ve,{children:[e.jsx(Ee,{children:q.getHeaderGroups().map(_=>e.jsx($,{children:_.headers.map(S=>e.jsx(C,{colSpan:S.colSpan,children:S.isPlaceholder?null:Oe(S.column.columnDef.header,S.getContext())},S.id))},_.id))}),e.jsx(Qe,{children:(c=q.getRowModel().rows)!=null&&c.length?q.getRowModel().rows.map(_=>e.jsx($,{"data-state":_.getIsSelected()&&"selected",children:_.getVisibleCells().map(S=>e.jsx(v,{children:Oe(S.column.columnDef.cell,S.getContext())},S.id))},_.id)):e.jsx($,{children:e.jsx(v,{colSpan:t.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]})}),e.jsx($t,{table:q})]})}function is(){var j;const[t,s]=T.useState(""),[a,n]=T.useState("all"),{data:i}=Ie(),{data:l}=Ke();T.useEffect(()=>{i&&i.length>0&&!t&&s(i[0].id)},[i,t]);const u=a!=="all"?(j=l==null?void 0:l.find(d=>d.id===a))==null?void 0:j.item_id:void 0,{data:h,isLoading:y,error:g}=zt({params:{store_uid:t||void 0,list_item_id:u},enabled:!!t});return g?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-muted-foreground mb-2 text-sm",children:"Có lỗi xảy ra khi tải dữ liệu cấu hình số lượng"}),e.jsx("button",{onClick:()=>window.location.reload(),className:"text-primary text-sm hover:underline",children:"Thử lại"})]})}):e.jsxs(Kt,{children:[e.jsx(ot,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ht,{}),e.jsx(mt,{}),e.jsx(dt,{})]})}),e.jsx(ct,{children:e.jsxs("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12",children:[y&&e.jsx(as,{}),!y&&e.jsx(ns,{columns:Xt,data:h,storesData:i,itemsData:l,onItemChange:n,onStoreChange:s,selectedStoreUid:t,selectedItemUid:a})]})}),e.jsx(ss,{})]})}const _a=is;export{_a as component};
