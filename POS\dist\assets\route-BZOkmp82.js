import{j as r,O as t}from"./index-C21OP4ex.js";import{C as i}from"./index-BL2y93Se.js";import"./date-range-picker-B1pgj5D_.js";import"./search-context-DtMZc3QX.js";import"./pos-api-D5WM5mnz.js";import"./form-usWdQ_Nt.js";import"./main-DRnqW_wu.js";import"./index-B1tAtzRS.js";import"./calendar-BiBi2kQF.js";import"./createLucideIcon-CL0CQOA1.js";import"./index-Ct3V_iCU.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-BAjIoZMb.js";import"./react-icons.esm-DB-kGUq7.js";import"./popover-BtedB187.js";import"./select-B8Pw9rS-.js";import"./index-Bh-UeytL.js";import"./index-DuT2Ibxp.js";import"./check-DcHT8QEO.js";import"./command-BnLmWlRk.js";import"./dialog-DXjwjGKV.js";import"./search-DHRhj6_i.js";import"./createReactComponent-zh6rKAzG.js";import"./scroll-area-DKiYF9x5.js";import"./IconChevronRight-Bwbz4HuV.js";const o="...",K=function(){return r.jsx(i,{publishableKey:o,afterSignOutUrl:"/clerk/sign-in",signInUrl:"/clerk/sign-in",signUpUrl:"/clerk/sign-up",signInFallbackRedirectUrl:"/clerk/user-management",signUpFallbackRedirectUrl:"/clerk/user-management",children:r.jsx(t,{})})};export{K as component};
