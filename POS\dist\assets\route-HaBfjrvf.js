import{j as r,O as t}from"./index-Bnt3OGV2.js";import{C as i}from"./index-CnN0oYdG.js";import"./date-range-picker-CVvofQC0.js";import"./search-context-DLufo9i0.js";import"./pos-api-BwpRFGce.js";import"./form-wT1R35uI.js";import"./main-Dj7NWzIf.js";import"./index-Bw0HHept.js";import"./calendar-CzR6WBaB.js";import"./createLucideIcon-CNa_hh6B.js";import"./index-BT7Z3RDV.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-sZt3EK3r.js";import"./react-icons.esm-B7rNr9e-.js";import"./popover-C2mvzdeD.js";import"./select-Czd7KcZQ.js";import"./index-Bl1CGAiZ.js";import"./index-C2T2k_Lh.js";import"./check-apx2eTVC.js";import"./command-ByfqjQDn.js";import"./dialog-hQ-PVOWr.js";import"./search-BVQVKwPC.js";import"./createReactComponent-BD5R5KSl.js";import"./scroll-area-BeVbW7LP.js";import"./IconChevronRight-BM-o6vT_.js";const o="...",K=function(){return r.jsx(i,{publishableKey:o,afterSignOutUrl:"/clerk/sign-in",signInUrl:"/clerk/sign-in",signUpUrl:"/clerk/sign-up",signInFallbackRedirectUrl:"/clerk/user-management",signUpFallbackRedirectUrl:"/clerk/user-management",children:r.jsx(t,{})})};export{K as component};
