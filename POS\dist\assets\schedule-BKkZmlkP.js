import{r as S,j as e,B as I,a4 as Q,a3 as te,z as m,ax as Ve,u as be,R as se,c as ue}from"./index-C21OP4ex.js";import{H as ze}from"./header-DNPEfjkR.js";import{M as Le}from"./main-DRnqW_wu.js";import{P as Oe}from"./profile-dropdown-DlMxjxHH.js";import{S as Ke,T as He}from"./search-5KGATEvM.js";import{D as Z}from"./data-table-column-header-7_8s6zRv.js";import{T as ve}from"./trash-2-C_5rhUMO.js";import{u as $e,g as Qe,a as Be,b as Ue,c as Ge,d as Xe,e as We,f as me}from"./index-B_FCwlUM.js";import{T as ne,a as ae,b as G,c as q,d as ie,e as E}from"./table-BIu4Pah2.js";import{D as Je}from"./data-table-pagination-BKENav2P.js";import{a as K}from"./pos-api-D5WM5mnz.js";import{S as V,a as z,b as L,c as O,d as F}from"./select-B8Pw9rS-.js";import{X as ce,C as he}from"./calendar-BiBi2kQF.js";import{S as f}from"./skeleton-xUyFo20h.js";import{P as Ye}from"./plus-C1IEs-Ov.js";import{C as Ze}from"./confirm-dialog-AXI5ANMc.js";import"./vietqr-api-ruJT0-tj.js";import{u as de}from"./useMutation-Bh5DVQPI.js";import{u as Ne,L as we,a as _,b as p,c as h,d as g,e as j,F as Ce}from"./form-usWdQ_Nt.js";import{s as Se}from"./zod-B4gLZVLM.js";import{u as Me}from"./use-removed-items-Ck-YNHyo.js";import{u as le}from"./use-stores-BQdEFBhG.js";import{I as $}from"./input-4sMIt001.js";import{u as Re}from"./use-items-zU7JIOkv.js";import"./user-CgNoQQSj.js";import"./crm-api-BUMUQ8t4.js";import{u as De}from"./use-customizations-ZELiZ0he.js";import{C as U}from"./checkbox-DUpnJ1Rx.js";import{T as Te}from"./textarea-BLrSKk17.js";import{u as es}from"./use-item-types-DA0U4OWS.js";import{u as ss}from"./use-item-classes-_IC65iw9.js";import{u as ts}from"./use-units-dM9GTfDw.js";import{P as xe,a as _e,b as pe}from"./popover-BtedB187.js";import{C as je}from"./calendar-BszJEazX.js";import{f as ye}from"./isSameMonth-C8JQo-AN.js";import{v as fe}from"./date-range-picker-B1pgj5D_.js";import{b as ns}from"./use-auth-DBc6rGI6.js";import{u as as}from"./useQuery-BNGphiae.js";import"./separator-ZoxOB1XH.js";import"./avatar-C98m2_SM.js";import"./dropdown-menu-DINyPmco.js";import"./index-Bh-UeytL.js";import"./index-Ct3V_iCU.js";import"./index-UJ-79IIJ.js";import"./check-DcHT8QEO.js";import"./createLucideIcon-CL0CQOA1.js";import"./search-context-DtMZc3QX.js";import"./command-BnLmWlRk.js";import"./dialog-DXjwjGKV.js";import"./search-DHRhj6_i.js";import"./createReactComponent-zh6rKAzG.js";import"./scroll-area-DKiYF9x5.js";import"./IconChevronRight-Bwbz4HuV.js";import"./IconSearch-CyZD7dtp.js";import"./react-icons.esm-DB-kGUq7.js";import"./index-DuT2Ibxp.js";import"./alert-dialog-CzdUByb-.js";import"./utils-km2FGkQ4.js";import"./query-keys-3lmd-xp6.js";import"./stores-api-BIve2jSO.js";import"./item-api-C8PXkgMG.js";import"./chevron-right-BAjIoZMb.js";const ke=S.createContext(void 0);function is({children:t}){const[a,n]=S.useState(null),[c,s]=S.useState(null),i={open:a,setOpen:n,currentRow:c,setCurrentRow:s};return e.jsx(ke.Provider,{value:i,children:t})}function J(){const t=S.useContext(ke);if(t===void 0)throw new Error("useMenuSchedule must be used within a MenuScheduleProvider");return t}function cs({row:t}){const{setOpen:a,setCurrentRow:n}=J(),c=t.original,s=i=>{i.stopPropagation(),n(c),a("delete")};return e.jsxs(I,{variant:"ghost",size:"sm",onClick:s,className:"h-8 w-8 p-0 text-red-600 hover:bg-red-50 hover:text-red-700",children:[e.jsx(ve,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Xóa"})]})}const ds=[{id:"index",header:"#",cell:({row:t})=>e.jsx("div",{className:"w-[50px]",children:t.index+1}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"time_formatted",header:({column:t})=>e.jsx(Z,{column:t,title:"Từ ngày"}),cell:({row:t})=>e.jsx("div",{className:"text-sm font-medium",children:t.getValue("time_formatted")}),enableSorting:!0,enableHiding:!1},{accessorKey:"end_time_formatted",header:({column:t})=>e.jsx(Z,{column:t,title:"Đến ngày"}),cell:({row:t})=>e.jsx("div",{className:"text-sm font-medium",children:t.getValue("end_time_formatted")}),enableSorting:!0,enableHiding:!1},{accessorKey:"city_name",header:({column:t})=>e.jsx(Z,{column:t,title:"Thành phố"}),cell:({row:t})=>e.jsx("div",{className:"text-sm",children:t.getValue("city_name")}),enableSorting:!1,enableHiding:!0},{accessorKey:"store_name",header:({column:t})=>e.jsx(Z,{column:t,title:"Cửa hàng"}),cell:({row:t})=>e.jsx("div",{className:"text-sm",children:t.getValue("store_name")}),enableSorting:!1,enableHiding:!0},{id:"actions",cell:({row:t})=>e.jsx(cs,{row:t}),enableSorting:!1,enableHiding:!1}],ls=async(t={})=>{var c;const a={};Object.entries(t).forEach(([s,i])=>{i!=null&&i!==""&&(a[s]=i)}),a.page||(a.page=1);const n=await K.get("/mdata/v1/item-schedules",{params:a});return((c=n.data)==null?void 0:c.data)||n.data||[]},rs=async t=>(await K.get("/mdata/v1/item-schedule",{params:t})).data,os=async t=>(await K.get(`/mdata/v1/item-schedules/${t}`)).data,us=async t=>(await K.post("/mdata/v1/item-schedule",t)).data,Pe=(t,a)=>a.map(n=>{var s,i,d,r,l;const c={company_uid:t.company_uid,brand_uid:t.brand_uid,type:"item",action:n.action,item_id:n.code,store_uid:t.store_uid,city_uid:t.city_uid,time:t.start_date.getTime(),end_time:((s=t.end_date)==null?void 0:s.getTime())||null};return n.action==="UPDATE"&&n.originalItem?{...c,item_uid:n.originalItem.id,changed_data:{...n.originalItem,item_name:n.name,ots_price:n.ots_price||n.originalItem.ots_price,ots_tax:n.vat_rate||n.originalItem.ots_tax,time_cooking:n.cooking_time||n.originalItem.time_cooking,unit_uid:n.unit_uid||n.originalItem.unit_uid,unit_secondary_uid:n.unit_secondary_uid||n.originalItem.unit_secondary_uid,item_type_uid:n.item_type_uid||n.originalItem.item_type_uid,item_class_uid:n.item_class_uid||n.originalItem.item_class_uid,customization_uid:n.customization_uid||n.originalItem.customization_uid,sort:n.display_order||n.originalItem.sort,extra_data:{...n.originalItem.extra_data,enable_edit_price:n.allow_edit_price?1:((i=n.originalItem.extra_data)==null?void 0:i.enable_edit_price)||0,no_update_quantity_toping:n.require_quantity_input?0:((d=n.originalItem.extra_data)==null?void 0:d.no_update_quantity_toping)||1,is_buffet_item:n.is_buffet?1:((r=n.originalItem.extra_data)==null?void 0:r.is_buffet_item)||0,is_item_service:n.is_service?1:((l=n.originalItem.extra_data)==null?void 0:l.is_item_service)||0},apply_with_store:1,store_uid:t.store_uid,city_uid:t.city_uid}}:{...c,changed_data:{unit_uid:n.unit_uid||"",ta_price:0,ots_tax:n.vat_rate||0,ta_tax:0,extra_data:{price_by_source:[],is_virtual_item:0,is_item_service:n.is_service?1:0,no_update_quantity_toping:n.require_quantity_input?0:1,enable_edit_price:n.allow_edit_price?1:0,is_buffet_item:n.is_buffet?1:0,exclude_items_buffet:[],up_size_buffet:[]},city_uid:t.city_uid,store_uid:t.store_uid,item_type_uid:n.item_type_uid||"",item_name:n.name,company_uid:t.company_uid,brand_uid:t.brand_uid,item_id:n.code,ots_price:n.ots_price||0,image_path_thumb:null,customization_uid:n.customization_uid||null,item_class_uid:n.item_class_uid||null,unit_secondary_uid:n.unit_secondary_uid||null,sort:n.display_order||1e3,apply_with_store:2}}}),ms=async(t,a)=>(await K.put(`/mdata/v1/item-schedules/${t}`,a)).data,hs=async t=>(await K.put("/mdata/v1/item-schedule",t)).data,xs=async t=>{const a={time:t.time,store_uid:t.store_uid,city_uid:t.city_uid,brand_uid:t.brand_uid,company_uid:t.company_uid};await K.delete("/mdata/v1/item-schedule",{params:a})},_s=async t=>(await K.put("/mdata/v1/item-schedule",t)).data,ps=async(t,a,n={})=>{var i;const c={start_date:t,end_date:a};Object.entries(n).forEach(([d,r])=>{r!=null&&r!==""&&(c[d]=r)});const s=await K.get("/mdata/v1/item-schedules",{params:c});return((i=s.data)==null?void 0:i.data)||s.data||[]},js=async(t,a)=>{var s;const n={store_uid:t};a&&(n.date=a);const c=await K.get("/mdata/v1/item-schedules",{params:n});return((s=c.data)==null?void 0:s.data)||c.data||[]},Y={getMenuSchedules:ls,getMenuScheduleById:os,getMenuScheduleByParams:rs,createMenuSchedule:us,createMenuSchedulePayload:Pe,updateMenuSchedule:ms,updateItemSchedule:hs,deleteMenuSchedule:xs,deleteItemSchedule:_s,getMenuSchedulesByDateRange:ps,getActiveMenuSchedulesForStore:js},ys=t=>{var n,c;const a=t.changed_data||{};return{id:a.id||t.id,code:t.item_id,name:a.item_name||"",action:t.action,originalItem:a,scheduleId:t.id,item_class_uid:a.item_class_uid||void 0,item_type_uid:a.item_type_uid,unit_uid:a.unit_uid,unit_secondary_uid:a.unit_secondary_uid||void 0,ots_price:a.ots_price,customization_uid:a.customization_uid||void 0,cooking_time:a.time_cooking,allow_edit_price:!!((n=a.extra_data)!=null&&n.enable_edit_price),is_buffet:!!((c=a.extra_data)!=null&&c.is_buffet_item),is_service:!!a.is_service,display_order:a.list_order,time_sale_date_week:a.time_sale_date_week,time_sale_hour_day:a.time_sale_hour_day}},fs=()=>({fetchMenuScheduleData:async a=>{var n,c;try{const s={company_uid:a.company_uid,brand_uid:a.brand_uid,time:a.time,end_time:a.end_time,store_uid:a.store_uid,city_uid:a.city_uid},i=await Y.getMenuScheduleByParams(s);if(((c=(n=i.data)==null?void 0:n.item_schedules)==null?void 0:c.length)>0){const d=i.data.item_schedules.map(ys);return{...a,status:i.data.status,menuItems:d}}else return Q.error("Không tìm thấy dữ liệu"),null}catch{return Q.error("Có lỗi xảy ra khi tải dữ liệu"),null}}});function gs({table:t,selectedStatus:a,onStatusChange:n}){const c=t.getState().columnFilters.length>0;return e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex flex-1 flex-col gap-2 sm:flex-row sm:items-center sm:space-x-2",children:[e.jsxs(V,{value:a,onValueChange:n,children:[e.jsx(z,{className:"h-8 w-[200px]",children:e.jsx(L,{placeholder:"Trạng thái"})}),e.jsxs(O,{children:[e.jsx(F,{value:"all",children:"Tất cả"}),e.jsx(F,{value:"PENDING",children:"Lịch sắp diễn ra"}),e.jsx(F,{value:"PROCESS",children:"Lịch đang diễn ra"}),e.jsx(F,{value:"DONE",children:"Lịch đã hoàn thành"})]})]}),c&&e.jsxs(I,{variant:"ghost",onClick:()=>t.resetColumnFilters(),className:"h-8 px-2 lg:px-3",children:["Đặt lại",e.jsx(ce,{className:"ml-2 h-4 w-4"})]})]})})}function bs({columns:t,data:a,selectedStatus:n,onStatusChange:c}){var k;const[s,i]=S.useState({}),[d,r]=S.useState({}),[l,o]=S.useState([]),[N,M]=S.useState([]),[x,y]=S.useState(!1),{setOpen:v,setCurrentRow:w}=J(),{fetchMenuScheduleData:T}=fs(),A=$e({data:a,columns:t,state:{sorting:N,columnVisibility:d,rowSelection:s,columnFilters:l},enableRowSelection:!0,onRowSelectionChange:i,onSortingChange:M,onColumnFiltersChange:o,onColumnVisibilityChange:r,getCoreRowModel:We(),getFilteredRowModel:Xe(),getPaginationRowModel:Ge(),getSortedRowModel:Ue(),getFacetedRowModel:Be(),getFacetedUniqueValues:Qe()}),W=async C=>{const b=C.original;y(!0);try{const P=await T(b);P&&(w(P),v("update"))}finally{y(!1)}};return e.jsxs("div",{className:"space-y-4",children:[e.jsx(gs,{table:A,selectedStatus:n,onStatusChange:c}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(ne,{children:[e.jsx(ae,{children:A.getHeaderGroups().map(C=>e.jsx(G,{children:C.headers.map(b=>e.jsx(q,{colSpan:b.colSpan,children:b.isPlaceholder?null:me(b.column.columnDef.header,b.getContext())},b.id))},C.id))}),e.jsx(ie,{children:(k=A.getRowModel().rows)!=null&&k.length?A.getRowModel().rows.map(C=>e.jsx(G,{"data-state":C.getIsSelected()&&"selected",className:`hover:bg-muted/50 cursor-pointer ${x?"pointer-events-none opacity-50":""}`,onClick:()=>!x&&W(C),children:C.getVisibleCells().map(b=>e.jsx(E,{onClick:P=>{b.column.id==="actions"&&P.stopPropagation()},children:me(b.column.columnDef.cell,b.getContext())},b.id))},C.id)):e.jsx(G,{children:e.jsx(E,{colSpan:t.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]})}),e.jsx(Je,{table:A})]})}function vs(){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-1 flex-col gap-2 sm:flex-row sm:items-center sm:space-x-2",children:[e.jsx(f,{className:"h-8 w-[250px]"}),e.jsx(f,{className:"h-8 w-[150px]"}),e.jsx(f,{className:"h-8 w-[150px]"}),e.jsx(f,{className:"h-8 w-[150px]"})]}),e.jsx(f,{className:"h-8 w-[70px]"})]}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(ne,{children:[e.jsx(ae,{children:e.jsxs(G,{children:[e.jsx(q,{className:"w-[50px]",children:e.jsx(f,{className:"h-4 w-6"})}),e.jsx(q,{children:e.jsx(f,{className:"h-4 w-20"})}),e.jsx(q,{children:e.jsx(f,{className:"h-4 w-20"})}),e.jsx(q,{children:e.jsx(f,{className:"h-4 w-24"})}),e.jsx(q,{children:e.jsx(f,{className:"h-4 w-20"})}),e.jsx(q,{children:e.jsx(f,{className:"h-4 w-28"})}),e.jsx(q,{children:e.jsx(f,{className:"h-4 w-16"})}),e.jsx(q,{children:e.jsx(f,{className:"h-4 w-20"})}),e.jsx(q,{className:"w-[70px]",children:e.jsx(f,{className:"h-4 w-12"})})]})}),e.jsx(ie,{children:Array.from({length:10}).map((t,a)=>e.jsxs(G,{children:[e.jsx(E,{children:e.jsx(f,{className:"h-4 w-6"})}),e.jsx(E,{children:e.jsx(f,{className:"h-4 w-20"})}),e.jsx(E,{children:e.jsx(f,{className:"h-4 w-20"})}),e.jsx(E,{children:e.jsx(f,{className:"h-4 w-24"})}),e.jsx(E,{children:e.jsx(f,{className:"h-4 w-20"})}),e.jsx(E,{children:e.jsx(f,{className:"h-4 w-32"})}),e.jsx(E,{children:e.jsx(f,{className:"h-4 w-16"})}),e.jsx(E,{children:e.jsx(f,{className:"h-6 w-16 rounded-full"})}),e.jsx(E,{children:e.jsx(f,{className:"h-8 w-8 rounded"})})]},a))})]})}),e.jsxs("div",{className:"flex items-center justify-between px-2",children:[e.jsx(f,{className:"h-4 w-32"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(f,{className:"h-8 w-8"}),e.jsx(f,{className:"h-8 w-8"}),e.jsx(f,{className:"h-8 w-8"}),e.jsx(f,{className:"h-8 w-8"})]})]})]})}function Ns(){const{setOpen:t}=J(),a=()=>{t("create")};return e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs(I,{onClick:a,size:"sm",className:"h-8",children:[e.jsx(Ye,{className:"mr-2 h-4 w-4"}),"Tạo lịch"]})})}const ws=(t={})=>{const{params:a={},enabled:n=!0}=t;return as({queryKey:["menu-schedule",a],queryFn:()=>Y.getMenuSchedules(a),enabled:n,staleTime:5*60*1e3,gcTime:10*60*1e3})},Cs=(t={})=>{var M;const{user:a,company:n}=ns(),c=(n==null?void 0:n.id)||(a==null?void 0:a.company_uid)||"595e8cb4-674c-49f7-adec-826b211a7ce3",i=(()=>{try{const x=localStorage.getItem("pos_selected_brand");if(x){const v=JSON.parse(x),w=v.id||v.brandId||"";if(w)return w}const y=localStorage.getItem("pos_brands_data");if(y){const v=JSON.parse(y);if(Array.isArray(v)&&v.length>0)return(v.find(T=>T.active===1)||v[0]).id||""}}catch{}return"d43a01ec-2f38-4430-a7ca-9b3324f7d39e"})(),d={...t,params:{company_uid:c,brand_uid:i,...t.params}},r=ws(d),{data:l=[]}=Me(),{data:o=[]}=le(),N=((M=r.data)==null?void 0:M.map((x,y)=>{var v,w;return{...x,id:y,city_uid:x.city_uid,store_uid:x.store_uid,city_name:((v=l.find(T=>T.id===x.city_uid))==null?void 0:v.city_name)||x.city_uid,store_name:((w=o.find(T=>T.id===x.store_uid))==null?void 0:w.name)||x.store_uid,time_formatted:new Date(x.time).toLocaleDateString("vi-VN"),end_time_formatted:x.end_time?new Date(x.end_time).toLocaleDateString("vi-VN"):""}}))||[];return{...r,data:N}},Ss=()=>{const t=te();return de({mutationFn:a=>Y.createMenuSchedule(a),onSuccess:()=>{t.invalidateQueries({queryKey:["menu-schedule"]}),t.invalidateQueries({queryKey:["menu-schedules"]}),t.invalidateQueries({queryKey:["item-schedule"]}),Q.success("Lịch trình menu đã được tạo thành công")},onError:a=>{Q.error(a.message||"Có lỗi xảy ra khi tạo lịch trình")}})},Ms=()=>{const t=te();return de({mutationFn:a=>Y.deleteMenuSchedule(a),onSuccess:()=>{t.invalidateQueries({queryKey:["menu-schedules"]}),t.invalidateQueries({queryKey:["menu-schedule"]}),Q.success("Lịch trình menu đã được xóa thành công")},onError:()=>{Q.error("Có lỗi xảy ra khi xóa menu schedule")}})},Ts=(t,a,n)=>{var d,r,l,o,N,M,x,y,v,w,T;const c=Date.now(),s=t.originalItem,i={id:(s==null?void 0:s.id)||t.id,sort:t.display_order||(s==null?void 0:s.sort)||1e3,is_fc:(s==null?void 0:s.is_fc)||0,point:(s==null?void 0:s.point)||0,active:(s==null?void 0:s.active)||1,is_kit:(s==null?void 0:s.is_kit)||0,is_sub:(s==null?void 0:s.is_sub)||0,ta_tax:(s==null?void 0:s.ta_tax)||0,deleted:(s==null?void 0:s.deleted)||!1,is_fabi:(s==null?void 0:s.is_fabi)||1,is_gift:(s==null?void 0:s.is_gift)||0,item_id:t.code,ots_tax:t.vat_rate||(s==null?void 0:s.ots_tax)||0,user_id:(s==null?void 0:s.user_id)||"",city_uid:a.city_uid,revision:(s==null?void 0:s.revision)||c,ta_price:t.ots_price||(s==null?void 0:s.ta_price)||0,unit_uid:t.unit_uid||(s==null?void 0:s.unit_uid)||"",brand_uid:a.brand_uid,is_parent:(s==null?void 0:s.is_parent)||0,item_name:t.name,ots_price:t.ots_price||(s==null?void 0:s.ots_price)||0,store_uid:a.store_uid,cost_price:(s==null?void 0:s.cost_price)||0,created_at:(s==null?void 0:s.created_at)||c,created_by:(s==null?void 0:s.created_by)||n,deleted_at:(s==null?void 0:s.deleted_at)||null,deleted_by:(s==null?void 0:s.deleted_by)||null,extra_data:{is_buffet_item:((d=s==null?void 0:s.extra_data)==null?void 0:d.is_buffet_item)||0,up_size_buffet:((r=s==null?void 0:s.extra_data)==null?void 0:r.up_size_buffet)||[],is_item_service:((l=s==null?void 0:s.extra_data)==null?void 0:l.is_item_service)||0,is_virtual_item:((o=s==null?void 0:s.extra_data)==null?void 0:o.is_virtual_item)||0,price_by_source:((N=s==null?void 0:s.extra_data)==null?void 0:N.price_by_source)||[],enable_edit_price:((M=s==null?void 0:s.extra_data)==null?void 0:M.enable_edit_price)||0,exclude_items_buffet:((x=s==null?void 0:s.extra_data)==null?void 0:x.exclude_items_buffet)||[],no_update_quantity_toping:((y=s==null?void 0:s.extra_data)==null?void 0:y.no_update_quantity_toping)||0},image_path:(s==null?void 0:s.image_path)||"",is_foreign:(s==null?void 0:s.is_foreign)||0,is_service:t.is_service?1:0,item_color:(s==null?void 0:s.item_color)||"",list_order:(s==null?void 0:s.list_order)||0,source_uid:(s==null?void 0:s.source_uid)||null,updated_at:(s==null?void 0:s.updated_at)||c,updated_by:n,company_uid:a.company_uid,description:(s==null?void 0:s.description)||"",expire_date:(s==null?void 0:s.expire_date)||0,is_eat_with:(s==null?void 0:s.is_eat_with)||0,is_material:(s==null?void 0:s.is_material)||0,show_on_web:(s==null?void 0:s.show_on_web)||0,price_change:(s==null?void 0:s.price_change)||0,time_cooking:t.cooking_time||(s==null?void 0:s.time_cooking)||0,item_type_uid:t.item_type_uid||(s==null?void 0:s.item_type_uid)||"",process_index:(s==null?void 0:s.process_index)||0,effective_date:(s==null?void 0:s.effective_date)||0,is_print_label:(s==null?void 0:s.is_print_label)||0,item_class_uid:t.item_class_uid||(s==null?void 0:s.item_class_uid)||null,quantity_limit:(s==null?void 0:s.quantity_limit)||0,allow_take_away:(s==null?void 0:s.allow_take_away)||1,item_id_barcode:(s==null?void 0:s.item_id_barcode)||"",item_id_mapping:(s==null?void 0:s.item_id_mapping)||"",apply_with_store:(s==null?void 0:s.apply_with_store)||1,currency_type_id:(s==null?void 0:s.currency_type_id)||"",image_path_thumb:(s==null?void 0:s.image_path_thumb)||null,item_id_eat_with:(s==null?void 0:s.item_id_eat_with)||"",quantity_default:(s==null?void 0:s.quantity_default)||0,quantity_per_day:(s==null?void 0:s.quantity_per_day)||0,customization_uid:t.customization_uid||(s==null?void 0:s.customization_uid)||null,is_allow_discount:(s==null?void 0:s.is_allow_discount)||0,show_price_on_web:(s==null?void 0:s.show_price_on_web)||0,time_sale_hour_day:t.time_sale_hour_day||(s==null?void 0:s.time_sale_hour_day)||0,unit_secondary_uid:t.unit_secondary_uid||(s==null?void 0:s.unit_secondary_uid)||null,time_sale_date_week:t.time_sale_date_week||(s==null?void 0:s.time_sale_date_week)||0};return{company_uid:a.company_uid,brand_uid:a.brand_uid,type:"item",id:a.schedule_id||t.id,item_id:t.code,item_old_uid:null,item_new_uid:null,action:t.action,partner:"",time:a.time,end_time:a.end_time||null,user_info:{},changed_data:i,data_old:s?{...s,image_path_thumb:s.image_path_thumb||"",extra_data:{...s.extra_data,up_size_buffet:((v=s.extra_data)==null?void 0:v.up_size_buffet)||[],price_by_source:((w=s.extra_data)==null?void 0:w.price_by_source)||[],exclude_items_buffet:((T=s.extra_data)==null?void 0:T.exclude_items_buffet)||[]}}:{},error:"",status:"PENDING",store_uid:a.store_uid,city_uid:a.city_uid,created_by:(s==null?void 0:s.created_by)||n,updated_by:n,deleted_by:(s==null?void 0:s.deleted_by)||null,created_at:(s==null?void 0:s.created_at)||c,updated_at:(s==null?void 0:s.updated_at)||c,deleted_at:(s==null?void 0:s.deleted_at)||null,deleted:(s==null?void 0:s.deleted)||!1,item_uid:(s==null?void 0:s.id)||t.id}},ks=m.object({item_name:m.string().optional(),ots_price:m.coerce.number().min(0,"Giá phải lớn hơn hoặc bằng 0").optional(),item_id:m.string().optional(),item_id_barcode:m.string().optional(),is_eat_with:m.boolean().optional(),no_update_quantity:m.boolean().optional(),item_class_uid:m.string().optional(),item_type_uid:m.string().optional(),description:m.string().optional(),store_uid:m.string().optional(),sku:m.string().optional(),unit_uid:m.string().optional(),unit_secondary_uid:m.string().optional(),vat_rate:m.coerce.number().min(0).max(100).optional(),cooking_time:m.coerce.number().min(0).optional(),allow_edit_price:m.boolean().optional(),require_quantity_input:m.boolean().optional(),allow_remove_without_permission:m.boolean().optional(),is_featured:m.boolean().optional(),is_buffet:m.boolean().optional(),inqr_formula:m.string().optional(),is_service:m.boolean().optional(),customization:m.string().optional(),selected_days:m.array(m.boolean()).optional(),selected_hours:m.record(m.boolean()).optional(),display_order:m.coerce.number().optional(),time_sale_date_week:m.coerce.number().optional(),time_sale_hour_day:m.coerce.number().optional()}),Ps=()=>{const t="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";let a="";for(let n=0;n<4;n++)a+=t.charAt(Math.floor(Math.random()*t.length));return`ITEM-${a}`},Es=t=>{const a=[!1,!1,!1,!1,!1,!1,!1],n=[6,0,1,3,2,4,5];for(let c=0;c<7;c++)if(t&1<<c){const s=n[c];s<7&&(a[s]=!0)}return a},Fs=t=>{let a=0;const n=[1,2,4,3,5,6,0];for(let c=0;c<7;c++)t[c]&&(a|=1<<n[c]);return a},qs=t=>{const a={};for(let n=0;n<24;n++)a[`${n}h`]=(t&1<<n)!==0;return a},Is=t=>{let a=0;for(let n=0;n<24;n++)t[`${n}h`]&&(a|=1<<n);return a},As=t=>({item_name:t.item_name||"",item_id:t.item_id||"",ots_price:t.ots_price||0,item_class_uid:t.item_class_uid||"",item_type_uid:t.item_type_uid||"",unit_uid:t.unit_uid||"",unit_secondary_uid:t.unit_secondary_uid||"",customization:t.customization_uid||"",vat_rate:t.ots_tax||0,cooking_time:t.time_cooking||0,is_service:t.is_service===1,display_order:t.sort||1e3,description:t.description||"",is_eat_with:t.is_eat_with===1,store_uid:t.store_uid||"",time_sale_date_week:t.time_sale_date_week||0,time_sale_hour_day:t.time_sale_hour_day||0,selected_days:t.time_sale_date_week?Es(t.time_sale_date_week):[!1,!1,!1,!1,!1,!1,!1],selected_hours:t.time_sale_hour_day?qs(t.time_sale_hour_day):{}}),Vs=({selectedItem:t,open:a})=>{const n=Ne({resolver:Se(ks),defaultValues:{}});return S.useEffect(()=>{if(t&&a){const s=t.originalItem||t,i=As({...s,item_name:t.name,item_id:t.code,is_service:typeof s.is_service=="boolean"?s.is_service?1:0:s.is_service});Object.entries(i).forEach(([d,r])=>{n.setValue(d,r)})}else a&&n.reset()},[t,a,n]),{form:n,createMenuItem:(s,i,d)=>({id:i==="UPDATE"?d:Date.now().toString(),code:s.item_id_barcode||s.item_id||Ps(),name:s.item_name||"Món mới",action:i,originalItem:i==="UPDATE"?t==null?void 0:t.originalItem:void 0,item_class_uid:s.item_class_uid,item_type_uid:s.item_type_uid,unit_uid:s.unit_uid,unit_secondary_uid:s.unit_secondary_uid,ots_price:s.ots_price,customization_uid:s.customization,vat_rate:s.vat_rate,cooking_time:s.cooking_time,is_service:s.is_service,display_order:s.display_order,time_sale_date_week:s.selected_days?Fs(s.selected_days):0,time_sale_hour_day:s.selected_hours?Is(s.selected_hours):0})}},zs=()=>{const t=te(),{user:a}=Ve();return de({mutationFn:async({menuItems:n,scheduleData:c})=>{const s=(a==null?void 0:a.email)||"<EMAIL>",i=n.map(d=>Ts(d,c,s));return Y.updateItemSchedule(i)},onSuccess:n=>(Q.success("Cập nhật menu schedule thành công!"),t.invalidateQueries({queryKey:["menu-schedules"]}),t.invalidateQueries({queryKey:["menu-schedule-by-params"]}),n),onError:()=>{Q.error("Có lỗi xảy ra khi cập nhật menu schedule")}})},Ls=t=>{switch(t){case"CREATE":return"Tạo món";case"UPDATE":return"Sửa món";case"DELETE":return"Xóa món";default:return t}};function Os({selectedAction:t,onActionChange:a,selectedItem:n}){return e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(we,{className:"w-40 rounded-sm bg-gray-100 px-3 py-2 text-sm font-medium whitespace-nowrap text-gray-700",children:"Thao tác"}),n?e.jsx("div",{className:"flex-1 rounded-md border bg-gray-50 px-3 py-2 text-sm text-gray-700",children:Ls(n.action)}):e.jsxs(V,{value:t,onValueChange:a,children:[e.jsx(z,{className:"w-full",children:e.jsx(L,{placeholder:"Chọn thao tác"})}),e.jsxs(O,{children:[e.jsx(F,{value:"CREATE",children:"Tạo món"}),e.jsx(F,{value:"UPDATE",children:"Sửa món"}),e.jsx(F,{value:"DELETE",children:"Xóa món"})]})]})]})}function Ks({form:t,cityUid:a,storeUid:n}){const{data:c=[],isLoading:s}=De({skip_limit:!0,...n&&{store_uid:n},...a&&!n&&{list_city_uid:[a]},enabled:!!a});return e.jsxs("div",{className:"space-y-4",children:[e.jsx(_,{control:t.control,name:"vat_rate",render:({field:i})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(h,{className:"w-40 text-sm font-medium text-gray-700",children:"Vat món ăn"}),e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx(g,{children:e.jsx($,{type:"number",placeholder:"0",...i,className:"flex-1"})}),e.jsx("span",{className:"text-sm text-gray-500",children:"%"})]})]}),e.jsx(j,{})]})}),e.jsx(_,{control:t.control,name:"cooking_time",render:({field:i})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(h,{className:"w-40 text-sm font-medium text-gray-700",children:"Thời gian chế biến (phút)"}),e.jsx(g,{children:e.jsx($,{type:"number",placeholder:"0",...i,className:"flex-1"})})]}),e.jsx(j,{})]})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(h,{className:"w-40 text-sm font-medium text-gray-700",children:"Cấu hình sửa giá, nhập số lượng"}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx(_,{control:t.control,name:"allow_edit_price",render:({field:i})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(g,{children:e.jsx(U,{checked:i.value,onCheckedChange:i.onChange})}),e.jsx(h,{className:"text-sm text-gray-700",children:"Cho phép sửa giá khi bán"})]}),e.jsx(j,{})]})}),e.jsx(_,{control:t.control,name:"require_quantity_input",render:({field:i})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(g,{children:e.jsx(U,{checked:i.value,onCheckedChange:i.onChange})}),e.jsx(h,{className:"text-sm text-gray-700",children:"Yêu cầu nhập số lượng khi gọi đồ"})]}),e.jsx(j,{})]})}),e.jsx(_,{control:t.control,name:"allow_remove_without_permission",render:({field:i})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(g,{children:e.jsx(U,{checked:i.value,onCheckedChange:i.onChange})}),e.jsx(h,{className:"text-sm text-gray-700",children:"Cho phép bỏ món mà không cần quyền áp dụng"})]}),e.jsx(j,{})]})})]})]}),e.jsx(_,{control:t.control,name:"is_featured",render:({field:i})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(h,{className:"w-40 text-sm font-medium text-gray-700",children:"Cấu hình món ăn"}),e.jsx(g,{children:e.jsx(U,{checked:i.value,onCheckedChange:i.onChange})})]}),e.jsx(j,{})]})}),e.jsx(_,{control:t.control,name:"is_buffet",render:({field:i})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(h,{className:"w-40 text-sm font-medium text-gray-700",children:"Cấu hình món ăn là vé buffet"}),e.jsx(g,{children:e.jsx(U,{checked:i.value,onCheckedChange:i.onChange})})]}),e.jsx(j,{})]})}),e.jsx(_,{control:t.control,name:"inqr_formula",render:({field:i})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx(h,{className:"mt-2 w-40 text-sm font-medium text-gray-700",children:"Công thức inQr cho máy pha trà"}),e.jsx(g,{children:e.jsx(Te,{placeholder:"Nhập công thức inQR",...i,className:"min-h-[80px] flex-1"})})]}),e.jsx(j,{})]})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(h,{className:"w-40 text-sm font-medium text-gray-700",children:"Cấu hình món dịch vụ"}),e.jsx(_,{control:t.control,name:"is_service",render:({field:i})=>e.jsxs(p,{children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(g,{children:e.jsx(U,{checked:i.value,onCheckedChange:i.onChange})})}),e.jsx(j,{})]})})]}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(h,{className:"text-sm font-medium text-gray-700",children:"Cấu hình giá theo nguồn"}),e.jsx(I,{type:"button",variant:"outline",size:"sm",className:"bg-blue-600 text-white hover:bg-blue-700",children:"Thêm nguồn"})]})}),e.jsx(_,{control:t.control,name:"customization",render:({field:i})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(h,{className:"w-40 text-sm font-medium text-gray-700",children:"Customization"}),e.jsx(g,{children:e.jsxs(V,{onValueChange:i.onChange,value:i.value||"",disabled:s,children:[e.jsx(z,{className:"flex-1",children:e.jsx(L,{placeholder:"Tìm kiếm"})}),e.jsx(O,{children:c==null?void 0:c.map(d=>e.jsx(F,{value:d.id,children:d.name},d.id))})]})})]}),e.jsx(j,{})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(h,{className:"text-sm font-medium text-gray-700",children:"Khung thời gian bán"}),e.jsx(_,{control:t.control,name:"selected_days",render:({field:i})=>e.jsxs(p,{children:[e.jsx(h,{className:"text-sm text-gray-600",children:"Chọn ngày"}),e.jsx("div",{className:"grid grid-cols-7 gap-2",children:["Thứ 2","Thứ 3","Thứ 4","Thứ 5","Thứ 6","Thứ 7","Chủ nhật"].map((d,r)=>{const l=Array.isArray(i.value)?i.value[r]:!1;return e.jsx(I,{type:"button",variant:l?"default":"outline",size:"sm",className:`text-xs ${l?"bg-blue-600 text-white":"border-blue-600 text-blue-600"}`,onClick:()=>{const o=Array.isArray(i.value)?[...i.value]:[!1,!1,!1,!1,!1,!1,!1];o[r]=!o[r],i.onChange(o)},children:d},d)})}),e.jsx(j,{})]})}),e.jsx(_,{control:t.control,name:"selected_hours",render:({field:i})=>e.jsxs(p,{children:[e.jsx(h,{className:"text-sm text-gray-600",children:"Chọn giờ"}),e.jsx("div",{className:"grid grid-cols-10 gap-2",children:Array.from({length:24},(d,r)=>`${r}h`).map(d=>{const r=i.value&&i.value[d];return e.jsx(I,{type:"button",variant:r?"default":"outline",size:"sm",className:`text-xs ${r?"bg-blue-600 text-white":"border-blue-600 text-blue-600"}`,onClick:()=>{const l={...i.value};l[d]=!l[d],i.onChange(l)},children:d},d)})}),e.jsx(j,{})]})})]}),e.jsx(_,{control:t.control,name:"display_order",render:({field:i})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(h,{className:"w-40 text-sm font-medium text-gray-700",children:"Thứ tự hiển thị"}),e.jsx(g,{children:e.jsx($,{type:"number",placeholder:"Nhập số thứ tự hiển thị",...i,className:"flex-1"})})]}),e.jsx(j,{})]})})]})}function Hs({form:t}){return e.jsxs("div",{className:"space-y-4",children:[e.jsx(_,{control:t.control,name:"item_name",render:({field:a})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(h,{className:"w-40 text-sm font-medium text-gray-700",children:["Tên ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(g,{children:e.jsx($,{placeholder:"Nhập tên món",...a,className:"flex-1"})})]}),e.jsx(j,{})]})}),e.jsx(_,{control:t.control,name:"ots_price",render:({field:a})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(h,{className:"w-40 text-sm font-medium text-gray-700",children:["Giá ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(g,{children:e.jsx($,{type:"number",placeholder:"0",...a,className:"flex-1"})})]}),e.jsx(j,{})]})}),e.jsx(_,{control:t.control,name:"item_id",render:({field:a})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx(h,{className:"mt-2 w-40 text-sm font-medium text-gray-700",children:"Mã món"}),e.jsx("div",{className:"flex-1",children:e.jsx(g,{children:e.jsx($,{placeholder:"Nếu để trống, hệ thống sẽ tự động tạo mới mã món",...a,className:"flex-1"})})})]}),e.jsx(j,{})]})}),e.jsx(_,{control:t.control,name:"item_id_barcode",render:({field:a})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx(h,{className:"mt-2 w-40 text-sm font-medium text-gray-700",children:"Mã barcode"}),e.jsx("div",{className:"flex-1",children:e.jsx(g,{children:e.jsx($,{placeholder:"Nếu bạn sử dụng tính năng scan QR tại POS hãy tạo mã barcode cho món",...a,className:"flex-1"})})})]}),e.jsx(j,{})]})}),e.jsx(_,{control:t.control,name:"description",render:({field:a})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx(h,{className:"mt-2 w-40 text-sm font-medium text-gray-700",children:"Mô tả"}),e.jsx(g,{children:e.jsx(Te,{placeholder:"Mô tả",...a,className:"min-h-[80px] flex-1"})})]}),e.jsx(j,{})]})})]})}function $s({form:t,cityUid:a,storeUid:n}){const{data:c=[],isLoading:s}=ss({skip_limit:!0,page:1,results_per_page:15e3,enabled:!!a}),{data:i=[]}=es({skip_limit:!0,...a&&{city_uid:a},enabled:!!a}),{data:d=[]}=le({params:{id:n},enabled:!!n&&!!a}),{data:r=[]}=ts();return e.jsxs("div",{className:"space-y-4",children:[e.jsx(_,{control:t.control,name:"is_eat_with",render:({field:l})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(h,{className:"w-40 text-sm font-medium text-gray-700",children:"Món ăn kèm"}),e.jsx(g,{children:e.jsx(U,{checked:l.value,onCheckedChange:l.onChange})})]}),e.jsx(j,{})]})}),e.jsx(_,{control:t.control,name:"no_update_quantity",render:({field:l})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(h,{className:"w-40 text-sm font-medium text-gray-700",children:"Không cập nhật số lượng món ăn kèm"}),e.jsx(g,{children:e.jsx(U,{checked:l.value,onCheckedChange:l.onChange})})]}),e.jsx(j,{})]})}),e.jsx(_,{control:t.control,name:"item_class_uid",render:({field:l})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(h,{className:"w-40 text-sm font-medium text-gray-700",children:"Nhóm"}),e.jsx(g,{children:e.jsxs(V,{onValueChange:l.onChange,value:l.value||"",disabled:s,children:[e.jsx(z,{className:"flex-1",children:e.jsx(L,{placeholder:"Chọn nhóm món"})}),e.jsx(O,{children:c==null?void 0:c.map(o=>e.jsx(F,{value:o.id,children:o.item_class_name},o.id))})]})})]}),e.jsx(j,{})]})}),e.jsx(_,{control:t.control,name:"item_type_uid",render:({field:l})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(h,{className:"w-40 text-sm font-medium text-gray-700",children:"Loại món"}),e.jsx(g,{children:e.jsxs(V,{onValueChange:l.onChange,value:l.value||"",children:[e.jsx(z,{className:"flex-1",children:e.jsx(L,{placeholder:"Chọn loại món"})}),e.jsx(O,{children:i==null?void 0:i.map(o=>e.jsx(F,{value:o.id,children:o.item_type_name},o.id))})]})})]}),e.jsx(j,{})]})}),e.jsx(_,{control:t.control,name:"store_uid",render:({field:l})=>{const o=Array.isArray(d)?d.find(N=>N.id===n):null;return e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(h,{className:"w-40 text-sm font-medium text-gray-700",children:["Cửa hàng áp dụng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(g,{children:e.jsxs(V,{onValueChange:l.onChange,value:l.value||"",disabled:!0,children:[e.jsx(z,{className:"flex-1",children:e.jsx(L,{placeholder:(o==null?void 0:o.name)||"Chưa chọn cửa hàng"})}),e.jsx(O,{children:o&&e.jsx(F,{value:o.id,children:o.name},o.id)})]})})]}),e.jsx(j,{})]})}}),e.jsx(_,{control:t.control,name:"sku",render:({field:l})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(h,{className:"w-40 text-sm font-medium text-gray-700",children:"SKU"}),e.jsx(g,{children:e.jsx($,{placeholder:"Nhập SKU",...l,className:"flex-1"})})]}),e.jsx(j,{})]})}),e.jsx(_,{control:t.control,name:"unit_uid",render:({field:l})=>e.jsxs(p,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(h,{className:"w-40 text-sm font-medium text-gray-700",children:["Đơn vị tính ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(g,{children:e.jsxs(V,{onValueChange:l.onChange,value:l.value||"",children:[e.jsx(z,{className:"flex-1",children:e.jsx(L,{placeholder:"Chọn đơn vị tính"})}),e.jsx(O,{children:r==null?void 0:r.map(o=>e.jsx(F,{value:o.id,children:o.unit_name},o.id))})]})})]}),e.jsx(j,{})]})})]})}function Qs({selectedItemId:t,onItemSelect:a,selectedItem:n,items:c}){return e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(we,{className:"w-34 rounded-sm bg-gray-100 px-3 py-2 text-sm font-medium whitespace-nowrap text-gray-700",children:["Chọn món ",e.jsx("span",{className:"text-red-500",children:"*"})]}),n?e.jsxs("div",{className:"flex-1 rounded-md border bg-gray-50 px-3 py-2 text-sm text-gray-700",children:[n.name," (",n.code,")"]}):e.jsxs(V,{value:t,onValueChange:a,disabled:!c.length,children:[e.jsx(z,{className:"flex-1",children:e.jsx(L,{placeholder:c.length?"Chọn món để cập nhật":"Không có món nào"})}),e.jsx(O,{children:c.map(s=>e.jsxs(F,{value:s.id,children:[s.item_name," (",s.item_id,")"]},s.id))})]})]})}function Bs({open:t,onOpenChange:a,onConfirm:n,cityUid:c,storeUid:s,selectedItem:i}){const[d,r]=S.useState(""),[l,o]=S.useState(""),{company:N,brands:M}=be(b=>b.auth),x=M==null?void 0:M[0],{form:y,createMenuItem:v}=Vs({selectedItem:i,open:t}),{data:w=[]}=Re({params:{company_uid:N==null?void 0:N.id,brand_uid:x==null?void 0:x.id,city_uid:c,store_uid:s},enabled:!!(N!=null&&N.id&&(x!=null&&x.id)&&(c||s))});se.useEffect(()=>{i?(r(i.action),o(i.id)):t&&(r(""),o(""))},[i,t]);const T=b=>{if(!d||d==="UPDATE"&&!l)return;const P=v(b,d,l);n([P]),y.reset(),r(""),o(""),a(!1)},A=se.useCallback(b=>{o(b);const P=w.find(X=>X.id===b);if(P){const X={item_name:P.item_name,item_id:P.item_id,ots_price:P.ots_price||0,item_class_uid:P.item_class_uid||"",item_type_uid:P.item_type_uid||"",unit_uid:P.unit_uid||"",unit_secondary_uid:P.unit_secondary_uid||""};Object.entries(X).forEach(([R,D])=>{y.setValue(R,D)})}},[w,y]),W=se.useMemo(()=>{const b=(i==null?void 0:i.action)||d;return b==="CREATE"||b==="UPDATE"},[i==null?void 0:i.action,d]);if(!t)return null;const k=()=>{a(!1),y.reset(),r(""),o("")},C=d!=="";return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(I,{variant:"ghost",size:"sm",onClick:k,className:"flex items-center",children:e.jsx(ce,{className:"h-4 w-4"})}),e.jsx(I,{type:"button",disabled:!C,className:"min-w-[100px]",onClick:()=>y.handleSubmit(T)(),children:"Xác nhận"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:"Quản lý món ăn"}),e.jsx("p",{className:"text-muted-foreground",children:"Thêm, sửa hoặc xóa món ăn trong menu schedule"})]})]}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx(Ce,{...y,children:e.jsxs("form",{onSubmit:y.handleSubmit(T),className:"space-y-6",children:[e.jsx("div",{className:"rounded-lg border bg-white p-6 shadow-sm",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Thông tin cơ bản"})}),e.jsx(Os,{selectedAction:d,onActionChange:r,selectedItem:i}),d==="UPDATE"||d==="DELETE"&&!i&&e.jsx(Qs,{selectedItemId:l,onItemSelect:A,selectedItem:i,items:w})]})}),W&&e.jsx("div",{className:"rounded-lg border bg-white p-6 shadow-sm",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Chi tiết món ăn"})}),e.jsx(Hs,{form:y}),e.jsx($s,{form:y,cityUid:c,storeUid:s}),e.jsx(Ks,{form:y,cityUid:c,storeUid:s})]})})]})})})]})}function Us({menuItems:t,onRowClick:a,onRemoveItem:n}){return e.jsx("div",{className:"rounded-md border",children:e.jsxs(ne,{children:[e.jsx(ae,{children:e.jsxs(G,{children:[e.jsx(q,{children:"Mã món"}),e.jsx(q,{children:"Tên món"}),e.jsx(q,{children:"Thao tác"}),e.jsx(q,{className:"w-[50px]"})]})}),e.jsx(ie,{children:t.length===0?e.jsx(G,{children:e.jsx(E,{colSpan:4,className:"text-center text-muted-foreground",children:"Chưa có món nào được thêm"})}):t.map(c=>e.jsxs(G,{className:"cursor-pointer hover:bg-gray-50",onClick:()=>a(c),children:[e.jsx(E,{className:"font-medium",children:c.code}),e.jsx(E,{children:c.name}),e.jsx(E,{children:e.jsx("span",{className:`inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset ${c.action==="CREATE"?"bg-green-50 text-green-700 ring-green-600/20":c.action==="UPDATE"?"bg-blue-50 text-blue-700 ring-blue-600/20":"bg-red-50 text-red-700 ring-red-600/20"}`,children:c.action})}),e.jsx(E,{children:e.jsx(I,{type:"button",variant:"ghost",size:"sm",onClick:s=>{s.stopPropagation(),n(c.id)},children:e.jsx(ve,{className:"h-4 w-4"})})})]},c.id))})]})})}function Gs({form:t,citiesData:a,storesData:n,isUpdateMode:c}){return e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(_,{control:t.control,name:"start_date",render:({field:s})=>{var i;return e.jsxs(p,{className:"flex flex-col",children:[e.jsx(h,{children:"Ngày bắt đầu"}),e.jsxs(xe,{children:[e.jsx(_e,{asChild:!0,children:e.jsx(g,{children:e.jsxs(I,{variant:"outline",className:ue("w-full pl-3 text-left font-normal",!s.value&&"text-muted-foreground"),children:[s.value?ye(s.value,"dd/MM/yyyy",{locale:fe}):e.jsx("span",{children:"Chọn ngày"}),e.jsx(je,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),e.jsx(pe,{className:"w-auto p-0",align:"start",children:e.jsx(he,{mode:"single",selected:s.value,onSelect:s.onChange,disabled:d=>d<new Date(new Date().setHours(0,0,0,0)),defaultMonth:s.value})})]},`start-date-${(i=s.value)==null?void 0:i.getTime()}`),e.jsx(j,{})]})}}),e.jsx(_,{control:t.control,name:"end_date",render:({field:s})=>{var i;return e.jsxs(p,{className:"flex flex-col",children:[e.jsx(h,{children:"Ngày kết thúc (tùy chọn)"}),e.jsxs(xe,{children:[e.jsx(_e,{asChild:!0,children:e.jsx(g,{children:e.jsxs(I,{variant:"outline",className:ue("w-full pl-3 text-left font-normal",!s.value&&"text-muted-foreground"),children:[s.value?ye(s.value,"dd/MM/yyyy",{locale:fe}):e.jsx("span",{children:"Chọn ngày"}),e.jsx(je,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),e.jsx(pe,{className:"w-auto p-0",align:"start",children:e.jsx(he,{mode:"single",selected:s.value,onSelect:s.onChange,disabled:d=>d<new Date(new Date().setHours(0,0,0,0)),defaultMonth:s.value})})]},`end-date-${(i=s.value)==null?void 0:i.getTime()}`),e.jsx(j,{})]})}}),e.jsx(_,{control:t.control,name:"city_uid",render:({field:s})=>e.jsxs(p,{children:[e.jsx(h,{children:"Thành phố"}),e.jsxs(V,{onValueChange:s.onChange,value:s.value,defaultValue:s.value,disabled:c,children:[e.jsx(g,{children:e.jsx(z,{className:"w-full",children:e.jsx(L,{placeholder:"Chọn thành phố"})})}),e.jsx(O,{children:a.map(i=>e.jsx(F,{value:i.id,children:i.name},i.id))})]},`city-${s.value}`),e.jsx(j,{})]})}),e.jsx(_,{control:t.control,name:"store_uid",render:({field:s})=>e.jsxs(p,{children:[e.jsx(h,{children:"Cửa hàng"}),e.jsxs(V,{onValueChange:s.onChange,value:s.value,defaultValue:s.value,disabled:c,children:[e.jsx(g,{children:e.jsx(z,{className:"w-full",children:e.jsx(L,{placeholder:"Chọn cửa hàng"})})}),e.jsx(O,{children:n.map(i=>e.jsx(F,{value:i.id,children:i.name},i.id))})]},`store-${s.value}`),e.jsx(j,{})]})})]})}const Xs=m.object({start_date:m.date({required_error:"Ngày bắt đầu là bắt buộc"}),end_date:m.date().optional(),city_uid:m.string().min(1,"Thành phố là bắt buộc"),store_uid:m.string().optional()});function ge({open:t,onOpenChange:a,currentRow:n}){const[c,s]=S.useState([]),[i,d]=S.useState(""),[r,l]=S.useState(!1),[o,N]=S.useState(null),{company:M,brands:x}=be(u=>u.auth),y=x==null?void 0:x[0],v=Ss(),w=zs(),T=!!(n!=null&&n.menuItems),{data:A=[]}=Me(),{data:W=[]}=le({enabled:t}),k=Ne({resolver:Se(Xs),defaultValues:{start_date:new Date(new Date().setDate(new Date().getDate()+1)),end_date:void 0,city_uid:"",store_uid:""}});S.useEffect(()=>{if(t&&n){s(n.menuItems||[]);const u={start_date:n.time?new Date(n.time*1e3):new Date,end_date:n.end_time?new Date(n.end_time*1e3):void 0,city_uid:n.city_uid||"",store_uid:n.store_uid||""};k.reset(u)}else t&&!n&&(s([]),k.reset({start_date:new Date(new Date().setDate(new Date().getDate()+1)),end_date:void 0,city_uid:"",store_uid:""}))},[n,t,k]);const C=k.watch("city_uid"),b=k.watch("store_uid"),P=C?W.filter(u=>{const H=A.find(B=>B.id===C);return H&&u.cityId===H.city_id}):[],X=u=>{u==null||u.preventDefault(),u==null||u.stopPropagation(),l(!0)},R=u=>{if(o){const H=c.map(B=>B.id===o.id?u[0]:B);s(H)}else s([...c,...u]);l(!1),N(null)},D=u=>{N(u),l(!0)},Ee=u=>{s(c.filter(H=>H.id!==u))},re=async u=>{var H;if(c.length===0){Q.error("Vui lòng thêm ít nhất một món");return}if(T){const B=()=>{var oe;return(oe=[...c,...(n==null?void 0:n.menuItems)||[]].find(Ae=>Ae.scheduleId))==null?void 0:oe.scheduleId},Ie={company_uid:(n==null?void 0:n.company_uid)||(M==null?void 0:M.id)||"",brand_uid:(n==null?void 0:n.brand_uid)||(y==null?void 0:y.id)||"",store_uid:(n==null?void 0:n.store_uid)||u.store_uid||"",city_uid:(n==null?void 0:n.city_uid)||u.city_uid,time:(n==null?void 0:n.time)||u.start_date.getTime(),end_time:(n==null?void 0:n.end_time)||((H=u.end_date)==null?void 0:H.getTime())||null,status:(n==null?void 0:n.status)||"PENDING",schedule_id:B()};w.mutate({menuItems:c,scheduleData:Ie},{onSuccess:()=>{a(!1)}})}else{const B=Pe({company_uid:(M==null?void 0:M.id)||"",brand_uid:(y==null?void 0:y.id)||"",city_uid:u.city_uid,store_uid:u.store_uid||"",start_date:u.start_date,end_date:u.end_date},c);v.mutate(B,{onSuccess:()=>{a(!1)}})}};if(!t)return null;const Fe=()=>{a(!1),k.reset(),s([]),d(""),N(null)},qe=k.watch("start_date")&&k.watch("city_uid"),ee=(()=>{if(!n)return null;switch(n.status||"PENDING"){case"PENDING":return{text:"Lịch sắp diễn ra",className:"bg-blue-500 text-white"};case"PROCESS":return{text:"Lịch đang diễn ra",className:"bg-orange-500 text-white"};case"DONE":return{text:"Lịch đã hoàn thành",className:"bg-green-500 text-white"};default:return{text:"Lịch sắp diễn ra",className:"bg-blue-500 text-white"}}})();return e.jsxs(e.Fragment,{children:[!r&&e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(I,{variant:"ghost",size:"sm",onClick:Fe,className:"flex items-center",children:e.jsx(ce,{className:"h-4 w-4"})}),e.jsx(I,{type:"button",disabled:v.isPending||w.isPending||!qe,className:"min-w-[100px]",onClick:()=>k.handleSubmit(re)(),children:v.isPending||w.isPending?"Đang lưu...":"Lưu"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:T?"Cập nhật lịch thay đổi":"Tạo lịch thay đổi"}),ee&&e.jsx("div",{className:"mt-4 w-full",children:e.jsx("div",{className:`flex w-full items-center justify-center rounded-lg px-4 py-3 text-sm font-medium ${ee.className}`,children:ee.text})})]})]}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"space-y-6",children:e.jsx(Ce,{...k,children:e.jsxs("form",{onSubmit:k.handleSubmit(re),className:"space-y-6",children:[e.jsx("div",{className:"rounded-lg border bg-white p-6 shadow-sm",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Thông tin cơ bản"})}),e.jsx(Gs,{form:k,citiesData:A.map(u=>({id:u.id,name:u.city_name})),storesData:P.map(u=>({id:u.id,name:u.name})),isUpdateMode:T}),e.jsx("div",{children:e.jsx("p",{className:"text-muted-foreground text-sm",children:"Thức đơn thay đổi theo cửa hàng"})})]})}),e.jsx("div",{className:"rounded-lg border bg-white p-6 shadow-sm",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Món lên lịch thay đổi"})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("div",{className:"flex-1",children:e.jsx($,{placeholder:C?"Nhập tên món hoặc thao tác":"Vui lòng chọn thành phố trước",value:i,onChange:u=>d(u.target.value),onKeyDown:u=>u.key==="Enter"&&C&&X(),disabled:!C})}),e.jsx(I,{type:"button",onClick:X,size:"sm",disabled:!C,children:"Thêm món"})]}),e.jsx(Us,{menuItems:c,onRowClick:D,onRemoveItem:Ee})]})})]})})})})]}),r&&e.jsx(Bs,{open:r,onOpenChange:l,onConfirm:R,cityUid:C,storeUid:b,selectedItem:o})]})}function Ws(){const{open:t,setOpen:a,currentRow:n,setCurrentRow:c}=J(),s=Ms();return e.jsxs(e.Fragment,{children:[e.jsx(ge,{open:t==="create",onOpenChange:i=>{i||a(null)}},"menu-schedule-day-create"),n&&e.jsxs(e.Fragment,{children:[e.jsx(ge,{open:t==="update",onOpenChange:i=>{i||(a(null),setTimeout(()=>{c(null)},500))},currentRow:n},`menu-schedule-day-update-${n}`),e.jsx(Ze,{destructive:!0,open:t==="delete",onOpenChange:()=>{a("delete"),setTimeout(()=>{c(null)},500)},handleConfirm:async()=>{if(n)try{await s.mutateAsync({time:n.time,store_uid:n.store_uid,city_uid:n.city_uid,brand_uid:n.brand_uid,company_uid:n.company_uid}),a(null),setTimeout(()=>{c(null)},500)}catch{}},className:"max-w-md",title:"Bạn có muốn xoá cấu hình này?",desc:e.jsx(e.Fragment,{children:"Hành động không thể hoàn tác."}),confirmText:"Xoá"},"quantity-day-delete")]})]})}function Js(){const[t,a]=S.useState("all"),{open:n}=J(),c=!n||n!=="create"&&n!=="update",s=S.useMemo(()=>{const l={};return t==="PENDING"?l.status="PENDING":t==="PROCESS"?l.status="PROCESS":t==="DONE"&&(l.status="DONE"),l},[t]),{data:i=[],isLoading:d,error:r}=Cs({params:s});return r?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-muted-foreground mb-2 text-sm",children:"Có lỗi xảy ra khi tải dữ liệu"}),e.jsx("p",{className:"text-muted-foreground text-xs",children:(r==null?void 0:r.message)||"Lỗi không xác định"}),e.jsx("button",{onClick:()=>window.location.reload(),className:"mt-4 rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600",children:"Tải lại trang"})]})}):e.jsxs(e.Fragment,{children:[c&&e.jsxs(e.Fragment,{children:[e.jsx(ze,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Ke,{}),e.jsx(He,{}),e.jsx(Oe,{})]})}),e.jsxs(Le,{children:[e.jsxs("div",{className:"mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4",children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Danh sách lịch"}),e.jsx(Ns,{})]}),e.jsxs("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12",children:[d&&e.jsx(vs,{}),!d&&e.jsx(bs,{columns:ds,data:i,selectedStatus:t,onStatusChange:a})]})]})]}),e.jsx(Ws,{})]})}function Ys(){return e.jsx(is,{children:e.jsx(Js,{})})}const ln=Ys;export{ln as component};
