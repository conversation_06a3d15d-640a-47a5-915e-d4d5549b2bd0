import{h as C,v as f,R as h,j as t}from"./index-Bnt3OGV2.js";import{g as I,a as j,b,c as D,d as g,e as o,f as S}from"./command-ByfqjQDn.js";import{c as e}from"./createReactComponent-BD5R5KSl.js";import"./pos-api-BwpRFGce.js";import{S as N}from"./scroll-area-BeVbW7LP.js";import{I as z}from"./IconChevronRight-BM-o6vT_.js";/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var d=e("outline","arrow-right-dashed","IconArrowRightDashed",[["path",{d:"M5 12h.5m3 0h1.5m3 0h6",key:"svg-0"}],["path",{d:"M13 18l6 -6",key:"svg-1"}],["path",{d:"M13 6l6 6",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var B=e("outline","chart-bar","IconChartBar",[["path",{d:"M3 13a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z",key:"svg-0"}],["path",{d:"M15 9a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v10a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z",key:"svg-1"}],["path",{d:"M9 5a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z",key:"svg-2"}],["path",{d:"M4 20h14",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var L=e("outline","checklist","IconChecklist",[["path",{d:"M9.615 20h-2.615a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8",key:"svg-0"}],["path",{d:"M14 19l2 2l4 -4",key:"svg-1"}],["path",{d:"M9 8h4",key:"svg-2"}],["path",{d:"M9 12h2",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var w=e("outline","credit-card","IconCreditCard",[["path",{d:"M3 5m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z",key:"svg-0"}],["path",{d:"M3 10l18 0",key:"svg-1"}],["path",{d:"M7 15l.01 0",key:"svg-2"}],["path",{d:"M11 15l2 0",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var T=e("outline","device-laptop","IconDeviceLaptop",[["path",{d:"M3 19l18 0",key:"svg-0"}],["path",{d:"M5 6m0 1a1 1 0 0 1 1 -1h12a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-12a1 1 0 0 1 -1 -1z",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var G=e("outline","devices","IconDevices",[["path",{d:"M13 9a1 1 0 0 1 1 -1h6a1 1 0 0 1 1 1v10a1 1 0 0 1 -1 1h-6a1 1 0 0 1 -1 -1v-10z",key:"svg-0"}],["path",{d:"M18 8v-3a1 1 0 0 0 -1 -1h-13a1 1 0 0 0 -1 1v12a1 1 0 0 0 1 1h9",key:"svg-1"}],["path",{d:"M16 9h2",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var v=e("outline","gift","IconGift",[["path",{d:"M3 8m0 1a1 1 0 0 1 1 -1h16a1 1 0 0 1 1 1v2a1 1 0 0 1 -1 1h-16a1 1 0 0 1 -1 -1z",key:"svg-0"}],["path",{d:"M12 8l0 13",key:"svg-1"}],["path",{d:"M19 12v7a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2v-7",key:"svg-2"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0 -5a4.8 8 0 0 1 4.5 5a4.8 8 0 0 1 4.5 -5a2.5 2.5 0 0 1 0 5",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var R=e("outline","layout-dashboard","IconLayoutDashboard",[["path",{d:"M5 4h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1v-6a1 1 0 0 1 1 -1",key:"svg-0"}],["path",{d:"M5 16h4a1 1 0 0 1 1 1v2a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1v-2a1 1 0 0 1 1 -1",key:"svg-1"}],["path",{d:"M15 12h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1v-6a1 1 0 0 1 1 -1",key:"svg-2"}],["path",{d:"M15 4h4a1 1 0 0 1 1 1v2a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1v-2a1 1 0 0 1 1 -1",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var y=e("outline","messages","IconMessages",[["path",{d:"M21 14l-3 -3h-7a1 1 0 0 1 -1 -1v-6a1 1 0 0 1 1 -1h9a1 1 0 0 1 1 1v10",key:"svg-0"}],["path",{d:"M14 15v2a1 1 0 0 1 -1 1h-7l-3 3v-10a1 1 0 0 1 1 -1h2",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var K=e("outline","moon","IconMoon",[["path",{d:"M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z",key:"svg-0"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var P=e("outline","packages","IconPackages",[["path",{d:"M7 16.5l-5 -3l5 -3l5 3v5.5l-5 3z",key:"svg-0"}],["path",{d:"M2 13.5v5.5l5 3",key:"svg-1"}],["path",{d:"M7 16.545l5 -3.03",key:"svg-2"}],["path",{d:"M17 16.5l-5 -3l5 -3l5 3v5.5l-5 3z",key:"svg-3"}],["path",{d:"M12 19l5 3",key:"svg-4"}],["path",{d:"M17 16.5l5 -3",key:"svg-5"}],["path",{d:"M12 13.5v-5.5l-5 -3l5 -3l5 3v5.5",key:"svg-6"}],["path",{d:"M7 5.03v5.455",key:"svg-7"}],["path",{d:"M12 8l5 -3",key:"svg-8"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var $=e("outline","receipt-dollar","IconReceiptDollar",[["path",{d:"M5 21v-16a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v16l-3 -2l-2 2l-2 -2l-2 2l-2 -2l-3 2",key:"svg-0"}],["path",{d:"M14.8 8a2 2 0 0 0 -1.8 -1h-2a2 2 0 1 0 0 4h2a2 2 0 1 1 0 4h-2a2 2 0 0 1 -1.8 -1",key:"svg-1"}],["path",{d:"M12 6v10",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var A=e("outline","settings","IconSettings",[["path",{d:"M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z",key:"svg-0"}],["path",{d:"M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var E=e("outline","sun","IconSun",[["path",{d:"M12 12m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0",key:"svg-0"}],["path",{d:"M3 12h1m8 -9v1m8 8h1m-9 8v1m-6.4 -15.4l.7 .7m12.1 -.7l-.7 .7m0 11.4l.7 .7m-12.1 -.7l-.7 .7",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var p=e("outline","users-group","IconUsersGroup",[["path",{d:"M10 13a2 2 0 1 0 4 0a2 2 0 0 0 -4 0",key:"svg-0"}],["path",{d:"M8 21v-1a2 2 0 0 1 2 -2h4a2 2 0 0 1 2 2v1",key:"svg-1"}],["path",{d:"M15 5a2 2 0 1 0 4 0a2 2 0 0 0 -4 0",key:"svg-2"}],["path",{d:"M17 10h2a2 2 0 0 1 2 2v1",key:"svg-3"}],["path",{d:"M5 5a2 2 0 1 0 4 0a2 2 0 0 0 -4 0",key:"svg-4"}],["path",{d:"M3 13v-1a2 2 0 0 1 2 -2h2",key:"svg-5"}]]);/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var V=e("outline","users","IconUsers",[["path",{d:"M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0",key:"svg-0"}],["path",{d:"M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2",key:"svg-1"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"svg-2"}],["path",{d:"M21 21v-2a4 4 0 0 0 -3 -3.85",key:"svg-3"}]]);const H=[{title:"Chương Trình",icon:$,items:[{title:"Khuyến mãi",url:"/sale/promotion"},{title:"Combo",url:"/sale/combo"},{title:"Giảm giá",url:"/sale/discount/regular"},{title:"Giảm giá theo hội viên",url:"/sale/discount/membership",disabled:!1},{title:"Chiết khấu thanh toán",url:"/sale/discount-payment",disabled:!1},{title:"Phí dịch vụ",url:"/sale/service-charge",disabled:!1}]},{title:"Kênh Bán Hàng",icon:y,items:[{title:"Kênh bán hàng",url:"/sale-channel/channel"},{title:"Giảm giá theo kênh",url:"/sale-channel/discount"}]},{title:"Thiết Bị",icon:G,items:[{title:"Quản lý thiết bị",url:"/devices/list"},{title:"Loại thiết bị",url:"/devices/types"}]},{title:"Nhân Viên",icon:V,items:[{title:"Danh sách nhân viên",url:"/employee/list"},{title:"Danh sách chức vụ",url:"/employee/role"}]}],U=[{title:"Ứng dụng",icon:p,items:[{title:"Marketing",icon:v,url:"/crm/config-register-page",guard:"crm",items:[{title:"Trang đăng ký thành viên",url:"/crm/config-register-page",guard:"crm"}]},{title:"Nhà hàng và thực đơn",icon:p,items:[{title:"Khách hàng",url:"/crm/customer-list",guard:"crm"},{title:"Biên tập nội dung thực đơn",url:"/crm/food-item-review/items",guard:"crm"},{title:"Nhà hàng",url:"/crm/general-setups/store-menu",guard:"crm"}]},{title:"Loyalty",icon:v,items:[{title:"Hạng thành viên",url:"/crm/loyalty/membership-type",guard:"crm"},{title:"Hệ số tích điểm",url:"/crm/loyalty/extra-point",guard:"crm"},{title:"Danh sách thành viên",url:"/crm/loyalty/member-list",guard:"crm"},{title:"Ưu đãi đổi điểm",url:"/crm/loyalty/point-rewards",guard:"crm"}]},{title:"Thanh toán",icon:w,items:[{title:"Bảng giá",url:"/crm/pricing-table",guard:"crm"},{title:"Hóa đơn",url:"/crm/invoice",guard:"crm"},{title:"Lưu lượng sử dụng",url:"/crm/using-month",guard:"crm"}]},{title:"Cài đặt",icon:A,items:[{title:"Cài đặt chung",url:"/crm/settings",guard:"crm"},{title:"Tài khoản",url:"/crm/general-setups/account",guard:"crm"},{title:"Nhật ký hệ thống",url:"/crm/system-log",guard:"crm"}]},{title:"Báo cáo",icon:B,items:[{title:"Biến động khách hàng",url:"/crm/report/customer"},{title:"Doanh thu thành viên",url:"/crm/report/revenue"},{title:"Phản hồi khách hàng",url:"/crm/report/rating-feedback"},{title:"Voucher",url:"/crm/report/voucher"},{title:"Bức tranh khách hàng",url:"/crm/customer-profile"},{title:"So sánh nhóm khách hàng",url:"/crm/customer-group"},{title:"Bảng kê hóa đơn",url:"/crm/report/sale-manager"}]}]}],O=[{title:"Trang Chủ",url:"/",icon:R},{title:"Cửa Hàng",icon:L,items:[{title:"Danh sách nhà hàng",url:"/setting/store"},{title:"Phương thức thanh toán",url:"/setting/payment-method"},{title:"Nguồn đơn hàng",url:"/setting/source"},{title:"Vị trí máy in",items:[{title:"Vị trí máy in toàn thương hiệu",url:"/setting/printer-position/printer-position-in-brand"},{title:"Vị trí máy in tại cửa hàng",url:"/setting/printer-position/printer-position-in-store"}]},{title:"Khu vực",url:"/setting/area"},{title:"Bàn",url:"/setting/table"},{title:"Sơ đồ bàn",url:"/setting/table-layout"},{title:"Mẫu hoá đơn",url:"/setting/bill-model"},{title:"Merchant Momo",url:"/setting/momo-merchant"},{title:"Liên kết điểm bán hàng",url:"/setting/schedule-sale"}]}],q=[{title:"Thực Đơn",icon:P,items:[{title:"Món ăn",items:[{title:"Món ăn tại thành phố",url:"/menu/items/items-in-city"},{title:"Món ăn tại cửa hàng",url:"/menu/items/items-in-store"}]},{title:"Nhóm món",items:[{title:"Nhóm món toàn thương hiệu",url:"/menu/categories/categories-in-brand"},{title:"Nhóm món tại cửa hàng",url:"/menu/categories/categories-in-store"}]},{title:"Loại món",url:"/menu/item-class"},{title:"Customization",items:[{title:"Customization tại thành phố",url:"/menu/customization/customization-in-city"},{title:"Customization tại cửa hàng",url:"/menu/customization/customization-in-store"}]},{title:"Lập lịch thực đơn",url:"/menu/schedule"},{title:"Món đã xoá",items:[{title:"Món đã xoá tại thành phố",url:"/menu/item-removed/item-removed-in-city"},{title:"Món đã xoá tại cửa hàng",url:"/menu/item-removed/item-removed-in-store"}]},{title:"Khai báo số lượng món",url:"/menu/quantity-day"}]}],Q=[{title:"Báo Cáo",icon:y,items:[{title:"Báo Cáo Doanh Thu - A",items:[{title:"A01 - Báo cáo doanh thu (net)",url:"/report/revenue/sale-summary"}]},{title:"Báo cáo kiểm soát - C",items:[{title:"C01 - Báo cáo tổng quan theo cửa hàng",url:"/report/revenue/revenue/general"}]},{title:"Báo cáo kế toán - D",items:[{title:"D02 - Báo cáo đối soát hoá đơn VAT",url:"/report/accounting/invoices/sale-sync-vat"},{title:"D07 - Bảng kê chi tiết hoá đơn bán hàng",url:"/report/accounting/sale-detail-audit"}]}]}],F={navGroups:[{title:"General",items:[...O,...q,...H,...Q,...U]}]};function J(){const i=C(),{setTheme:l}=f(),{open:c,setOpen:s}=W(),a=h.useCallback(r=>{s(!1),r()},[s]);return t.jsxs(I,{modal:!0,open:c,onOpenChange:s,children:[t.jsx(j,{placeholder:"Type a command or search..."}),t.jsx(b,{children:t.jsxs(N,{type:"hover",className:"h-72 pr-1",children:[t.jsx(D,{children:"No results found."}),F.navGroups.map(r=>t.jsx(g,{heading:r.title,children:r.items.map((n,M)=>{var u;return n.url?t.jsxs(o,{value:n.title,onSelect:()=>{a(()=>i({to:n.url}))},children:[t.jsx("div",{className:"mr-2 flex h-4 w-4 items-center justify-center",children:t.jsx(d,{className:"text-muted-foreground/80 size-2"})}),n.title]},`${n.url}-${M}`):(u=n.items)==null?void 0:u.map((m,x)=>t.jsxs(o,{value:`${n.title}-${m.url}`,onSelect:()=>{a(()=>i({to:m.url}))},children:[t.jsx("div",{className:"mr-2 flex h-4 w-4 items-center justify-center",children:t.jsx(d,{className:"text-muted-foreground/80 size-2"})}),n.title," ",t.jsx(z,{})," ",m.title]},`${n.title}-${m.url}-${x}`))})},r.title)),t.jsx(S,{}),t.jsxs(g,{heading:"Theme",children:[t.jsxs(o,{onSelect:()=>a(()=>l("light")),children:[t.jsx(E,{})," ",t.jsx("span",{children:"Light"})]}),t.jsxs(o,{onSelect:()=>a(()=>l("dark")),children:[t.jsx(K,{className:"scale-90"}),t.jsx("span",{children:"Dark"})]}),t.jsxs(o,{onSelect:()=>a(()=>l("system")),children:[t.jsx(T,{}),t.jsx("span",{children:"System"})]})]})]})})]})}const k=h.createContext(null);function at({children:i}){const[l,c]=h.useState(!1);return h.useEffect(()=>{const s=a=>{a.key==="k"&&(a.metaKey||a.ctrlKey)&&(a.preventDefault(),c(r=>!r))};return document.addEventListener("keydown",s),()=>document.removeEventListener("keydown",s)},[]),t.jsxs(k.Provider,{value:{open:l,setOpen:c},children:[i,t.jsx(J,{})]})}const W=()=>{const i=h.useContext(k);if(!i)throw new Error("useSearch has to be used within <SearchContext.Provider>");return i};export{p as I,at as S,E as a,K as b,A as c,F as s,W as u};
