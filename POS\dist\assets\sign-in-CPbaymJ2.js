import{j as r}from"./index-Bnt3OGV2.js";import{C as e,a as t,b as o,c as i,d as s,f as a}from"./card-aL168XsH.js";import{A as n}from"./auth-layout-CNHS5raG.js";import{U as m}from"./user-auth-form-BU1jp7OW.js";import"./form-wT1R35uI.js";import"./zod-CNC8A7Cl.js";import"./use-auth-Bxzq8gtF.js";import"./useMutation-d67-fNFq.js";import"./utils-km2FGkQ4.js";import"./pos-api-BwpRFGce.js";import"./input-CiKEYbig.js";import"./password-input-BnuMYfF_.js";import"./createReactComponent-BD5R5KSl.js";import"./IconBrandGithub-BuoIJq30.js";function c(){return r.jsx(n,{children:r.jsxs(e,{className:"gap-4",children:[r.jsxs(t,{children:[r.jsx(o,{className:"text-lg tracking-tight",children:"Login"}),r.jsxs(i,{children:["Enter your email and password below to ",r.jsx("br",{}),"log into your account"]})]}),r.jsx(s,{children:r.jsx(m,{})}),r.jsx(a,{children:r.jsxs("p",{className:"text-muted-foreground px-8 text-center text-sm",children:["By clicking login, you agree to our"," ",r.jsx("a",{href:"/terms",className:"hover:text-primary underline underline-offset-4",children:"Terms of Service"})," ","and"," ",r.jsx("a",{href:"/privacy",className:"hover:text-primary underline underline-offset-4",children:"Privacy Policy"}),"."]})})]})})}const A=c;export{A as component};
