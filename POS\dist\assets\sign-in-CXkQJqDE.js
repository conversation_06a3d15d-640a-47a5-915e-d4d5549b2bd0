import{j as r}from"./index-C21OP4ex.js";import{C as e,a as t,b as o,c as i,d as s,f as a}from"./card-Ctkrqupu.js";import{A as n}from"./auth-layout-GxVxJN4C.js";import{U as m}from"./user-auth-form-8JX-9Vtm.js";import"./form-usWdQ_Nt.js";import"./zod-B4gLZVLM.js";import"./use-auth-DBc6rGI6.js";import"./useMutation-Bh5DVQPI.js";import"./utils-km2FGkQ4.js";import"./pos-api-D5WM5mnz.js";import"./input-4sMIt001.js";import"./password-input-DT0dsbyP.js";import"./createReactComponent-zh6rKAzG.js";import"./IconBrandGithub-Bp3vGO9l.js";function c(){return r.jsx(n,{children:r.jsxs(e,{className:"gap-4",children:[r.jsxs(t,{children:[r.jsx(o,{className:"text-lg tracking-tight",children:"Login"}),r.jsxs(i,{children:["Enter your email and password below to ",r.jsx("br",{}),"log into your account"]})]}),r.jsx(s,{children:r.jsx(m,{})}),r.jsx(a,{children:r.jsxs("p",{className:"text-muted-foreground px-8 text-center text-sm",children:["By clicking login, you agree to our"," ",r.jsx("a",{href:"/terms",className:"hover:text-primary underline underline-offset-4",children:"Terms of Service"})," ","and"," ",r.jsx("a",{href:"/privacy",className:"hover:text-primary underline underline-offset-4",children:"Privacy Policy"}),"."]})})]})})}const A=c;export{A as component};
