import{a as c}from"./pos-api-BwpRFGce.js";const p=async t=>{var e,n,o,s;const a=await c.get("/mdata/v1/stores",{params:{company_uid:t.company_uid,brand_uid:t.brand_uid,page:t.page||1,limit:t.limit||50}});return{data:((e=a.data)==null?void 0:e.data)||a.data||[],total:(n=a.data)==null?void 0:n.total,page:((o=a.data)==null?void 0:o.page)||t.page||1,limit:((s=a.data)==null?void 0:s.limit)||t.limit||50}},m=async t=>(await c.post("/mdata/v1/store",t,{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"}})).data,y=async t=>(await c.put("/mdata/v1/store",t,{headers:{Accept:"application/json, text/plain, */*","Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"}})).data,S=async t=>{await c.put("/mdata/v1/store/is-franchise",t)},d=()=>{try{const t=localStorage.getItem("pos_stores_data");return t?JSON.parse(t):[]}catch{return[]}},u=t=>{try{localStorage.setItem("pos_stores_data",JSON.stringify(t))}catch{}},r=new Map,i=new Map,l=5*60*1e3,_=async(t,a)=>{const e=`${t}-${a}`,n=i.get(e);if(n&&Date.now()-n.timestamp<l)return n.data;if(r.has(e))return r.get(e);const o=(async()=>{try{const s=await p({company_uid:t,brand_uid:a,page:1,limit:100});return s.data?(u(s.data),i.set(e,{data:s.data,timestamp:Date.now()}),s.data):[]}catch{return d()}finally{r.delete(e)}})();return r.set(e,o),o};export{d as a,y as b,m as c,_ as f,p as g,S as u};
