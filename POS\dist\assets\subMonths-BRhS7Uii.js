import{t as u,i as f,h,e as D,g as l}from"./isSameMonth-C8JQo-AN.js";function y(r,a){const e=u(r,a==null?void 0:a.in);return e.setHours(23,59,59,999),e}function b(r,a){const{start:e,end:s}=f(a==null?void 0:a.in,r);let n=+e>+s;const c=n?+e:+s,t=n?s:e;t.setHours(0,0,0,0);let d=1;const m=[];for(;+t<=c;)m.push(h(e,t)),t.setDate(t.getDate()+d),t.setHours(0,0,0,0);return n?m.reverse():m}function H(r,a,e){const s=+u(r,e==null?void 0:e.in),[n,c]=[+u(a.start,e==null?void 0:e.in),+u(a.end,e==null?void 0:e.in)].sort((t,d)=>t-d);return s>=n&&s<=c}function I(r,a,e){return D(r,-a,e)}function T(r,a,e){return l(r,-1,e)}export{y as a,I as b,b as e,H as i,T as s};
