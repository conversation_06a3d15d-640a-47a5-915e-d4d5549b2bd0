import{j as s}from"./index-Bnt3OGV2.js";import{S as e}from"./skeleton-C-doKLMW.js";import"./date-range-picker-CVvofQC0.js";import"./form-wT1R35uI.js";import{b as l,e as a}from"./table-C602nYEy.js";function j(){return s.jsx(s.Fragment,{children:Array.from({length:5}).map((m,r)=>s.jsxs(l,{children:[s.jsx(a,{className:"text-center",children:s.jsx(e,{className:"mx-auto h-4 w-6"})}),s.jsx(a,{children:s.jsx(e,{className:"h-4 w-24"})}),s.jsx(a,{children:s.jsx(e,{className:"h-4 w-32"})}),s.jsx(a,{children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(e,{className:"h-4 w-20"}),s.jsx(e,{className:"h-4 w-4"})]})}),s.jsx(a,{children:s.jsx(e,{className:"h-8 w-8"})})]},r))})}export{j as T};
