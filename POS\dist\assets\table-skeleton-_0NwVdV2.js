import{j as s}from"./index-C21OP4ex.js";import{S as e}from"./skeleton-xUyFo20h.js";import"./date-range-picker-B1pgj5D_.js";import"./form-usWdQ_Nt.js";import{b as l,e as a}from"./table-BIu4Pah2.js";function j(){return s.jsx(s.<PERSON>agment,{children:Array.from({length:5}).map((m,r)=>s.jsxs(l,{children:[s.jsx(a,{className:"text-center",children:s.jsx(e,{className:"mx-auto h-4 w-6"})}),s.jsx(a,{children:s.jsx(e,{className:"h-4 w-24"})}),s.jsx(a,{children:s.jsx(e,{className:"h-4 w-32"})}),s.jsx(a,{children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(e,{className:"h-4 w-20"}),s.jsx(e,{className:"h-4 w-4"})]})}),s.jsx(a,{children:s.jsx(e,{className:"h-8 w-8"})})]},r))})}export{j as T};
