import{r as b,A as P,j as s,C as S,D as F,P as g,E as M,F as m,c as p}from"./index-C21OP4ex.js";import{c as h,R as k,I as D}from"./index-UJ-79IIJ.js";import{u as $}from"./index-Bh-UeytL.js";var x="Tabs",[V,J]=S(x,[h]),C=h(),[G,T]=V(x),I=b.forwardRef((e,t)=>{const{__scopeTabs:c,value:a,onValueChange:r,defaultValue:d,orientation:o="horizontal",dir:l,activationMode:v="automatic",...f}=e,i=$(l),[n,u]=P({prop:a,onChange:r,defaultProp:d});return s.jsx(G,{scope:c,baseId:F(),value:n,onValueChange:u,orientation:o,dir:i,activationMode:v,children:s.jsx(g.div,{dir:i,"data-orientation":o,...f,ref:t})})});I.displayName=x;var j="TabsList",y=b.forwardRef((e,t)=>{const{__scopeTabs:c,loop:a=!0,...r}=e,d=T(j,c),o=C(c);return s.jsx(k,{asChild:!0,...o,orientation:d.orientation,dir:d.dir,loop:a,children:s.jsx(g.div,{role:"tablist","aria-orientation":d.orientation,...r,ref:t})})});y.displayName=j;var _="TabsTrigger",w=b.forwardRef((e,t)=>{const{__scopeTabs:c,value:a,disabled:r=!1,...d}=e,o=T(_,c),l=C(c),v=A(o.baseId,a),f=E(o.baseId,a),i=a===o.value;return s.jsx(D,{asChild:!0,...l,focusable:!r,active:i,children:s.jsx(g.button,{type:"button",role:"tab","aria-selected":i,"aria-controls":f,"data-state":i?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:v,...d,ref:t,onMouseDown:m(e.onMouseDown,n=>{!r&&n.button===0&&n.ctrlKey===!1?o.onValueChange(a):n.preventDefault()}),onKeyDown:m(e.onKeyDown,n=>{[" ","Enter"].includes(n.key)&&o.onValueChange(a)}),onFocus:m(e.onFocus,()=>{const n=o.activationMode!=="manual";!i&&!r&&n&&o.onValueChange(a)})})})});w.displayName=_;var N="TabsContent",R=b.forwardRef((e,t)=>{const{__scopeTabs:c,value:a,forceMount:r,children:d,...o}=e,l=T(N,c),v=A(l.baseId,a),f=E(l.baseId,a),i=a===l.value,n=b.useRef(i);return b.useEffect(()=>{const u=requestAnimationFrame(()=>n.current=!1);return()=>cancelAnimationFrame(u)},[]),s.jsx(M,{present:r||i,children:({present:u})=>s.jsx(g.div,{"data-state":i?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":v,hidden:!u,id:f,tabIndex:0,...o,ref:t,style:{...e.style,animationDuration:n.current?"0s":void 0},children:u&&d})})});R.displayName=N;function A(e,t){return`${e}-trigger-${t}`}function E(e,t){return`${e}-content-${t}`}var L=I,z=y,K=w,B=R;function Q({className:e,...t}){return s.jsx(L,{"data-slot":"tabs",className:p("flex flex-col gap-2",e),...t})}function U({className:e,...t}){return s.jsx(z,{"data-slot":"tabs-list",className:p("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function W({className:e,...t}){return s.jsx(K,{"data-slot":"tabs-trigger",className:p("dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow,background-color,border-color] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:border-blue-200 data-[state=active]:bg-white data-[state=active]:font-semibold data-[state=active]:text-blue-600 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function X({className:e,...t}){return s.jsx(B,{"data-slot":"tabs-content",className:p("flex-1 outline-none",e),...t})}export{Q as T,U as a,X as b,W as c};
