import{u as S}from"./useQuery-BNGphiae.js";import{u as h,a3 as y,a4 as c}from"./index-C21OP4ex.js";import{u as m}from"./useMutation-Bh5DVQPI.js";import{a as _}from"./pos-api-D5WM5mnz.js";import{i as b}from"./images-api-V0IeMkhY.js";import{Q as p}from"./query-keys-3lmd-xp6.js";const l={getAreasList:async t=>{const a=new URLSearchParams({company_uid:t.company_uid,brand_uid:t.brand_uid,store_uid:t.store_uid,page:(t.page||1).toString()});return typeof t.skip_limit<"u"&&a.append("skip_limit",String(t.skip_limit)),typeof t.results_per_page<"u"&&a.append("results_per_page",String(t.results_per_page)),typeof t.limit<"u"&&a.append("limit",String(t.limit)),(await _.get(`/pos/v1/area?${a.toString()}`)).data},updateArea:async t=>{console.log("Updating area with POST:",t);const a=await _.post("/pos/v1/area",t);return a.data.data||a.data},deleteArea:async t=>{const a=new URLSearchParams({company_uid:t.company_uid,brand_uid:t.brand_uid,store_uid:t.store_uid});await _.delete(`/pos/v1/area/${t.id}?${a.toString()}`)},getAreaById:async(t,a,n,e)=>{const r=new URLSearchParams({company_uid:a,brand_uid:n,store_uid:e,id:t}),i=await _.get(`/pos/v1/area?${r.toString()}`);return i.data.data||i.data},deleteAreas:async t=>(await _.delete("/pos/v1/area",{data:t})).data,createArea:async t=>{var n;const a=await _.post("/pos/v1/area",[t]);return((n=a.data.data)==null?void 0:n[0])||a.data[0]},bulkImportAreas:async t=>{const a=await _.post("/pos/v1/area",t);return a.data.data||a.data}},R=(t={})=>{var i;const{company:a,brands:n}=h(s=>s.auth),e=n==null?void 0:n[0],r=S({queryKey:[p.AREAS_LIST,"filter",t.storeUid,t.page,t.results_per_page],queryFn:async()=>{const s=(a==null?void 0:a.id)||"",u=(e==null?void 0:e.id)||"",o=t.storeUid||"";if(!s||!u||!o)throw new Error("Thiếu thông tin cần thiết");const d={company_uid:s,brand_uid:u,store_uid:o,skip_limit:!0,page:t.page,results_per_page:t.results_per_page};return await l.getAreasList(d)},enabled:!!(a!=null&&a.id&&(e!=null&&e.id)&&t.storeUid),staleTime:5*60*1e3});return{...r,data:((i=r.data)==null?void 0:i.data)||[]}},C=()=>{const t=y(),{mutate:a,isPending:n}=m({mutationFn:async e=>await l.updateArea(e),onSuccess:()=>{t.invalidateQueries({queryKey:[p.AREAS_LIST]}),t.invalidateQueries({queryKey:[p.AREAS_DETAIL]}),c.success("Cập nhật khu vực thành công")},onError:e=>{var i,s;const r=((s=(i=e==null?void 0:e.response)==null?void 0:i.data)==null?void 0:s.message)||"Có lỗi xảy ra khi cập nhật khu vực";c.error(r)}});return{updateArea:a,isUpdating:n}},I=()=>{const t=y(),{mutate:a,isPending:n}=m({mutationFn:async e=>{const r=e.active===1?0:1,i={id:e.id,area_id:e.area_id,area_name:e.area_name,description:e.description||"",extra_data:e.extra_data||{},active:r,revision:e.revision,sort:e.sort,store_uid:e.store_uid,brand_uid:e.brand_uid,company_uid:e.company_uid,store_id:e.store_id||null,brand_id:e.brand_id||null,company_id:e.company_id||null,is_fabi:e.is_fabi,created_by:e.created_by,updated_by:e.updated_by||e.created_by,created_at:e.created_at,updated_at:e.updated_at,list_table_id:e.list_table_id||[]};return console.log("Sending area update:",i),console.log("Original area:",e),console.log("Making POST request to: /pos/v1/area"),(await _.post("/pos/v1/area",i)).data},onSuccess:e=>{t.invalidateQueries({queryKey:[p.AREAS_LIST]});const r=e.active===1?"kích hoạt":"vô hiệu hóa";c.success(`${r} khu vực thành công`)},onError:e=>{var i,s;const r=((s=(i=e==null?void 0:e.response)==null?void 0:i.data)==null?void 0:s.message)||"Có lỗi xảy ra khi cập nhật trạng thái khu vực";c.error(r)}});return{toggleAreaStatus:a,isToggling:n}},L=()=>{const t=y(),{company:a,brands:n}=h(i=>i.auth),{mutate:e,isPending:r}=m({mutationFn:async i=>{var d;const s=(a==null?void 0:a.id)||"",u=((d=n==null?void 0:n[0])==null?void 0:d.id)||"";if(!s||!u||!i.storeUid)throw new Error("Missing required authentication information");const o={company_uid:s,brand_uid:u,list_id:i.areaIds,store_uid:i.storeUid};return console.log("Deleting areas:",o),await l.deleteAreas(o)},onSuccess:(i,s)=>{t.invalidateQueries({queryKey:[p.AREAS_LIST]});const u=s.areaIds.length,o=u===1?"Xóa khu vực thành công":`Xóa ${u} khu vực thành công`;c.success(o)},onError:i=>{var u,o;const s=((o=(u=i==null?void 0:i.response)==null?void 0:u.data)==null?void 0:o.message)||"Có lỗi xảy ra khi xóa khu vực";c.error(s)}});return{deleteAreas:e,isDeleting:r}},P=(t,a)=>{const{company:n,brands:e}=h(i=>i.auth),r=e==null?void 0:e[0];return S({queryKey:[p.AREAS_DETAIL,t,a],queryFn:async()=>{const i=(n==null?void 0:n.id)||"",s=(r==null?void 0:r.id)||"";if(!i||!s||!t||!a)throw new Error("Thiếu thông tin cần thiết");return await l.getAreaById(t,i,s,a)},enabled:!!(n!=null&&n.id&&(r!=null&&r.id)&&t&&a),staleTime:5*60*1e3})},v=()=>{const t="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";let a="AREA-";for(let n=0;n<4;n++)a+=t.charAt(Math.floor(Math.random()*t.length));return a},Q=()=>{const t=y(),{company:a,brands:n}=h(s=>s.auth),e=n==null?void 0:n[0],{mutate:r,isPending:i}=m({mutationFn:async s=>{const u=(a==null?void 0:a.id)||"",o=(e==null?void 0:e.id)||"";if(!u||!o)throw new Error("Thiếu thông tin công ty hoặc thương hiệu");const d=v();let g="";s.backgroundFile&&(g=(await b.uploadImage(s.backgroundFile)).data.image_url);const A={area_name:s.area_name,area_id:d,description:s.description,store_uid:s.store_uid,brand_uid:o,company_uid:u,sort:s.sort||1,active:1,extra_data:g?{background:g}:void 0};return await l.createArea(A)},onSuccess:()=>{t.invalidateQueries({queryKey:[p.AREAS_LIST]}),c.success("Tạo khu vực thành công")},onError:s=>{var o,d;const u=((d=(o=s==null?void 0:s.response)==null?void 0:o.data)==null?void 0:d.message)||"Có lỗi xảy ra khi tạo khu vực";c.error(u)}});return{createArea:r,isCreating:i}},x=()=>{const t=y(),{company:a,brands:n}=h(s=>s.auth),e=n==null?void 0:n[0],{mutateAsync:r,isPending:i}=m({mutationFn:async s=>{const u=(a==null?void 0:a.id)||"",o=(e==null?void 0:e.id)||"";if(!u||!o)throw new Error("Thiếu thông tin công ty hoặc thương hiệu");const d=s.areas.map((g,A)=>({company_uid:u,brand_uid:o,area_name:g.area_name,area_id:v(),description:g.description||"",store_uid:s.storeUid,sort:A+1}));return await l.bulkImportAreas(d)},onSuccess:()=>{t.invalidateQueries({queryKey:[p.AREAS_LIST]})},onError:s=>{var o,d;const u=((d=(o=s==null?void 0:s.response)==null?void 0:o.data)==null?void 0:d.message)||"Có lỗi xảy ra khi import khu vực";c.error(u)}});return{mutateAsync:r,isPending:i}};export{x as a,L as b,l as c,I as d,Q as e,C as f,P as g,R as u};
