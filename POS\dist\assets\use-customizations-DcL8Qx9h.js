const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/exceljs.min-Da2DFgpf.js","assets/index-Bnt3OGV2.js","assets/index-CVkaVZVo.css"])))=>i.map(i=>d[i]);
import{u as A}from"./useQuery-DSrD7NAp.js";import{ay as E,u as T,a3 as m}from"./index-Bnt3OGV2.js";import{u as U}from"./useMutation-d67-fNFq.js";import{a as p}from"./pos-api-BwpRFGce.js";import{Q as b}from"./query-keys-3lmd-xp6.js";const v=e=>({id:e.id,name:e.name,data:e.data,extraData:e.extra_data,active:e.active,revision:e.revision,sort:e.sort,brandUid:e.brand_uid,companyUid:e.company_uid,isCustomizationInStore:e.is_customization_in_store,updatedAt:e.updated_at,cityUid:e.city_uid,cityName:e.city_name,storeUid:e.store_uid,customizationClonedId:e.customization_cloned_id,listItem:e.list_item}),P=async e=>{const a=await E(()=>import("./exceljs.min-Da2DFgpf.js").then(_=>_.e),__vite__mapDeps([0,1,2])),t=e.some(_=>_.storeUid||_.storeName||_.isCustomizationInStore===1),r=[t?["Tên","Cửa hàng","Mã món áp dụng","Tên nhóm","Yêu cầu chọn","Giới hạn chọn","Mã món theo nhóm"]:["ID","Tên","Thành phố","Mã món áp dụng","Tên nhóm","Yêu cầu chọn","Giới hạn chọn","Mã món theo nhóm"]];e.forEach(_=>{var I,f,D;const s=(I=_.data.LstItem_Options)==null?void 0:I[0];t?r.push([_.name,_.storeName||"Cửa hàng hiện tại",_.listItem.join(","),(s==null?void 0:s.Name)||"",String((s==null?void 0:s.Min_Permitted)||""),String((s==null?void 0:s.Max_Permitted)||""),((f=s==null?void 0:s.LstItem_Id)==null?void 0:f.join(","))||""]):r.push([_.id,_.name,_.cityName,_.listItem.join(","),(s==null?void 0:s.Name)||"",String((s==null?void 0:s.Min_Permitted)||""),String((s==null?void 0:s.Max_Permitted)||""),((D=s==null?void 0:s.LstItem_Id)==null?void 0:D.join(","))||""])});const c=new a.Workbook,o=c.addWorksheet("Customizations");r.forEach((_,s)=>{const I=o.addRow(_);s===0&&I.eachCell(f=>{f.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF79abe3"}},f.font={bold:!0,color:{argb:"FFFFFFFF"}},f.alignment={horizontal:"center",vertical:"middle"}})}),o.columns.forEach(_=>{_&&(_.width=15)});const i=`customizations_export_${new Date().toISOString().split("T")[0]}.xlsx`,d=await c.xlsx.writeBuffer(),y=new Blob([d],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),u=document.createElement("a"),h=URL.createObjectURL(y);u.setAttribute("href",h),u.setAttribute("download",i),u.style.visibility="hidden",document.body.appendChild(u),u.click(),document.body.removeChild(u),URL.revokeObjectURL(h)},k=async(e,a,t)=>{const r=localStorage.getItem("pos_user_data"),c=localStorage.getItem("pos_brands_data");let o="",l="";if(r)try{o=JSON.parse(r).company_uid||""}catch{}if(c)try{const d=JSON.parse(c);Array.isArray(d)&&d.length>0&&(l=d[0].id||"")}catch{}if(!o||!l)throw new Error("Company or brand UID not found in localStorage");const n=e.map(d=>{const y=d.groupItemCodes?d.groupItemCodes.split(",").map(_=>_.trim()).filter(Boolean):[],u=d.appliedItemCodes?d.appliedItemCodes.split(",").map(_=>_.trim()).filter(Boolean):[],h={name:d.name,list_item:u,data:{LstItem_Options:[{Name:d.groupName,Min_Permitted:d.minRequired,Max_Permitted:d.maxAllowed,LstItem_Id:y}]},company_uid:o,brand_uid:l};return a?h.store_uid=a:t&&(h.city_uid=t),h});await p.post("/mdata/v1/customizations/import",n)},w=()=>{try{const e=localStorage.getItem("pos_cities_data");if(e)return JSON.parse(e).map(t=>t.id)}catch{}return[]},F=async(e={})=>{var c;const a=new URLSearchParams;if(e.company_uid&&a.append("company_uid",e.company_uid),e.brand_uid&&a.append("brand_uid",e.brand_uid),e.skip_limit&&a.append("skip_limit","true"),e.store_uid&&a.append("store_uid",e.store_uid),e.list_city_uid&&a.append("list_city_uid",e.list_city_uid),e.page&&a.append("page",String(e.page)),e.limit&&a.append("limit",String(e.limit)),e.searchTerm&&a.append("search",e.searchTerm),!e.skip_limit){if(e.list_store_uid&&e.list_store_uid.length>0)e.list_store_uid.length===1?a.append("store_uid",e.list_store_uid[0]):a.append("list_store_uid",e.list_store_uid.join(","));else if(e.list_city_uid&&e.list_city_uid.length>0)a.has("list_city_uid")||a.append("list_city_uid",e.list_city_uid.join(","));else if(!a.has("list_city_uid")&&!e.store_uid){const o=w();if(o.length>0)a.append("list_city_uid",o.join(","));else throw new Error("No cities or stores available for filtering")}}const t=`/mdata/v1/customizations?${a.toString()}`,r=await p.get(t);return(c=r.data)!=null&&c.data?r.data.data.map(l=>v(l)):[]},N=async e=>{var r;const a=`/mdata/v1/customization?id=${e}`,t=await p.get(a);if((r=t.data)!=null&&r.data)return t.data.data;throw new Error("Customization not found")},M=async e=>{const a=localStorage.getItem("pos_user_data"),t=localStorage.getItem("pos_brands_data");let r="",c="";if(a)try{r=JSON.parse(a).company_uid||""}catch{}if(t)try{const i=JSON.parse(t);Array.isArray(i)&&i.length>0&&(c=i[0].id||"")}catch{}if(!r||!c)throw new Error("Company or brand UID not found in localStorage");const o=await N(e.customizationId),l={data:o.data,sort:o.sort,brand_uid:c,company_uid:r,list_item:o.list_item||[],name:e.newName};e.targetStoreUid?l.store_uid=e.targetStoreUid:l.city_uid=e.targetCityUid||o.city_uid,await p.post("/mdata/v1/customization",l)},q=async e=>{const a=localStorage.getItem("pos_user_data"),t=localStorage.getItem("pos_brands_data");let r="",c="";if(a)try{r=JSON.parse(a).company_uid||""}catch{}if(t)try{const n=JSON.parse(t);Array.isArray(n)&&n.length>0&&(c=n[0].id||"")}catch{}if(!r||!c)throw new Error("Company or brand UID not found in localStorage");const o={name:e.name,data:e.data,list_item:e.listItem,company_uid:r,brand_uid:c,sort:e.sort||1e3,is_update_same_customization:e.isUpdateSameCustomization||!1};if(e.storeUid)o.store_uid=e.storeUid;else if(e.cityUid)o.city_uid=e.cityUid;else throw new Error("Either storeUid or cityUid must be provided");await p.post("/mdata/v1/customization",o)},j=async(e,a,t)=>{const r=localStorage.getItem("pos_user_data"),c=localStorage.getItem("pos_brands_data");let o="",l="";if(r)try{o=JSON.parse(r).company_uid||""}catch{}if(c)try{const d=JSON.parse(c);Array.isArray(d)&&d.length>0&&(l=d[0].id||"")}catch{}if(!o||!l)throw new Error("Company or brand UID not found in localStorage");const n={id:e,name:a.name,data:a.data,extra_data:(t==null?void 0:t.extra_data)||(t==null?void 0:t.extraData)||{},active:(t==null?void 0:t.active)||1,revision:(t==null?void 0:t.revision)||0,sort:a.sort||1e3,brand_uid:l,company_uid:o,is_customization_in_store:(t==null?void 0:t.is_customization_in_store)||(t==null?void 0:t.isCustomizationInStore)||1,customization_cloned_id:(t==null?void 0:t.customization_cloned_id)||(t==null?void 0:t.customizationClonedId)||null,is_fabi:(t==null?void 0:t.is_fabi)||1,created_at:t==null?void 0:t.created_at,updated_at:Math.floor(Date.now()/1e3),deleted_at:(t==null?void 0:t.deleted_at)||null,created_by:(t==null?void 0:t.created_by)||"",updated_by:(t==null?void 0:t.updated_by)||"",deleted_by:(t==null?void 0:t.deleted_by)||null,deleted:(t==null?void 0:t.deleted)||!1,list_item:a.listItem,is_update_same_customization:a.isUpdateSameCustomization||!1};if(a.storeUid)n.store_uid=a.storeUid,n.city_uid=(t==null?void 0:t.city_uid)||(t==null?void 0:t.cityUid)||"";else if(a.cityUid)n.city_uid=a.cityUid,n.store_uid=(t==null?void 0:t.store_uid)||(t==null?void 0:t.storeUid)||"";else throw new Error("Either storeUid or cityUid must be provided");await p.put("/mdata/v1/customization",n)},J=async e=>{const a=localStorage.getItem("pos_user_data"),t=localStorage.getItem("pos_brands_data");let r="",c="";if(a)try{r=JSON.parse(a).company_uid||""}catch{}if(t)try{const n=JSON.parse(t);Array.isArray(n)&&n.length>0&&(c=n[0].id||"")}catch{}if(!r||!c)throw new Error("Company or brand UID not found in localStorage");const l=`/mdata/v1/customization?${new URLSearchParams({company_uid:r,brand_uid:c,id:e}).toString()}`;await p.delete(l)},O=async(e={})=>{var i;const a=localStorage.getItem("pos_user_data"),t=localStorage.getItem("pos_brands_data");let r="",c="";if(a)try{r=JSON.parse(a).company_uid||""}catch{}if(t)try{const d=JSON.parse(t);Array.isArray(d)&&d.length>0&&(c=d[0].id||"")}catch{}if(!r||!c)throw new Error("Company or brand UID not found in localStorage");const o=new URLSearchParams({skip_limit:"true",company_uid:r,brand_uid:c});if(e.list_store_uid&&e.list_store_uid.length>0)e.list_store_uid.length===1?o.append("store_uid",e.list_store_uid[0]):o.append("list_store_uid",e.list_store_uid.join(","));else if(e.list_city_uid&&e.list_city_uid.length>0){const d=w();e.list_city_uid.length===d.length&&e.list_city_uid.every(u=>d.includes(u))?o.append("list_city_uid",d.join(",")):o.append("list_city_uid",e.list_city_uid.join(","))}else{const d=w();if(d.length>0)o.append("list_city_uid",d.join(","));else throw new Error("No cities or stores available for filtering")}const l=`/mdata/v1/customizations?${o.toString()}`,n=await p.get(l);if((i=n.data)!=null&&i.data){const y=n.data.data.map(u=>v(u)).map(u=>e.list_store_uid&&e.list_store_uid.length>0&&e.storeName?{...u,storeName:e.storeName,storeUid:e.list_store_uid[0]}:u);await P(y)}else throw new Error("No customizations data received from API")},S={getCustomizations:F,getCustomizationById:N,copyCustomization:M,createCustomization:q,updateCustomization:j,deleteCustomization:J,exportCustomizations:O,bulkImportCustomizations:k};function Z(e={}){const{enabled:a=!0,skip_limit:t,store_uid:r,list_city_uid:c,...o}=e,{company:l,brands:n}=T(y=>y.auth),i=n==null?void 0:n[0],d={company_uid:(l==null?void 0:l.id)||"",brand_uid:(i==null?void 0:i.id)||"",...o,...t?{skip_limit:t}:{},...r?{store_uid:r}:{},...c&&c.length>0?{list_city_uid:c.join(",")}:{}};return A({queryKey:[b.CUSTOMIZATIONS,d],queryFn:async()=>await S.getCustomizations(d)||[],enabled:a&&!!(l!=null&&l.id&&(i!=null&&i.id)),staleTime:5*60*1e3,gcTime:10*60*1e3})}function $(){const e=m();return U({mutationFn:a=>S.copyCustomization(a),onSuccess:()=>{e.invalidateQueries({queryKey:[b.CUSTOMIZATIONS]})}})}function Y(){const e=m();return U({mutationFn:a=>S.createCustomization(a),onSuccess:()=>{e.invalidateQueries({queryKey:[b.CUSTOMIZATIONS]})}})}function W(){const e=m();return U({mutationFn:a=>S.deleteCustomization(a),onSuccess:()=>{e.invalidateQueries({queryKey:[b.CUSTOMIZATIONS]})}})}function G(){return U({mutationFn:e=>S.exportCustomizations(e)})}function V(){const e=m();return U({mutationFn:({parsedData:a,storeUid:t,cityUid:r})=>S.bulkImportCustomizations(a,t,r),onSuccess:()=>{e.invalidateQueries({queryKey:[b.CUSTOMIZATIONS]})}})}export{Y as a,j as b,v as c,$ as d,W as e,G as f,N as g,V as h,Z as u};
