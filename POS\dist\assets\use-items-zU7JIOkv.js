import{u as o}from"./useQuery-BNGphiae.js";import{u as m,l as c}from"./index-C21OP4ex.js";import{i as d}from"./item-api-C8PXkgMG.js";import{Q as p}from"./query-keys-3lmd-xp6.js";const l=(e={})=>{const{params:i={skip_limit:!0,active:1},enabled:u=!0}=e,{company:t}=m(a=>a.auth),{selectedBrand:r}=c(),s={...{company_uid:(t==null?void 0:t.id)||"",brand_uid:(r==null?void 0:r.id)||""},...i},n=!!(t!=null&&t.id&&(r!=null&&r.id));return o({queryKey:[p.ITEMS_LIST,s],queryFn:async()=>(await d.getItems(s)).data||[],enabled:u&&n,staleTime:10*60*1e3,refetchInterval:15*60*1e3})};export{l as u};
