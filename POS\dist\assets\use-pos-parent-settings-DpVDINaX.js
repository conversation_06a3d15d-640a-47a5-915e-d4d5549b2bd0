import{u as n}from"./useQuery-DSrD7NAp.js";import"./user-oq7iQk7S.js";import{l as o}from"./index-Bnt3OGV2.js";import{c as s}from"./crm-api-Dd9UhSCJ.js";import"./pos-api-BwpRFGce.js";import{C as a}from"./query-keys-DQo7uRnN.js";import{c as i}from"./settings-api-CYmE3Qr-.js";const g={getRegisterPageConfig:async e=>(await s.get("/marketing/get-config-register-page",{params:{pos_parent:e}})).data,saveRegisterPageConfig:async e=>(await s.post("/marketing/save-config-register-page",e)).data};function C(e={}){const{selectedBrand:r}=o(),t=e.pos_parent||(r==null?void 0:r.brandId)||"";return n({queryKey:["crm",a.REGISTER_PAGE_CONFIG,t],queryFn:async()=>g.getRegisterPageConfig(t),enabled:!!t})}function R(e){return n({queryKey:[a.POS_PARENT_SETTINGS,e],queryFn:()=>i.getCrmSettings(e.pos_parent),enabled:!!e.pos_parent})}export{R as a,g as m,C as u};
