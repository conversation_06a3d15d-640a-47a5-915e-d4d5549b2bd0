import{a as h,b as v,r as y}from"./index-Bnt3OGV2.js";import{u as w}from"./useQuery-DSrD7NAp.js";import{a as q}from"./pos-api-BwpRFGce.js";const m={all:["sourceData"],lists:()=>[...m.all,"list"],list:o=>[...m.lists(),o]};function k(o={}){const{autoFetch:g=!0,storeUid:u,brandUid:b,skipLimit:l}=o,{selectedBrand:a}=h(),{company:r}=v(),n=b||(a==null?void 0:a.id),i=r==null?void 0:r.id,{data:s,isLoading:_,error:d,refetch:S}=w({queryKey:m.list({companyUid:i,brandUid:n,storeUid:u,skipLimit:l,page:1}),queryFn:async()=>{if(!n||!i)throw new Error("Brand or company not selected");const e=new URLSearchParams({company_uid:i,brand_uid:n,page:"1"});return u&&e.append("store_uid",u),l&&e.append("skip_limit","true"),(await q.get(`/mdata/v1/sources?${e.toString()}`)).data},enabled:g&&!!n&&!!i,staleTime:10*60*1e3,gcTime:30*60*1e3,retry:(e,t)=>{var p,f;return(p=t==null?void 0:t.message)!=null&&p.includes("401")||(f=t==null?void 0:t.message)!=null&&f.includes("403")?!1:e<3}}),c=y.useMemo(()=>s!=null&&s.data?s.data.filter(e=>e.active===1):[],[s==null?void 0:s.data]),U=y.useMemo(()=>c.map(e=>({value:e.source_id,label:e.source_name,id:e.id})),[c]);return{sources:c,sourceOptions:U,isLoading:_,error:(d==null?void 0:d.message)||null,refetch:S,selectedBrand:a}}export{k as u};
