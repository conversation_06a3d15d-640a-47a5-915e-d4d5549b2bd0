import{a3 as u}from"./index-Bnt3OGV2.js";import{u as a}from"./useMutation-d67-fNFq.js";import{b as n}from"./use-customizations-DcL8Qx9h.js";import{Q as o}from"./query-keys-3lmd-xp6.js";function y(){const e=u();return a({mutationFn:async({customizationId:r,existingCustomization:t,...i})=>n(r,i,t||{}),onSuccess:()=>{e.invalidateQueries({queryKey:[o.CUSTOMIZATIONS]}),e.invalidateQueries({queryKey:[o.CUSTOMIZATIONS_DETAIL]})},onError:r=>{console.error("Update customization error:",r)}})}export{y as u};
