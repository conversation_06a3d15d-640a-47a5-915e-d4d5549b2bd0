import{u as T}from"./useQuery-DSrD7NAp.js";import{u as p,a3 as E,a4 as l}from"./index-Bnt3OGV2.js";import{u as w}from"./useMutation-d67-fNFq.js";import{b as h}from"./pos-api-BwpRFGce.js";import{Q as v}from"./query-keys-3lmd-xp6.js";const f={createUser:async e=>{const t=await h.post("/accounts/v1/users",e);return t.data.data||t.data},getUser:async e=>{var o,u,d,g,q,C;const t=await h.get(`/accounts/v1/user?user_uid=${e}`),n=t.data.data||t.data,s=(n==null?void 0:n.user)||((o=n==null?void 0:n.data)==null?void 0:o.user)||(n==null?void 0:n.data)||n,a=(n==null?void 0:n.user_role)||((u=n==null?void 0:n.data)==null?void 0:u.user_role),i=(n==null?void 0:n.user_permissions)||((d=n==null?void 0:n.data)==null?void 0:d.user_permissions),c=(n==null?void 0:n.brands)||((g=n==null?void 0:n.data)==null?void 0:g.brands),b=(n==null?void 0:n.cities)||((q=n==null?void 0:n.data)==null?void 0:q.cities),_=(n==null?void 0:n.stores)||((C=n==null?void 0:n.data)==null?void 0:C.stores),r=[];return i!=null&&i.stores&&Object.keys(i.stores).length>0&&(Object.keys(i.stores).forEach(m=>{const S=i.stores[m];S&&typeof S=="object"&&Object.keys(S).forEach(A=>{const k=S[A];Array.isArray(k)&&k.forEach(U=>{typeof U=="string"&&r.push(U.startsWith("store:")?U:`store:${U}`)})})}),r.length===0&&Object.keys(i.stores).forEach(m=>{r.push(m.startsWith("store:")?m:`store:${m}`)})),{id:s.id,email:s.email,full_name:s.full_name,phone:s.phone||"",role_uid:s.role_uid,active:s.active,phone_verified_at:s.phone_verified_at,role_name:(a==null?void 0:a.role_name)||"",role_id:(a==null?void 0:a.role_id)||"",role_description:(a==null?void 0:a.description)||"",stores:(i==null?void 0:i.stores)||{},brand_access:r,user_permissions:i?{id:i.id,user_uid:i.user_uid,company_uid:i.company_uid,stores:i.stores,tables:i.tables}:void 0,brands:c,cities:b,storeDetails:_}},updateUser:async(e,t)=>{const n=await h.put(`/accounts/v1/users/${e}`,t);return n.data.data||n.data},updateUserProfile:async e=>{const t=await h.put("/accounts/v1/user",e);return t.data.data||t.data},deactivateUser:async e=>{const t=await h.patch(`/accounts/v1/users/${e}/deactivate`);return t.data.data||t.data},activateUser:async e=>{const t=await h.patch(`/accounts/v1/user?user_uid=${e}&active=1`);return t.data.data||t.data},deactivateUserNew:async e=>{const t=await h.patch(`/accounts/v1/user?user_uid=${e}&active=0`);return t.data.data||t.data},changePassword:async e=>{const t=await h.post("/accounts/v1/user/change-password",e);return t.data.data||t.data},getUsers:async(e={})=>{const t=new URLSearchParams;e.company_uid&&t.append("company_uid",e.company_uid),e.active!==void 0&&t.append("active",e.active.toString()),e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),e.search&&t.append("search",e.search),e.brand_uid&&e.brand_uid!=="all"&&t.append("brand_uid",e.brand_uid),e.city_uid&&e.city_uid!=="all"&&t.append("city_uid",e.city_uid),e.store_uid&&e.store_uid!=="all"&&t.append("store_uid",e.store_uid);const n=`/accounts/v1/users${t.toString()?`?${t.toString()}`:""}`,s=await h.get(n);return Array.isArray(s.data)?{data:s.data,total:s.data.length}:s.data&&typeof s.data=="object"&&"data"in s.data?s.data:{data:[],total:0}}};function L(e={}){const{enabled:t=!0,...n}=e,{company:s}=p(c=>c.auth),a={company_uid:s==null?void 0:s.id,...n},i=!!(s!=null&&s.id||a.company_uid);return T({queryKey:[v.USERS_LIST,a],queryFn:async()=>(await f.getUsers(a)).data||[],enabled:t&&i,staleTime:5*60*1e3,gcTime:10*60*1e3})}function j(){const e=E(),{company:t,stores:n}=p(s=>s.auth);return w({mutationFn:async s=>{const a=(t==null?void 0:t.id)||s.company_uid;if(!a)throw new Error("Company UID is required");let i;if(s.brand_access&&s.brand_access.length>0){i={company_uid:a,tables:{},stores:{}};const b=n||[];s.brand_access.forEach(_=>{if(_.startsWith("store:")){const r=_.replace("store:",""),y=b.find(o=>o.id===r);if(y){const o=y.brand_uid,u=y.city_uid;i.stores[o]||(i.stores[o]={}),i.stores[o][u]||(i.stores[o][u]=[]),i.stores[o][u].push(r)}}})}const c={email:s.email,full_name:s.full_name,phone:s.phone,role_uid:s.role_uid,password:s.password,confirm_password:s.confirm_password,company_uid:a,...i&&{permissions:i}};return await f.createUser(c)},onSuccess:()=>{l.success("Tạo nhân viên thành công!"),e.invalidateQueries({queryKey:[v.USERS_LIST]})},onError:s=>{var i,c;const a=((c=(i=s==null?void 0:s.response)==null?void 0:i.data)==null?void 0:c.message)||(s==null?void 0:s.message)||"Có lỗi xảy ra khi tạo nhân viên";throw l.error(a),s}})}function x(e,t=!0){return T({queryKey:[v.USERS_LIST,e],queryFn:async()=>await f.getUser(e),enabled:t&&!!e,staleTime:5*60*1e3,gcTime:10*60*1e3})}function I(){const e=E(),{company:t,stores:n}=p(s=>s.auth);return w({mutationFn:async({userId:s,params:a,userData:i})=>{var _;const c={company_uid:(t==null?void 0:t.id)||"",tables:{},stores:{}};if(a.brand_access&&a.brand_access.length>0){c.stores={};const r=n||(i==null?void 0:i.storeDetails)||[];a.brand_access.forEach(y=>{if(y.startsWith("store:")){const o=y.replace("store:",""),u=r.find(d=>d.id===o);if(u){const d=u.brand_uid,g=u.city_uid;c.stores[d]||(c.stores[d]={}),c.stores[d][g]||(c.stores[d][g]=[]),c.stores[d][g].push(o)}}})}else(_=i==null?void 0:i.user_permissions)!=null&&_.stores&&(c.stores=i.user_permissions.stores,c.tables=i.user_permissions.tables||{});const b={id:s,phone:a.phone,full_name:a.full_name,profile_image_path:null,role_uid:a.role_uid,last_login_at:null,is_fabi:1,permissions:c};return await f.updateUserProfile(b)},onSuccess:()=>{l.success("Cập nhật nhân viên thành công!"),e.invalidateQueries({queryKey:[v.USERS_LIST]})},onError:s=>{var i,c;const a=((c=(i=s==null?void 0:s.response)==null?void 0:i.data)==null?void 0:c.message)||(s==null?void 0:s.message)||"Có lỗi xảy ra khi cập nhật nhân viên";throw l.error(a),s}})}function M(){const e=E();return w({mutationFn:async t=>await f.deactivateUserNew(t),onSuccess:()=>{l.success("Hủy kích hoạt nhân viên thành công!"),e.invalidateQueries({queryKey:[v.USERS_LIST]})},onError:t=>{var s,a;const n=((a=(s=t==null?void 0:t.response)==null?void 0:s.data)==null?void 0:a.message)||(t==null?void 0:t.message)||"Có lỗi xảy ra khi hủy kích hoạt nhân viên";throw l.error(n),t}})}function O(){const e=E();return w({mutationFn:async t=>await f.activateUser(t),onSuccess:()=>{l.success("Kích hoạt nhân viên thành công!"),e.invalidateQueries({queryKey:[v.USERS_LIST]})},onError:t=>{var s,a;const n=((a=(s=t==null?void 0:t.response)==null?void 0:s.data)==null?void 0:a.message)||(t==null?void 0:t.message)||"Có lỗi xảy ra khi kích hoạt nhân viên";throw l.error(n),t}})}function W(){return w({mutationFn:async e=>await f.changePassword(e),onError:e=>{var n,s;const t=((s=(n=e==null?void 0:e.response)==null?void 0:n.data)==null?void 0:s.message)||(e==null?void 0:e.message)||"Có lỗi xảy ra khi đổi mật khẩu";throw new Error(t)}})}export{M as a,L as b,W as c,j as d,I as e,x as f,O as u};
