import{z as e,y as r}from"./index-Bnt3OGV2.js";const a=e.union([e.literal("active"),e.literal("inactive")]);e.object({id:e.string(),active:e.number(),address:e.string(),city_uid:e.string(),created_at:e.number(),expiry_date:e.number(),email:e.string(),fb_store_id:e.number(),is_fabi:e.number(),updated_by:e.string(),updated_at:e.number(),brand_uid:e.string(),company_uid:e.string(),latitude:e.number(),longitude:e.number(),phone:e.string(),store_id:e.string(),store_name:e.string(),sort:e.number(),is_ahamove_active:e.number(),phone_manager:e.string(),delivery_services:e.string(),extra_data:e.record(e.any()),email_delivery_service:e.string(),city_name:e.string(),city_id:e.string()});const i=e.object({id:e.string(),name:e.string(),code:e.string(),status:a,address:e.string(),phone:e.string(),email:e.string(),managerPhone:e.string().optional(),companyId:e.string(),brandId:e.string(),cityId:e.string(),cityName:e.string(),isActive:e.boolean(),isFabi:e.boolean(),isAhamoveActive:e.boolean(),latitude:e.number(),longitude:e.number(),deliveryServices:e.string(),createdAt:e.coerce.date(),updatedAt:e.coerce.date(),expiryDate:e.coerce.date(),extraData:e.record(e.any()).optional(),sort:e.number().optional()});e.array(i);const m=t=>({id:t.id,name:t.store_name,code:t.store_id,status:t.active===1?"active":"inactive",address:t.address,phone:t.phone,email:t.email,managerPhone:t.phone_manager||void 0,companyId:t.company_uid,brandId:t.brand_uid,cityId:t.city_id,cityName:t.city_name,isActive:t.active===1,isFabi:t.is_fabi===1,isAhamoveActive:t.is_ahamove_active===1,latitude:t.latitude,longitude:t.longitude,deliveryServices:t.delivery_services,createdAt:new Date(t.created_at*1e3),updatedAt:new Date(t.updated_at*1e3),expiryDate:new Date(t.expiry_date*1e3),extraData:t.extra_data,sort:t.sort}),s=e.enum(["active","inactive"]),d=e.object({id:e.string(),item_class_id:e.string(),item_class_name:e.string(),description:e.string().nullable(),sort:e.number(),extra_data:e.any().nullable(),active:e.number(),revision:e.number(),brand_uid:e.string(),company_uid:e.string(),created_by:e.string().nullable(),updated_by:e.string().nullable(),deleted_by:e.string().nullable(),created_at:e.number(),updated_at:e.number(),deleted_at:e.number().nullable(),deleted:e.boolean(),list_item:e.array(e.string()).optional(),status:s.optional(),isActive:e.boolean().optional()});e.object({data:e.array(d),track_id:e.string()});const b=t=>({...t,status:t.active===1?"active":"inactive",isActive:t.active===1}),o=e.object({id:e.string(),item_id:e.string(),item_name:e.string(),ots_price:e.number(),ta_price:e.number(),deleted_at:e.number(),deleted:e.boolean(),deleted_by:e.string(),city_uid:e.string(),store_uid:e.string().optional()});e.object({data:e.array(o),track_id:e.string()});const g=t=>({...t}),_=e.object({id:e.string(),item_type_id:e.string(),item_type_name:e.string(),item_type_parent_id:e.string().nullable(),item_type_color:e.string().nullable(),list_order:e.number().nullable(),is_material:e.number().nullable(),print_name_menu:e.string().nullable(),image_path:e.string().nullable(),description:e.string().nullable(),sort:e.number(),sort_online:e.number(),extra_data:e.any().nullable(),active:e.number(),revision:e.number(),store_uid:e.string().nullable(),brand_uid:e.string(),company_uid:e.string(),is_fabi:e.number(),created_by:e.string().nullable(),updated_by:e.string().nullable(),deleted_by:e.string().nullable(),created_at:e.number(),updated_at:e.number(),deleted_at:e.number().nullable(),deleted:e.boolean(),list_item:e.array(e.string()).optional()});e.object({data:e.array(_),track_id:e.string()});const y=t=>({...t}),c=e.object({id:e.string(),created_at:e.number(),created_by:e.string(),updated_at:e.number(),updated_by:e.string(),deleted:e.boolean(),deleted_at:e.number().nullable(),deleted_by:e.string().nullable(),source_id:e.string(),source_name:e.string(),source_type:e.array(e.string()),description:e.string().nullable(),extra_data:e.object({commission:e.number(),exclude_ship:e.number(),payment_type:e.string(),deduct_tax_rate:e.number(),require_tran_no:e.number(),use_order_online:e.number(),payment_method_id:e.string(),payment_method_name:e.string(),voucher_run_partner:e.string().nullable(),not_show_partner_bill:e.number(),marketing_partner_cost:e.number(),marketing_partner_cost_type:e.string(),marketing_partner_cost_to_date:e.number(),marketing_partner_cost_hour_day:e.number(),marketing_partner_cost_date_week:e.number(),marketing_partner_cost_from_date:e.number()}),is_fb:e.number(),active:e.number(),revision:e.number().nullable(),brand_uid:e.string(),company_uid:e.string(),sort:e.number(),is_fabi:e.number(),store_uid:e.string(),partner_config:e.number(),stores:e.object({id:e.string(),created_at:e.number(),created_by:e.string(),updated_at:e.number(),updated_by:e.string(),deleted:e.boolean(),deleted_at:e.number().nullable(),deleted_by:e.string().nullable(),store_id:e.string(),fb_store_id:e.number(),store_name:e.string(),is_delivery_direct:e.number(),email:e.string(),phone:e.string(),logo:e.string(),background:e.string(),facebook:e.string(),website:e.string(),address:e.string(),description:e.string(),workstation_id:e.number(),active:e.number(),is_default:e.number(),is_test:e.number(),extra_data:e.any(),revision:e.number(),city_uid:e.string(),pos_server_group_uid:e.string().nullable(),brand_uid:e.string(),company_uid:e.string(),latitude:e.number(),longitude:e.number(),sort:e.number(),store_address:e.any(),last_synced_transaction:e.string().nullable(),delivery_services:e.string(),phone_manager:e.string(),is_ahamove_active:e.number(),expiry_date:e.number(),is_fabi:e.number(),email_delivery_service:e.string(),operation_form:e.string().nullable(),partner:e.string(),pos_type:e.string().nullable(),size:e.number(),district:e.string().nullable(),currency:e.string(),ward:e.string().nullable()})});e.object({data:e.array(c),track_id:e.string().optional()});const p=t=>({...t}),l=r.create({baseURL:"https://api.vietqr.io",timeout:3e4,headers:{accept:"application/json, text/plain, */*","accept-language":"vi",access_token:"5c885b2ef8c34fb7b1d1fad11eef7bec",authorization:"","cache-control":"no-cache",fabi_type:"pos-cms",pragma:"no-cache","x-client-timezone":"********"}}),v={getBanks:async()=>(await l.get("/v2/banks")).data,generateQuickLink:async t=>(await new Promise(n=>setTimeout(n,1e3)),{data:{imageURL:`https://img.vietqr.io/image/${t.bank_id}-${t.bank_acc}-compact.png?accountName=${encodeURIComponent(t.bank_acc_name)}`},track_id:`2025-0804-${Date.now()}-mock-test-id`})};export{g as a,b,m as c,p as d,y as e,v};
