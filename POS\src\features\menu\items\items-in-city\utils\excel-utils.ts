import type { ItemClass } from '@/types/item-class'
import ExcelJS from 'exceljs'

import type { ItemType } from '@/lib/item-types-api'
import type { Unit } from '@/lib/units-api'

export const createExcelWorkbook = (): ExcelJS.Workbook => {
  const workbook = new ExcelJS.Workbook()
  workbook.creator = 'POS System'
  workbook.lastModifiedBy = 'POS System'
  workbook.created = new Date()
  workbook.modified = new Date()
  return workbook
}

export const getMenuHeaders = (): string[] => {
  return [
    'ID',
    'Mã món',
    'Thành phố',
    'Tên',
    'Gi<PERSON>',
    'Trạng thái',
    'Mã barcode',
    'Món ăn kèm',
    '<PERSON>hông cập nhật số lượng món ăn kèm',
    'Đơn vị',
    'Nhóm',
    'Tên nhóm',
    '<PERSON><PERSON>i món',
    'Tên loại',
    '<PERSON><PERSON> tả',
    'SKU',
    'VAT (%)',
    'Thời gian chế biến (phút)',
    '<PERSON> phép sửa giá khi bán',
    '<PERSON><PERSON><PERSON> hình món ảo',
    '<PERSON><PERSON><PERSON> hình món dịch vụ',
    'Cấu hình món ăn là vé buffet',
    'Giờ',
    'Ngày',
    'Thứ tự',
    'Hình ảnh',
    'Công thức inQR cho máy pha trà'
  ]
}

export const createMenuWorksheet = (workbook: ExcelJS.Workbook): ExcelJS.Worksheet => {
  const headers = getMenuHeaders()
  const menuWorksheet = workbook.addWorksheet('Menu')

  // Add header row
  const headerRow = menuWorksheet.addRow(headers)

  // Style header row with blue color as requested
  headerRow.eachCell((cell: any) => {
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF0560A6' } // Blue color
    }
    cell.font = {
      color: { argb: 'FFFFFFFF' },
      bold: true,
      size: 11
    }
    cell.alignment = {
      horizontal: 'center',
      vertical: 'middle'
    }
    cell.border = {
      top: { style: 'thin', color: { argb: 'FF000000' } },
      left: { style: 'thin', color: { argb: 'FF000000' } },
      bottom: { style: 'thin', color: { argb: 'FF000000' } },
      right: { style: 'thin', color: { argb: 'FF000000' } }
    }
  })

  // Set column widths for Menu sheet
  const columnWidths = [36, 15, 12, 25, 12, 12, 15, 15, 35, 12, 15, 12, 15, 12, 25, 15, 12, 25, 25, 20, 25, 35, 12, 12, 12, 25, 35]
  columnWidths.forEach((width, index) => {
    menuWorksheet.getColumn(index + 1).width = width
  })

  return menuWorksheet
}

export const getTemplateHeaders = (): string[] => {
  return [
    'ID',
    'Mã món',
    'Thành phố',
    'Tên',
    'Giá (Mặc định 0)',
    'Trạng thái (Mặc định 1)',
    'Mã barcode (Tối đa 13)',
    'Món ăn kèm (Mặc định 0)',
    'Không cập nhật số lượng món ăn kèm (Mặc định 1)',
    'Đơn vị (Mặc định MON)',
    'Nhóm (Mặc định LOẠI KHÁC)',
    'Tên nhóm',
    'Loại món (Mặc định rỗng)',
    'Tên loại',
    'Mô tả',
    'SKU (Tối đa 50)',
    'VAT (%) (Mặc định 0)',
    'Thời gian chế biến (phút) (Mặc định 0)',
    'Cho phép sửa giá khi bán (Mặc định 0)',
    'Cấu hình món ảo (Mặc định 0)',
    'Cấu hình món dịch vụ (Mặc định 0)',
    'Cấu hình món ăn là vé buffet (Mặc định 0)',
    'Giờ (Mặc định 0)',
    'Ngày (Mặc định 0)',
    'Thứ tự (Mặc định 0)',
    'Hình ảnh',
    'Công thức inQR cho máy pha trà'
  ]
}

export const getExampleData = (): any[][] => {
  return [
    [
      'd9692391-3f3f-4754-9416-878d2d8b52ce',
      'ITEM-3279',
      'Hồ Chí Minh',
      'Cà Phê Sữa (L)',
      0,
      1,
      '',
      0,
      1,
      'AM',
      'ITEM_TYPE_OTHER',
      'Uncategory',
      'DA',
      'Đồ ăn',
      'không',
      '',
      0,
      10,
      14,
      0,
      0,
      1,
      2064384,
      224,
      0,
      'https://image.foodbook.vn/images/20250829/1756431019051-anh-test-may-in-mau-chat-luong-hinh-2.jpg',
      ''
    ],
    [
      '8119567b-f80e-43ac-8c85-012dd8f56b18',
      'KHOAI',
      'Hồ Chí Minh',
      'pomato',
      0,
      1,
      '',
      1,
      1,
      'TRAI',
      'MAK',
      'MÓN ĂN KÈM',
      'MA',
      'manh',
      '',
      '',
      0,
      5,
      14,
      1,
      0,
      1,
      6355002,
      254,
      0,
      'https://image.foodbook.vn/images/20250829/1756450169874-vo_tri_2.jpg',
      ''
    ]
  ]
}

export const getDayReferenceData = (): string[][] => {
  return [
    ['Chủ nhật', '2'],
    ['Thứ 2', '4'],
    ['Thứ 3', '8'],
    ['Thứ 4', '16'],
    ['Thứ 5', '32'],
    ['Thứ 6', '64'],
    ['Thứ 7', '128'],
    ['Ví dụ: CN, T2, T5 = 2 + 4 + 32', '38']
  ]
}

export const getHourReferenceData = (): string[][] => {
  return [
    ['0h', '1'],
    ['1h', '2'],
    ['2h', '4'],
    ['3h', '8'],
    ['4h', '16'],
    ['5h', '32'],
    ['6h', '64'],
    ['7h', '128'],
    ['8h', '256'],
    ['9h', '512'],
    ['10h', '1024'],
    ['11h', '2048'],
    ['12h', '4096'],
    ['13h', '8192'],
    ['14h', '16384'],
    ['15h', '32768'],
    ['16h', '65536'],
    ['17h', '131072'],
    ['18h', '262144'],
    ['19h', '524288'],
    ['20h', '1048576'],
    ['21h', '2097152'],
    ['22h', '4194304'],
    ['23h', '8388608'],
    ['Ví dụ: 0h, 1h, 3h = 1 + 2 + 8', '11']
  ]
}

export const getItemTypesReferenceData = (itemTypes: ItemType[]): string[][] => {
  const data = itemTypes
    .filter(itemType => itemType.active === 1)
    .map(itemType => [itemType.item_type_id, itemType.item_type_name])

  return data.length > 0 ? data : [['Không có dữ liệu', '']]
}

export const getItemClassesReferenceData = (itemClasses: ItemClass[]): string[][] => {
  const data = itemClasses
    .filter(itemClass => itemClass.active === 1)
    .map(itemClass => [itemClass.item_class_id, itemClass.item_class_name])

  return data.length > 0 ? data : [['Không có dữ liệu', '']]
}

export const getUnitsReferenceData = (units: Unit[]): string[][] => {
  if (!units || units.length === 0) {
    return [['Không có dữ liệu', '']]
  }

  const data = units.map(unit => [unit.unit_id, unit.unit_name])
  return data.length > 0 ? data : [['Không có dữ liệu', '']]
}

export const createTemplateWorksheet = (
  workbook: ExcelJS.Workbook,
  apiData?: {
    itemTypes?: ItemType[]
    itemClasses?: ItemClass[]
    units?: Unit[]
  }
): ExcelJS.Worksheet => {
  const headers = getTemplateHeaders()
  const templateWorksheet = workbook.addWorksheet('Template')

  // Add instruction row
  const instructionRow = templateWorksheet.addRow([
    'Đây là sheet mẫu để tham khảo. Vui lòng quay lại sheet "Menu" để nhập dữ liệu.'
  ])

  // Merge cells for instruction row (from column A to AA - 27 columns total)
  templateWorksheet.mergeCells(`A${instructionRow.number}:AA${instructionRow.number}`)

  // Style instruction row with dark blue background and white text
  instructionRow.getCell(1).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FF0560A6' } // Dark blue
  }
  instructionRow.getCell(1).font = {
    color: { argb: 'FFFFFFFF' }, // White text
    bold: true,
    size: 11
  }
  instructionRow.getCell(1).alignment = {
    horizontal: 'left',
    vertical: 'middle'
  }
  instructionRow.getCell(1).border = {
    top: { style: 'thin', color: { argb: 'FF000000' } },
    left: { style: 'thin', color: { argb: 'FF000000' } },
    bottom: { style: 'thin', color: { argb: 'FF000000' } },
    right: { style: 'thin', color: { argb: 'FF000000' } }
  }

  // Add header row for template
  const templateHeaderRow = templateWorksheet.addRow(headers)

  // Style template header row with blue color
  templateHeaderRow.eachCell((cell: any) => {
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF0560A6' } // Blue color as requested
    }
    cell.font = {
      color: { argb: 'FFFFFFFF' },
      bold: true,
      size: 11
    }
    cell.alignment = { horizontal: 'center', vertical: 'middle' }
    cell.border = {
      top: { style: 'thin', color: { argb: 'FF000000' } },
      left: { style: 'thin', color: { argb: 'FF000000' } },
      bottom: { style: 'thin', color: { argb: 'FF000000' } },
      right: { style: 'thin', color: { argb: 'FF000000' } }
    }
  })

  // Add example data rows
  const exampleData = getExampleData()
  exampleData.forEach(rowData => {
    const dataRow = templateWorksheet.addRow(rowData)

    // Style data rows
    dataRow.eachCell((cell: any) => {
      cell.font = { size: 10 }
      cell.border = {
        top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
        left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
        bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
        right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
      }
    })
  })

  // Add empty row and reference tables
  templateWorksheet.addRow([])

  // Add all reference table headers in one row (horizontal layout)
  const referenceHeaderRow = templateWorksheet.addRow([
    'BẢNG THAM CHIẾU NGÀY',
    '',
    '',
    'BẢNG THAM CHIẾU GIỜ',
    '',
    '',
    'BẢNG THAM CHIẾU NHÓM MÓN',
    '',
    '',
    'BẢNG THAM CHIẾU LOẠI MÓN',
    '',
    '',
    'BẢNG THAM CHIẾU ĐƠN VỊ',
    ''
  ])

  // Get current row number for merging
  const currentRowNumber = referenceHeaderRow.number

  // Merge cells for all reference table headers
  templateWorksheet.mergeCells(`A${currentRowNumber}:B${currentRowNumber}`) // NGÀY
  templateWorksheet.mergeCells(`D${currentRowNumber}:E${currentRowNumber}`) // GIỜ
  templateWorksheet.mergeCells(`G${currentRowNumber}:H${currentRowNumber}`) // NHÓM MÓN
  templateWorksheet.mergeCells(`J${currentRowNumber}:K${currentRowNumber}`) // LOẠI MÓN
  templateWorksheet.mergeCells(`M${currentRowNumber}:N${currentRowNumber}`) // ĐƠN VỊ

  // Style reference table headers (dark blue)
  referenceHeaderRow.eachCell((cell: any) => {
    if (cell.value && cell.value.toString().trim() !== '') {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF0560A6' } // Dark blue
      }
      cell.font = {
        color: { argb: 'FFFFFFFF' },
        bold: true,
        size: 11
      }
      cell.alignment = {
        horizontal: 'center',
        vertical: 'middle'
      }
      cell.border = {
        top: { style: 'thin', color: { argb: 'FF000000' } },
        left: { style: 'thin', color: { argb: 'FF000000' } },
        bottom: { style: 'thin', color: { argb: 'FF000000' } },
        right: { style: 'thin', color: { argb: 'FF000000' } }
      }
    }
  })

  // Add column headers with light blue background for all reference tables
  const columnHeaderRow = templateWorksheet.addRow([
    'Thời gian',
    'Giá trị',
    '',
    'Thời gian',
    'Giá trị',
    '',
    'Mã nhóm',
    'Tên nhóm',
    '',
    'Mã loại món',
    'Tên loại món',
    '',
    'Mã đơn vị',
    'Tên đơn vị'
  ])

  // Style column headers (light blue)
  columnHeaderRow.eachCell((cell: any) => {
    if (cell.value && cell.value.toString().trim() !== '') {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF79ABE3' } // Light blue
      }
      cell.font = {
        bold: true,
        size: 10
      }
      cell.alignment = {
        horizontal: 'center',
        vertical: 'middle'
      }
      cell.border = {
        top: { style: 'thin', color: { argb: 'FF000000' } },
        left: { style: 'thin', color: { argb: 'FF000000' } },
        bottom: { style: 'thin', color: { argb: 'FF000000' } },
        right: { style: 'thin', color: { argb: 'FF000000' } }
      }
    }
  })

  // Get all reference data
  const dayReferenceData = getDayReferenceData()
  const hourReferenceData = getHourReferenceData()

  // Get API reference data if available (swap data mapping)
  const itemClassesData = apiData?.itemTypes ? getItemTypesReferenceData(apiData.itemTypes) : [['Không có dữ liệu', '']] // NHÓM MÓN lấy từ Item Types
  const itemTypesData = apiData?.itemClasses
    ? getItemClassesReferenceData(apiData.itemClasses)
    : [['Không có dữ liệu', '']] // LOẠI MÓN lấy từ Item Classes
  const unitsData = apiData?.units ? getUnitsReferenceData(apiData.units) : [['Không có dữ liệu', '']]

  // Combine all reference data into rows (horizontal layout)
  const maxRows = Math.max(
    dayReferenceData.length,
    hourReferenceData.length,
    itemClassesData.length,
    itemTypesData.length,
    unitsData.length
  )
  const referenceData: string[][] = []
  for (let i = 0; i < maxRows; i++) {
    const row = [
      dayReferenceData[i]?.[0] || '', // Day name
      dayReferenceData[i]?.[1] || '', // Day value
      '', // Empty column
      hourReferenceData[i]?.[0] || '', // Hour name
      hourReferenceData[i]?.[1] || '', // Hour value
      '', // Empty column
      itemClassesData[i]?.[0] || '', // NHÓM MÓN ID (from Item Types)
      itemClassesData[i]?.[1] || '', // NHÓM MÓN name (from Item Types)
      '', // Empty column
      itemTypesData[i]?.[0] || '', // LOẠI MÓN ID (from Item Classes)
      itemTypesData[i]?.[1] || '', // LOẠI MÓN name (from Item Classes)
      '', // Empty column
      unitsData[i]?.[0] || '', // Unit ID
      unitsData[i]?.[1] || '' // Unit name
    ]
    referenceData.push(row)
  }

  referenceData.forEach(rowData => {
    const dataRow = templateWorksheet.addRow(rowData)

    // Style reference data rows
    dataRow.eachCell((cell: any) => {
      if (cell.value && cell.value.toString().trim() !== '') {
        cell.font = {
          size: 10
        }
        cell.alignment = {
          horizontal: 'left',
          vertical: 'middle'
        }
        cell.border = {
          top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
          left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
          bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
          right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
        }
      }
    })
  })

  // Set column widths for template sheet
  const columnWidths = [36, 15, 12, 25, 12, 12, 15, 15, 35, 12, 15, 12, 15, 12, 25, 15, 12, 25, 25, 20, 25, 35, 12, 12, 12, 25, 35]
  columnWidths.forEach((width, index) => {
    templateWorksheet.getColumn(index + 1).width = width
  })

  return templateWorksheet
}

export const generateItemsExcelFile = async (
  referenceData?: {
    itemTypes?: ItemType[]
    itemClasses?: ItemClass[]
    units?: Unit[]
  },
  actualData?: any[]
): Promise<void> => {
  try {
    // Create workbook
    const workbook = createExcelWorkbook()

    // Create Menu worksheet
    const menuWorksheet = createMenuWorksheet(workbook)

    // Add actual data to Menu worksheet if provided
    if (actualData && actualData.length > 0) {
      // Helper functions to lookup names from UIDs
      const getItemType = (itemTypeUid: string) => {
        const itemType = referenceData?.itemTypes?.find(it => it.id === itemTypeUid)
        return {
          name: itemType?.item_type_name || itemTypeUid,
          id: itemType?.item_type_id || itemTypeUid
        }
      }

      const getItemClass = (itemClassUid: string) => {
        const itemClass = referenceData?.itemClasses?.find(ic => ic.id === itemClassUid)
        return {
          name: itemClass?.item_class_name || itemClassUid,
          id: itemClass?.item_class_id || itemClassUid
        }
      }

      const getUnit = (unitUid: string) => {
        const unit = referenceData?.units?.find(u => u.id === unitUid)
        return { name: unit?.unit_name || unitUid, id: unit?.unit_id || unitUid }
      }

      actualData.forEach(item => {
        // Get city name from cities array
        const cities = (item.cities as Array<{ city_name: string }>) || []
        const cityName = cities[0]?.city_name || ''

        // Get extra_data for additional fields
        const extraData = (item.extra_data as Record<string, unknown>) || {}

        const rowData = [
          item.id || '',
          item.item_id || '',
          cityName,
          item.item_name || '',
          item.ta_price || 0,
          item.active || 1,
          item.item_id_barcode || '',
          item.is_eat_with || 0,
          extraData.no_update_quantity_toping || 1, 
          getUnit(item.unit_uid).id, 
          getItemType(item.item_type_uid).id, 
          getItemType(item.item_type_uid).name,
          getItemClass(item.item_class_uid).id,
          getItemClass(item.item_class_uid).name,
          item.description || '',
          item.item_id_mapping || '',
          item.ta_tax * 100 || 0,
          Math.round(item.time_cooking / 60000) || 0,
          extraData.enable_edit_price || 0,
          extraData.is_virtual_item || 0, 
          extraData.is_item_service || 0, 
          extraData.is_buffet_item || 0, 
          item.time_sale_hour_day || 0,
          item.time_sale_date_week || 0,
          item.sort,
          item.image_path || '',
          extraData.formula_qrcode || ''
        ]
        menuWorksheet.addRow(rowData)
      })
    }

    // Create template worksheet (with example data and reference tables)
    createTemplateWorksheet(workbook, referenceData)

    // Generate Excel file
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const filename = `items_export_template_${timestamp}.xlsx`

    // Download file
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    return Promise.resolve()
  } catch (error) {
    console.error('Error creating Excel file:', error)
    return Promise.reject(error)
  }
}

// Function to create Excel blob from data for items-in-city
export const createItemsExcelBlob = async (
  data: any[],
  referenceData?: {
    itemTypes?: ItemType[]
    itemClasses?: ItemClass[]
    units?: Unit[]
  }
): Promise<Blob> => {
  try {
    // Create workbook
    const workbook = createExcelWorkbook()

    // Create Menu worksheet with data
    const menuWorksheet = createMenuWorksheet(workbook)

    // Helper functions to lookup names from UIDs
    const getItemType = (itemTypeUid: string) => {
      const itemType = referenceData?.itemTypes?.find(it => it.id === itemTypeUid)
      return {
        name: itemType?.item_type_name || itemTypeUid,
        id: itemType?.item_type_id || itemTypeUid
      }
    }

    const getItemClass = (itemClassUid: string) => {
      const itemClass = referenceData?.itemClasses?.find(ic => ic.id === itemClassUid)
      return {
        name: itemClass?.item_class_name || itemClassUid,
        id: itemClass?.item_class_id || itemClassUid
      }
    }

    const getUnit = (unitUid: string) => {
      const unit = referenceData?.units?.find(u => u.id === unitUid)
      return { name: unit?.unit_name || unitUid, id: unit?.unit_id || unitUid }
    }

    // Add data rows to Menu worksheet
    data.forEach(item => {
      // Get city name from cities array
      const cities = (item.cities as Array<{ city_name: string }>) || []
      const cityName = cities[0]?.city_name || ''

      // Get extra_data for additional fields
      const extraData = (item.extra_data as Record<string, unknown>) || {}

      const rowData = [
        item.id || '',
        item.item_id || '',
        cityName, 
        item.item_name || '',
        item.ta_price || 0, 
        item.active || 1,
        item.item_id_barcode || '',
        item.is_eat_with || 0, 
        extraData.no_update_quantity_toping || 1, 
        getUnit(item.unit_uid).id,
        getItemType(item.item_type_uid).id,
        getItemType(item.item_type_uid).name,
        getItemClass(item.item_class_uid).id,
        getItemClass(item.item_class_uid).name,
        item.description || '',
        item.item_id_mapping || '',
        item.ta_tax * 100 || 0, 
        Math.round(item.time_cooking / 60000) || 0,
        extraData.enable_edit_price || 0,
        extraData.is_virtual_item || 0,
        extraData.is_item_service || 0,
        extraData.is_buffet_item || 0,
        item.time_sale_hour_day || 0,
        item.time_sale_date_week || 0,
        item.sort, 
        item.image_path || '', 
        extraData.formula_qrcode || ''
      ]
      menuWorksheet.addRow(rowData)
    })

    // Generate Excel buffer
    const buffer = await workbook.xlsx.writeBuffer()
    return new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
  } catch (error) {
    console.error('Error creating Excel blob:', error)
    throw error
  }
}
