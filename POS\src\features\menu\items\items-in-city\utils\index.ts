/* eslint-disable @typescript-eslint/no-explicit-any */
import { TimeFrameConfig, PriceTime } from '../hooks/items-in-city-types'

export const generateItemId = () => {
  const numbers = '0123456789'
  const randomNumbers = Array.from({ length: 4 }, () => numbers[Math.floor(Math.random() * numbers.length)]).join('')
  return `ITEM-${randomNumbers}`
}

export const getSourceName = (source_id: string, sources: any[]) => {
  return (sources as any[])?.find(s => s.sourceId === source_id || s.id === source_id)?.sourceName
}

export const getDayLabels = (dayValues: number[]) => {
  const dayMap: { [key: number]: string } = {
    1: 'Thứ 2',
    2: 'Thứ 3',
    4: 'Thứ 4',
    8: 'Thứ 5',
    16: 'Thứ 6',
    32: 'Thứ 7',
    64: '<PERSON><PERSON> nhật'
  }
  return dayValues.map(value => dayMap[value]).join(', ')
}

export const getHourLabels = (hourValues: number[]) => {
  return hourValues.map(hour => `${hour}h`).join(', ')
}

export const getDaysFromBitwise = (bitwiseValue: number): number[] => {
  const days: number[] = []
  if (bitwiseValue & 1) days.push(1)
  if (bitwiseValue & 2) days.push(2)
  if (bitwiseValue & 4) days.push(4)
  if (bitwiseValue & 8) days.push(8)
  if (bitwiseValue & 16) days.push(16)
  if (bitwiseValue & 32) days.push(32)
  if (bitwiseValue & 64) days.push(64)
  return days
}

export const getHoursFromBitwise = (bitwiseValue: number): number[] => {
  const hours: number[] = []
  for (let i = 0; i < 24; i++) {
    if (bitwiseValue & (1 << i)) {
      hours.push(i)
    }
  }
  return hours
}

export const formatDate = (date: Date) => {
  return date.toLocaleDateString('vi-VN')
}

export const convertToPriceTimes = (configs: TimeFrameConfig[]): PriceTime[] => {
  return configs.map(config => {
    const time_sale_date_week = config.selectedDays.reduce((sum, day) => sum | day, 0)
    const time_sale_hour_day = config.selectedHours.reduce((sum, hour) => sum | (1 << hour), 0)

    return {
      price: config.price,
      from_date: config.startDate.getTime(),
      to_date: new Date(config.endDate.getTime() + 24 * 60 * 60 * 1000 - 1).getTime(),
      time_sale_date_week,
      time_sale_hour_day
    }
  })
}

// Export Excel utilities
export {
  createExcelWorkbook,
  getMenuHeaders,
  createMenuWorksheet,
  getTemplateHeaders,
  getImportTemplateHeaders,
  getExampleData,
  getDayReferenceData,
  getHourReferenceData,
  getItemTypesReferenceData,
  getItemClassesReferenceData,
  getUnitsReferenceData,
  createTemplateWorksheet,
  createImportMenuWorksheet,
  createImportTemplateWorksheet,
  generateItemsExcelFile,
  generateImportItemsExcelFile,
  createItemsExcelBlob
} from './excel-utils'
